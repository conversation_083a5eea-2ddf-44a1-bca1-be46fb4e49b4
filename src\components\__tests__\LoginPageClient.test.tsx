import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoginPageClient } from '../LoginPageClient'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

vi.mock('../LoginForm', () => ({
  LoginForm: vi.fn(({ onSuccess }) => (
    <div data-testid="login-form">
      <button onClick={onSuccess} data-testid="mock-login-button">
        Mock Login
      </button>
    </div>
  )),
}))

vi.mock('../auth/QuickSuccessScreen', () => ({
  QuickSuccessScreen: vi.fn(({ onComplete }) => (
    <div data-testid="success-screen">
      <h1>Success Screen</h1>
      <button onClick={onComplete} data-testid="mock-complete-button">
        Complete
      </button>
    </div>
  )),
}))

const mockPush = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

vi.mock('@/hooks/useLoginPrefetch', () => ({
  useLoginPrefetch: () => ({
    startPrefetch: vi.fn(),
  }),
}))

describe('LoginPageClient', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should handle null matchMedia return gracefully', () => {
    // Mock matchMedia to return null
    const originalMatchMedia = window.matchMedia
    window.matchMedia = vi.fn().mockReturnValue(null)

    // Should not throw when rendering
    expect(() => render(<LoginPageClient />)).not.toThrow()

    // Restore original
    window.matchMedia = originalMatchMedia
  })

  it('should render login form initially', () => {
    render(<LoginPageClient />)

    expect(screen.getByTestId('login-form')).toBeInTheDocument()
    expect(screen.queryByTestId('success-screen')).not.toBeInTheDocument()
  })

  it('should render title and subtitle on separate lines', () => {
    render(<LoginPageClient />)

    // Find the header element
    const header = screen.getByRole('heading', { level: 1 })

    // Header should only contain the title
    expect(header.textContent).toBe('Dr. Muscle X')

    // Find the subtitle paragraph
    const subtitle = screen.getByText("World's Fastest AI Personal Trainer")
    expect(subtitle).toBeInTheDocument()
    expect(subtitle.tagName).toBe('P')
  })

  it('should show success screen after login success', async () => {
    render(<LoginPageClient />)

    // Initially shows login form
    expect(screen.getByTestId('login-form')).toBeInTheDocument()

    // Simulate successful login
    await user.click(screen.getByTestId('mock-login-button'))

    // Should now show success screen
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument()
    expect(screen.getByTestId('success-screen')).toBeInTheDocument()
  })

  it('should navigate to program page after success screen completes', async () => {
    render(<LoginPageClient />)

    // Login
    await user.click(screen.getByTestId('mock-login-button'))

    // Complete success screen
    await user.click(screen.getByTestId('mock-complete-button'))

    // Should navigate to program page
    expect(mockPush).toHaveBeenCalledWith('/program')
  })

  it('should navigate to custom return URL if provided', async () => {
    render(<LoginPageClient returnUrl="/workout/exercise/123" />)

    // Login
    await user.click(screen.getByTestId('mock-login-button'))

    // Complete success screen
    await user.click(screen.getByTestId('mock-complete-button'))

    // Should navigate to custom URL
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
  })

  it('should render footer links', () => {
    render(<LoginPageClient />)

    expect(screen.getByText("Don't have an account?")).toBeInTheDocument()
    const signUpLink = screen.getByText('Sign up')
    expect(signUpLink).toBeInTheDocument()
    expect(signUpLink).toHaveAttribute('href', '/register')
  })

  it('should use theme-aware colors for footer text', () => {
    render(<LoginPageClient />)

    const footerParagraph = screen
      .getByText("Don't have an account?")
      .closest('p')
    const signUpLink = screen.getByText('Sign up')

    // Check that footer text uses theme secondary text color
    expect(footerParagraph).toHaveClass('text-text-secondary')

    // Check that sign up link uses theme brand primary color
    expect(signUpLink).toHaveClass('text-brand-primary')
    expect(signUpLink).toHaveClass('hover:text-brand-secondary')
  })

  it('should have proper PWA safe areas', () => {
    const { container } = render(<LoginPageClient />)

    const safeAreaTop = container.querySelector('.safe-area-top')
    const safeAreaBottom = container.querySelector('.safe-area-bottom')

    expect(safeAreaTop).toBeInTheDocument()
    expect(safeAreaBottom).toBeInTheDocument()
  })

  it('should maintain state through component lifecycle', async () => {
    const { rerender } = render(<LoginPageClient />)

    // Login
    await user.click(screen.getByTestId('mock-login-button'))

    // Should show success screen
    expect(screen.getByTestId('success-screen')).toBeInTheDocument()

    // Re-render component
    rerender(<LoginPageClient />)

    // Should still show success screen
    expect(screen.getByTestId('success-screen')).toBeInTheDocument()
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument()
  })
})
