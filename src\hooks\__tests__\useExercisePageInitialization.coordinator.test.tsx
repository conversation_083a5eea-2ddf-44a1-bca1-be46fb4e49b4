import { renderHook, waitFor, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'next/navigation'

vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  }),
}))

describe('useExercisePageInitialization - RecommendationLoadingCoordinator Integration', () => {
  const mockRouter = {
    replace: vi.fn(),
  }

  const mockUseWorkout = {
    todaysWorkout: null,
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [],
    workoutSession: null,
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockUseWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  const mockUseAuthStore = {
    token: 'valid-token',
    isAuthenticated: true,
  }

  let coordinator: RecommendationLoadingCoordinator

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkout).mockReturnValue(mockUseWorkout as any)
    vi.mocked(useWorkoutStore).mockReturnValue(mockUseWorkoutStore as any)
    vi.mocked(useAuthStore).mockReturnValue(mockUseAuthStore as any)

    // Reset coordinator singleton
    RecommendationLoadingCoordinator['instance'] = null
    coordinator = RecommendationLoadingCoordinator.getInstance()
  })

  afterEach(() => {
    // Clean up coordinator
    coordinator.reset()
  })

  it('should check coordinator before loading recommendations', async () => {
    const exerciseId = 123
    const canStartLoadingSpy = vi.spyOn(coordinator, 'canStartLoading')
    const startLoadingSpy = vi.spyOn(coordinator, 'startLoading')

    // Setup workout with exercise - need to mock the return value properly
    vi.mocked(useWorkout).mockReturnValue({
      ...mockUseWorkout,
      workoutSession: { id: 'test-session' },
      exercises: [{ Id: exerciseId, Label: 'Test Exercise', sets: [] }],
    } as any)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Verify coordinator was checked and called with timeout
    expect(canStartLoadingSpy).toHaveBeenCalledWith(exerciseId)
    expect(startLoadingSpy).toHaveBeenCalledWith(exerciseId, { timeout: 30000 })
    expect(mockUseWorkout.loadRecommendation).toHaveBeenCalledWith(
      exerciseId,
      'Test Exercise'
    )
  })

  it('should not load recommendations if coordinator indicates loading is in progress', async () => {
    const exerciseId = 123

    // Mark exercise as already loading in coordinator
    coordinator.startLoading(exerciseId)

    // Setup workout with exercise
    mockUseWorkout.workoutSession = { id: 'test-session' }
    mockUseWorkout.exercises = [
      { Id: exerciseId, Label: 'Test Exercise', sets: [] },
    ]

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Verify loadRecommendation was NOT called because coordinator blocked it
    expect(mockUseWorkout.loadRecommendation).not.toHaveBeenCalled()
  })

  it('should update coordinator when starting recommendation loading', async () => {
    const exerciseId = 456
    const startLoadingSpy = vi.spyOn(coordinator, 'startLoading')

    // Setup workout with exercise - need to mock the return value properly
    vi.mocked(useWorkout).mockReturnValue({
      ...mockUseWorkout,
      workoutSession: { id: 'test-session' },
      exercises: [{ Id: exerciseId, Label: 'Test Exercise', sets: [] }],
    } as any)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Verify coordinator was updated with timeout
    expect(startLoadingSpy).toHaveBeenCalledWith(exerciseId, { timeout: 30000 })
  })

  it('should handle coordinator errors gracefully', async () => {
    const exerciseId = 789

    // Make coordinator throw an error
    vi.spyOn(coordinator, 'canStartLoading').mockImplementation(() => {
      throw new Error('Coordinator error')
    })

    // Setup workout with exercise - need to mock the return value properly
    vi.mocked(useWorkout).mockReturnValue({
      ...mockUseWorkout,
      workoutSession: { id: 'test-session' },
      exercises: [{ Id: exerciseId, Label: 'Test Exercise', sets: [] }],
    } as any)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Should proceed with loading despite coordinator error
    expect(mockUseWorkout.loadRecommendation).toHaveBeenCalledWith(
      exerciseId,
      'Test Exercise'
    )
  })

  it('should work correctly with retryInitialization', async () => {
    const exerciseId = 999
    const canStartLoadingSpy = vi.spyOn(coordinator, 'canStartLoading')
    const startLoadingSpy = vi.spyOn(coordinator, 'startLoading')

    // Setup workout with exercise - need to mock the return value properly
    vi.mocked(useWorkout).mockReturnValue({
      ...mockUseWorkout,
      workoutSession: { id: 'test-session' },
      exercises: [{ Id: exerciseId, Label: 'Test Exercise', sets: [] }],
    } as any)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result.current.isInitializing).toBe(false)
    })

    // Reset mocks before retry, but keep the workout data
    mockUseWorkout.loadRecommendation.mockClear()
    mockUseWorkoutStore.setCurrentExerciseById.mockClear()
    mockUseWorkoutStore.getCachedExerciseRecommendation.mockClear()
    canStartLoadingSpy.mockClear()
    startLoadingSpy.mockClear()

    // Reset coordinator state to allow retry
    coordinator.completeLoading(exerciseId)

    // Call retry
    await act(async () => {
      await result.current.retryInitialization()
    })

    // Verify coordinator was used in retry with timeout
    expect(canStartLoadingSpy).toHaveBeenCalledWith(exerciseId)
    expect(startLoadingSpy).toHaveBeenCalledWith(exerciseId, { timeout: 30000 })
  })

  it('should respect coordinator state across multiple hook instances', async () => {
    const exerciseId = 111

    // Start loading in coordinator
    coordinator.startLoading(exerciseId)

    // Setup workout with exercise
    mockUseWorkout.workoutSession = { id: 'test-session' }
    mockUseWorkout.exercises = [
      { Id: exerciseId, Label: 'Test Exercise', sets: [] },
    ]

    // Render multiple instances of the hook
    const { result: result1 } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )
    const { result: result2 } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(() => {
      expect(result1.current.isInitializing).toBe(false)
      expect(result2.current.isInitializing).toBe(false)
    })

    // Neither instance should have triggered loading
    expect(mockUseWorkout.loadRecommendation).not.toHaveBeenCalled()
  })
})
