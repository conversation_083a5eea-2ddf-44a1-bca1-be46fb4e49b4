import { test, expect } from '@playwright/test'

test.describe('1RM Progression Display', () => {
  test('should display 1RM progression with 2 decimal places', async ({
    page,
  }) => {
    // Mock the API responses
    await page.route('**/api/Auth/Login', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          access_token: 'test-token',
          user: { Email: '<EMAIL>' },
        },
      })
    })

    await page.route('**/api/Exercise/GetTodayProgram*', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          TodayProgram: {
            Exercises: [
              {
                Id: 1,
                ExerciseName: 'Bench Press',
                MuscleGroup: 'Chest',
                Equipment: 'Barbell',
              },
            ],
          },
        },
      })
    })

    await page.route('**/api/Exercise/GetRecommendation*', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          ExerciseId: 1,
          Series: 3,
          Reps: 10,
          Weight: { Lb: 135, Kg: 61.2 },
          WarmupsCount: 2,
          FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
          FirstWorkSetReps: 10,
          FirstWorkSetWeight: { Lb: 120, Kg: 54.4 },
        },
      })
    })

    // Navigate to login and perform login
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Navigate to exercise page
    await page.goto('/workout/exercise/1')

    // Look for 1RM progression display with 2 decimal places
    // The component should show values like "+12.34%" or "-5.67%"
    const progressText = await page
      .locator('text=/1RM Progress: [+-]?\\d+\\.\\d{2}%/')
      .textContent()

    // Verify it has exactly 2 decimal places
    expect(progressText).toMatch(/1RM Progress: [+-]?\d+\.\d{2}%/)

    // Also check for the Today: format in SetMetricsDisplay
    const todayText = await page
      .locator('text=/Today: [+-]?\\d+\\.\\d{2}%/')
      .textContent()
    if (todayText) {
      expect(todayText).toMatch(/Today: [+-]?\d+\.\d{2}%/)
    }
  })
})
