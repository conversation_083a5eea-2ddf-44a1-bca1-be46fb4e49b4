import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { WeightInput } from '../WeightInput'

const defaultProps = {
  weight: 100,
  unit: 'lbs' as const,
  onChange: vi.fn(),
  onIncrement: vi.fn(),
  onDecrement: vi.fn(),
}

describe('WeightInput - Dynamic Text Sizing', () => {
  // Test dynamic text sizing based on weight value length
  it('should use largest text size (text-7xl) for small whole numbers (1-99)', () => {
    render(<WeightInput {...defaultProps} weight={35} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-7xl')
  })

  it('should use medium text size (text-6xl) for 3-digit whole numbers (100-999)', () => {
    render(<WeightInput {...defaultProps} weight={150} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-6xl')
  })

  it('should use smaller text size (text-5xl) for numbers with decimals', () => {
    render(<WeightInput {...defaultProps} weight={17.5} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
  })

  it('should use text-5xl for decimal numbers like 17.50', () => {
    render(<WeightInput {...defaultProps} weight={17.5} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
  })

  it('should use smallest text size (text-4xl) for very large numbers with decimals', () => {
    render(<WeightInput {...defaultProps} weight={999.99} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-4xl')
  })

  // Edge cases
  it('should handle zero weight with largest size', () => {
    render(<WeightInput {...defaultProps} weight={0} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-7xl')
  })

  it('should handle very small decimals appropriately', () => {
    render(<WeightInput {...defaultProps} weight={0.5} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
  })

  it('should handle very small decimals like 0.25', () => {
    render(<WeightInput {...defaultProps} weight={0.25} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
  })

  // Test that text size changes dynamically when weight changes
  it('should update text size when weight value changes', () => {
    const { rerender } = render(<WeightInput {...defaultProps} weight={50} />)

    let input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-7xl')

    // Change to decimal value
    rerender(<WeightInput {...defaultProps} weight={50.25} />)
    input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')

    // Change back to whole number
    rerender(<WeightInput {...defaultProps} weight={150} />)
    input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-6xl')
  })

  // Test with different units
  it('should apply same sizing logic for kg units', () => {
    render(<WeightInput {...defaultProps} weight={17.5} unit="kg" />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
  })

  // Test that other classes remain intact
  it('should maintain other styling classes when applying dynamic size', () => {
    render(<WeightInput {...defaultProps} weight={17.5} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-5xl')
    expect(input).toHaveClass('font-bold')
    expect(input).toHaveClass('text-white')
    expect(input).toHaveClass('text-center')
    expect(input).toHaveClass('bg-transparent')
  })

  // Test for proper width adjustment
  it('should maintain consistent width with dynamic sizing', () => {
    render(<WeightInput {...defaultProps} weight={17.5} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('w-36') // Should maintain the same width
  })
})
