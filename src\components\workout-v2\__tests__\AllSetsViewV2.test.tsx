import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AllSetsViewV2 } from '../AllSetsViewV2'
import type { WorkoutLogSerieModel } from '@/types'

// Mock data for testing

const mockAllSets: WorkoutLogSerieModel[] = [
  // Warmup sets
  {
    Id: -1000,
    SetNo: '1',
    IsWarmups: true,
    IsFinished: false,
    IsNext: true,
    WarmUpReps: 5,
    WarmUpWeightSet: { Lb: 95, Kg: 43 },
    Weight: { Lb: 0, Kg: 0 },
    Reps: 0,
  },
  {
    Id: -1001,
    SetNo: '2',
    IsWarmups: true,
    IsFinished: false,
    IsNext: false,
    WarmUpReps: 5,
    WarmUpWeightSet: { Lb: 115, Kg: 52 },
    Weight: { Lb: 0, Kg: 0 },
    Reps: 0,
  },
  // Work sets
  {
    Id: -2000,
    SetNo: '3',
    IsWarmups: false,
    IsFinished: false,
    IsNext: false,
    Reps: 8,
    Weight: { Lb: 135, Kg: 61 },
  },
  {
    Id: -2001,
    SetNo: '4',
    IsWarmups: false,
    IsFinished: false,
    IsNext: false,
    Reps: 8,
    Weight: { Lb: 135, Kg: 61 },
  },
]

describe('AllSetsViewV2', () => {
  it('should render grid headers', () => {
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for header cells
    expect(screen.getByText('SET')).toBeInTheDocument()
    expect(screen.getByText('REPS')).toBeInTheDocument()
    expect(screen.getByText('*')).toBeInTheDocument()
    expect(screen.getByText('LBS')).toBeInTheDocument()
  })

  it('should display all warmup sets with W indicator', () => {
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for warmup set indicators - now numbered
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('W2')).toBeInTheDocument()
  })

  it('should display numbered warmup sets (W1, W2, etc)', () => {
    // Test rationale: Warm-up sets should be numbered to help users track which warm-up they're on
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Should display W1 for first warmup
    expect(screen.getByText('W1')).toBeInTheDocument()
    // Should display W2 for second warmup
    expect(screen.getByText('W2')).toBeInTheDocument()
    // Should NOT display just 'W' anymore
    expect(screen.queryAllByText('W')).toHaveLength(0)
  })

  it('should display single warmup set as W1', () => {
    // Test rationale: Even with a single warm-up, it should show W1 for consistency
    const singleWarmupSet = [mockAllSets[0]]

    render(
      <AllSetsViewV2
        allSets={singleWarmupSet}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Should display W1 even for single warmup
    expect(screen.getByText('W1')).toBeInTheDocument()
  })

  it('should handle multiple warmup sets with proper numbering', () => {
    // Test rationale: Verify numbering works with many warm-up sets
    const manyWarmupSets = [
      { ...mockAllSets[0], Id: -1000, SetNo: '1' },
      { ...mockAllSets[1], Id: -1001, SetNo: '2' },
      {
        Id: -1002,
        SetNo: '3',
        IsWarmups: true,
        IsFinished: false,
        IsNext: false,
        WarmUpReps: 5,
        WarmUpWeightSet: { Lb: 125, Kg: 57 },
        Weight: { Lb: 0, Kg: 0 },
        Reps: 0,
      },
    ]

    render(
      <AllSetsViewV2
        allSets={manyWarmupSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Should display W1, W2, W3
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('W2')).toBeInTheDocument()
    expect(screen.getByText('W3')).toBeInTheDocument()
  })

  it('should display work sets with proper numbering', () => {
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        currentSetIndex={2}
        setData={{ reps: 8, weight: 135, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for work set numbers
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
  })

  it('should show arrow buttons only for current set', async () => {
    const onSetDataChange = vi.fn()
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={onSetDataChange}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Arrow buttons should exist for the current set (first warmup)
    const incrementButtons = screen.getAllByRole('button', {
      name: /increment/i,
    })
    const decrementButtons = screen.getAllByRole('button', {
      name: /decrement/i,
    })

    // Should have 2 increment and 2 decrement buttons (reps and weight)
    expect(incrementButtons).toHaveLength(2)
    expect(decrementButtons).toHaveLength(2)

    // Click increment weight button
    await userEvent.click(incrementButtons[1])
    expect(onSetDataChange).toHaveBeenCalledWith({
      reps: 5,
      weight: 97.5, // 95 + 2.5 (lbs increment)
      duration: 0,
    })
  })

  it('should display completed sets with check marks', () => {
    const setsWithCompleted = [
      ...mockAllSets.slice(0, 2).map((set) => ({ ...set, IsFinished: true })),
      ...mockAllSets.slice(2),
    ]

    render(
      <AllSetsViewV2
        allSets={setsWithCompleted}
        currentSetIndex={2}
        setData={{ reps: 8, weight: 135, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for completed indicators (✓)
    const checkMarks = screen.getAllByText('✓')
    expect(checkMarks).toHaveLength(2)
  })

  it('should show save button under current set', () => {
    const onComplete = vi.fn()
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={onComplete}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Should show save button
    const saveButton = screen.getByRole('button', { name: /save set/i })
    expect(saveButton).toBeInTheDocument()
  })

  it('should handle rest-pause sets correctly', () => {
    const restPauseSets: WorkoutLogSerieModel[] = [
      {
        Id: -2000,
        SetNo: '1',
        IsWarmups: false,
        IsFinished: false,
        IsNext: true,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61 },
        SetTitle: '1st work set',
      },
      {
        Id: -2001,
        SetNo: '2',
        IsWarmups: false,
        IsFinished: false,
        IsNext: false,
        Reps: 5,
        Weight: { Lb: 135, Kg: 61 },
        SetTitle: "All right! Now let's try:",
        IsRestPause: true,
      },
    ]

    render(
      <AllSetsViewV2
        allSets={restPauseSets}
        currentSetIndex={0}
        setData={{ reps: 10, weight: 135, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for rest-pause indicator
    expect(screen.getByText('Rest-pause')).toBeInTheDocument()
  })

  it('should switch between kg and lbs units', () => {
    const { rerender } = render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 43, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="kg"
      />
    )

    // Check for KG header
    expect(screen.getByText('KG')).toBeInTheDocument()

    // Rerender with lbs
    rerender(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Check for LBS header
    expect(screen.getByText('LBS')).toBeInTheDocument()
  })

  it('should highlight the current set', () => {
    render(
      <AllSetsViewV2
        allSets={mockAllSets}
        setData={{ reps: 5, weight: 95, duration: 0 }}
        onSetDataChange={vi.fn()}
        onComplete={vi.fn()}
        onSkip={vi.fn()}
        isSaving={false}
        unit="lbs"
      />
    )

    // Find the row containing the current set (first warmup)
    const currentSetRow = screen.getByTestId('set-row-0')
    expect(currentSetRow).toHaveClass('bg-brand-primary/10')
  })
})
