#!/usr/bin/env node

/**
 * Test script to validate CI fixes
 * This script tests the main components that were causing CI failures
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Testing CI fixes...\n');

// Test 1: Check if the async component test passes
console.log('1. Testing async component fix...');
const testResult = spawn('npm', ['run', 'test', 'src/app/workout/exercise/[id]/__tests__/page.navigation.test.tsx'], {
  stdio: 'pipe',
  shell: true
});

testResult.stdout.on('data', (data) => {
  console.log(`   ${data.toString().trim()}`);
});

testResult.stderr.on('data', (data) => {
  console.error(`   ERROR: ${data.toString().trim()}`);
});

testResult.on('close', (code) => {
  if (code === 0) {
    console.log('   ✅ Async component test passed\n');
  } else {
    console.log('   ❌ Async component test failed\n');
  }
  
  // Test 2: Check memory settings
  console.log('2. Testing memory settings...');
  const nodeOptions = process.env.NODE_OPTIONS || '';
  if (nodeOptions.includes('max_old_space_size')) {
    console.log('   ✅ Memory settings configured');
  } else {
    console.log('   ⚠️  Memory settings not found in NODE_OPTIONS');
  }
  
  // Test 3: Check if CI workflow file exists and has our fixes
  console.log('\n3. Validating CI workflow fixes...');
  const workflowPath = '.github/workflows/ci-optimized.yml';
  if (fs.existsSync(workflowPath)) {
    const workflowContent = fs.readFileSync(workflowPath, 'utf8');
    
    const checks = [
      { name: 'WebKit system preparation', pattern: /Preparing macOS system for WebKit/ },
      { name: 'Memory optimization', pattern: /max_old_space_size=12288/ },
      { name: 'Server startup', pattern: /Starting development server/ },
      { name: 'WebKit fallback', pattern: /attempting fallback to Chromium/ }
    ];
    
    checks.forEach(check => {
      if (check.pattern.test(workflowContent)) {
        console.log(`   ✅ ${check.name} fix applied`);
      } else {
        console.log(`   ❌ ${check.name} fix missing`);
      }
    });
  } else {
    console.log('   ❌ CI workflow file not found');
  }
  
  console.log('\n🎉 CI fixes validation completed!');
  console.log('\nNext steps:');
  console.log('1. Commit these changes');
  console.log('2. Push to trigger CI pipeline');
  console.log('3. Monitor the CI results for improvements');
});
