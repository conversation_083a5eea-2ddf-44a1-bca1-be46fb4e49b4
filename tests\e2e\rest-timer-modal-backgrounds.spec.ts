import { test, expect } from '@playwright/test'

test.describe('Rest Timer Modal Backgrounds', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authenticated state
    await page.route('**/api/user', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: 1,
          Email: '<EMAIL>',
          Name: 'Test User',
          MassUnit: 'kg',
        },
      })
    })

    // Mock workout data
    await page.route('**/api/GetWorkoutInfo', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: 1,
          Label: 'Day A',
          RepsUnit: 'reps',
          MassUnit: 'kg',
          Started: new Date().toISOString(),
          ProgramType: 'NormalProgram',
          ExerciseCount: 3,
          Exercises: [
            {
              Id: 1,
              ExerciseTypeId: 1,
              Label: 'Bench Press',
              RepsUnit: 'reps',
              AvgTimeInMin: 5,
              Path: '',
              RecommendationInKg: 50,
            },
          ],
        },
      })
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-v2"]')
  })

  test('should have consistent glassmorphism style on duration picker', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Click duration setting button
    await page.click('[data-testid="duration-setting-button"]')

    // Wait for duration picker
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Check backdrop has blur effect
    const backdrop = await page.locator('.backdrop-blur-sm').first()
    await expect(backdrop).toBeVisible()

    // Check picker has glassmorphism style
    const picker = page.locator('[data-testid="duration-picker"]')
    const pickerClasses = await picker.getAttribute('class')
    expect(pickerClasses).toContain('bg-surface-primary/90')
    expect(pickerClasses).toContain('backdrop-blur-md')
    expect(pickerClasses).toContain('border-surface-tertiary')
  })

  test('should have consistent glassmorphism style on custom duration modal', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Click duration setting button
    await page.click('[data-testid="duration-setting-button"]')

    // Wait for duration picker
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Click custom option
    await page.click('[data-testid="duration-option-custom"]')

    // Wait for custom modal
    await page.waitForSelector('[data-testid="custom-duration-modal"]')

    // Check backdrop has blur effect
    const backdrop = await page.locator('.backdrop-blur-sm').nth(1)
    await expect(backdrop).toBeVisible()

    // Check modal has glassmorphism style
    const modal = page.locator('[data-testid="custom-duration-modal"]')
    const modalClasses = await modal.getAttribute('class')
    expect(modalClasses).toContain('bg-surface-primary/95')
    expect(modalClasses).toContain('backdrop-blur-md')
    expect(modalClasses).toContain('border-surface-tertiary')
  })

  test('should maintain visual consistency between all modals', async ({
    page,
  }) => {
    // Start a set to trigger rest timer
    await page.click('[data-testid="save-set-button"]')

    // Wait for rest timer
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Check rest timer has blurred background
    const restTimer = page.locator('[data-testid="rest-timer-container"]')
    const restTimerClasses = await restTimer.getAttribute('class')
    expect(restTimerClasses).toContain('bg-surface-primary/90')
    expect(restTimerClasses).toContain('backdrop-blur-sm')

    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await page.waitForSelector('[data-testid="duration-picker"]')

    // Both should have similar glassmorphism styling
    const picker = page.locator('[data-testid="duration-picker"]')
    const pickerClasses = await picker.getAttribute('class')

    // All use surface-primary background with transparency
    expect(restTimerClasses).toContain('bg-surface-primary')
    expect(pickerClasses).toContain('bg-surface-primary')

    // All use backdrop blur
    expect(restTimerClasses).toContain('backdrop-blur')
    expect(pickerClasses).toContain('backdrop-blur')
  })
})
