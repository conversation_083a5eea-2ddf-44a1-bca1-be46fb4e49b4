+# Warmup Calculation Implementation Guide

- +This guide provides comprehensive instructions for implementing local warmup calculations in the Dr. Muscle web app, based on the MAUI mobile app's logic.
- +## Table of Contents
  +1. [Overview](#overview)
  +2. [Key Findings](#key-findings)
  +3. [API Response Structure](#api-response-structure)
  +4. [Exercise Types](#exercise-types)
  +5. [Warmup Calculation Algorithm](#warmup-calculation-algorithm)
  +6. [Equipment-Specific Weight Rounding](#equipment-specific-weight-rounding)
  +7. [Complete Implementation](#complete-implementation)
  +8. [Testing Scenarios](#testing-scenarios)
- +## Overview
- +The Dr. Muscle app calculates warmup sets locally when the API returns a `WarmupsCount` but an empty `WarmUpsList`. This allows for dynamic warmup generation based on user equipment and settings.
- +## Key Findings
- +1. **Set styles do NOT affect warmup calculations** - Normal, rest-pause, pyramid, reverse pyramid, drop sets all use the same warmup calculation
  +2. **Exercise type DOES affect warmup calculations** - Bodyweight, weighted, and assisted exercises have different calculation methods
  +3. **First warmup for weighted bodyweight exercises (IsWeighted = true) is always 0 kg** (bodyweight only) or the minimum weight
  +4. **Equipment type affects weight rounding** - Plates, dumbbells, bands, and cables each have specific rounding logic
- +## API Response Structure
- +```typescript
  +interface RecommendationModel {
- WarmupsCount: number; // Number of warmup sets to generate
- WarmUpsList: WarmUp[]; // Empty array - calculate locally
- Weight: MultiUnityWeight; // Working set weight
- Reps: number; // Working set reps
- Increments?: MultiUnityWeight; // Weight increments
- Min?: MultiUnityWeight; // Minimum weight
- Max?: MultiUnityWeight; // Maximum weight
-
- // Equipment flags
- isPlateAvailable: boolean; // Barbell with plates
- isDumbbellAvailable: boolean;
- isBandsAvailable: boolean;
- isPulleyAvailable: boolean;
-
- // Exercise type flags
- IsBodyweight: boolean;
- IsWeighted: boolean;
- IsAssisted: boolean;
-
- // Set style flags (don't affect warmups)
- IsNormalSets: boolean;
- IsPyramid: boolean;
- IsReversePyramid: boolean;
- IsDropSet: boolean;
- IsMedium: boolean;
- IsEasy: boolean;
  +}
  +```
- +## Exercise Types
- +### 1. Bodyweight Exercises
  +- Examples: Push-ups, pull-ups, dips, bodyweight squats
  +- Weight stays constant (user's bodyweight)
  +- Only reps vary in warmup sets
- +### 2. Weighted Exercises
  +- Examples: Bench press, squats, curls
  +- Weight progresses from 50% to 85% of working weight
  +- Reps decrease as weight increases
- +### 3. Weighted Bodyweight Exercises (IsWeighted = true)
  +- Examples: Weighted pull-ups, weighted dips, weighted crunches
  +- These are bodyweight exercises performed with additional weight
  +- First warmup is overridden to 0 kg (bodyweight only) or minimum weight
  +- The `IsWeighted` flag identifies these hybrid exercises
- +### 4. Assisted Exercises
  +- Examples: Assisted pull-ups, assisted dips
  +- Weight represents assistance (reduction from bodyweight)
  +- Calculated differently than regular weighted exercises
- +## Warmup Calculation Algorithm
- +### Core Algorithm Parameters
- +```typescript
  +interface WarmupCalculationConfig {
- warmupsCount: number;
- workingWeight: MultiUnityWeight;
- workingReps: number;
- incrementValue: number;
- minWeight?: number;
- maxWeight?: number;
- isPlateAvailable: boolean;
- isDumbbellAvailable: boolean;
- isBandsAvailable: boolean;
- isPulleyAvailable: boolean;
- isBodyweight: boolean;
- isWeighted: boolean;
- isAssisted: boolean;
- barbellWeight: number;
- availablePlates: string;
- userBodyWeight: number;
- isKg: boolean;
  +}
  +```
- +### Bodyweight Exercise Warmups
- +```typescript
  +function calculateBodyweightWarmups(config: WarmupCalculationConfig): WarmUp[] {
- const warmups: WarmUp[] = [];
-
- // Initial reps: 60% of working reps
- const initialReps = config.workingReps \* 0.60;
-
- // Single warmup set
- if (config.warmupsCount === 1) {
- warmups.push({
-      warmUpWeightSet: config.workingWeight, // User's bodyweight
-      warmUpReps: Math.ceil(initialReps)
- });
- return warmups;
- }
-
- // Multiple warmup sets
- const newWarmupCount = config.warmupsCount - 1;
- const repsIncrement = (config.workingReps _ 0.6 - config.workingReps _ 0.5) / newWarmupCount;
-
- for (let i = 0; i < config.warmupsCount; i++) {
- warmups.push({
-      warmUpWeightSet: config.workingWeight, // Always bodyweight
-      warmUpReps: Math.ceil(
-        initialReps - (repsIncrement * (config.warmupsCount - (i + 1)))
-      )
- });
- }
-
- return warmups;
  +}
  +```
- +### Weighted Exercise Warmups
- +```typescript
  +function calculateWeightedWarmups(config: WarmupCalculationConfig): WarmUp[] {
- const warmups: WarmUp[] = [];
-
- // Weight progression: 50% to 85% of working weight
- const initialWeight = config.workingWeight.kg \* 0.5;
- const newWarmupCount = config.warmupsCount > 1 ? config.warmupsCount - 1 : 1;
- const weightIncrement = ((config.workingWeight.kg \* 0.85) -
- (config.workingWeight.kg \* 0.5)) / newWarmupCount;
-
- // Reps progression: 75% to 40% of working reps
- let initialReps = config.workingReps \* 0.75;
- const repsIncrement = (config.workingReps \* 0.75 -
- config.workingReps \* 0.4) / newWarmupCount;
-
- // Minimum reps adjustment
- if (initialReps < 5.01 && !config.isPlateAvailable) {
- initialReps = 6;
- }
- if (config.isPlateAvailable && initialReps < 1) {
- initialReps = 1;
- }
-
- for (let i = 0; i < config.warmupsCount; i++) {
- const warmupWeight = initialWeight + (weightIncrement \* i);
- let warmupReps = Math.ceil(initialReps - (repsIncrement \* i));
-
- // Minimum 3 reps for non-plate exercises
- if (warmupReps < 3 && !config.isPlateAvailable) {
-      warmupReps = 3;
- }
-
- // Round weight to available increments
- const roundedWeight = roundToNearestIncrement(
-      warmupWeight,
-      config.incrementValue,
-      config.minWeight,
-      config.maxWeight
- );
-
- // Apply equipment-specific rounding
- const finalWeight = applyEquipmentRounding(roundedWeight, config);
-
- warmups.push({
-      warmUpWeightSet: {
-        kg: finalWeight,
-        lb: finalWeight * 2.20462
-      },
-      warmUpReps: warmupReps
- });
- }
-
- // CRITICAL: Override first warmup for weighted bodyweight exercises
- // This applies to exercises like weighted pull-ups, weighted dips, etc.
- // where 0 kg means bodyweight only (no additional weight)
- if (config.isWeighted && warmups.length > 0) {
- if (config.minWeight === null || config.minWeight === undefined) {
-      warmups[0].warmUpWeightSet = { kg: 0, lb: 0 };
- } else {
-      warmups[0].warmUpWeightSet = {
-        kg: config.minWeight,
-        lb: config.minWeight * 2.20462
-      };
- }
- }
-
- return warmups;
  +}
  +```
- +### Assisted Exercise Warmups
- +```typescript
  +function calculateAssistedWarmups(config: WarmupCalculationConfig): WarmUp[] {
- // Similar to weighted but using assistance weight
- // Working weight represents the assistance amount
- // Actual resistance = bodyweight - assistance
-
- const warmups: WarmUp[] = [];
- const resistanceWeight = config.workingWeight.kg;
-
- const initialWeight = resistanceWeight \* 0.5;
- const newWarmupCount = config.warmupsCount > 1 ? config.warmupsCount - 1 : 1;
- const weightIncrement = ((resistanceWeight \* 0.85) -
- (resistanceWeight \* 0.5)) / newWarmupCount;
-
- let initialReps = config.workingReps \* 0.75;
- const repsIncrement = (config.workingReps \* 0.75 -
- config.workingReps \* 0.4) / newWarmupCount;
-
- for (let i = 0; i < config.warmupsCount; i++) {
- const warmupResistance = initialWeight + (weightIncrement \* i);
- const assistanceWeight = config.userBodyWeight - warmupResistance;
-
- warmups.push({
-      warmUpWeightSet: {
-        kg: assistanceWeight,
-        lb: assistanceWeight * 2.20462
-      },
-      warmUpReps: Math.ceil(initialReps - (repsIncrement * i))
- });
- }
-
- return warmups;
  +}
  +```
- +## Equipment-Specific Weight Rounding
- +### Plate Calculation (Barbell)
- +```typescript
  +function calculatePlatesWeight(
- availablePlates: string,
- targetWeight: number,
- barbellWeight: number,
- isKg: boolean
  +): number {
- if (targetWeight <= barbellWeight) {
- return barbellWeight;
- }
-
- const weightPerSide = (targetWeight - barbellWeight) / 2;
- const plates = isKg
- ? [20, 15, 10, 5, 2.5, 1.25, 0.5, 0.25]
- : [45, 35, 25, 10, 5, 2.5];
-
- let remainingWeight = weightPerSide;
- let totalPlateWeight = 0;
-
- for (const plate of plates) {
- while (remainingWeight >= plate) {
-      totalPlateWeight += plate;
-      remainingWeight -= plate;
- }
- }
-
- return barbellWeight + (totalPlateWeight \* 2);
  +}
  +```
- +### Dumbbell Selection
- +```typescript
  +function getDumbbellWeight(
- availableDumbbells: string,
- targetWeight: number,
- isKg: boolean
  +): number {
- // Parse available dumbbells
- const dumbbells = parseAvailableWeights(availableDumbbells);
-
- // Sort descending
- dumbbells.sort((a, b) => b.weight - a.weight);
-
- // Find closest dumbbell not exceeding target
- for (const db of dumbbells) {
- if (db.weight <= targetWeight && db.count > 0) {
-      return db.weight;
- }
- }
-
- // If no suitable dumbbell, return smallest available
- return dumbbells[dumbbells.length - 1].weight;
  +}
  +```
- +### Resistance Bands
- +```typescript
  +function getBandsWeight(
- availableBands: string,
- targetWeight: number,
- isKg: boolean
  +): number {
- const bands = parseAvailableBands(availableBands);
-
- // Sort by weight
- bands.sort((a, b) => b.weight - a.weight);
-
- // Find closest band
- let closestBand = bands[0];
- let minDiff = Math.abs(targetWeight - bands[0].weight);
-
- for (const band of bands) {
- const diff = Math.abs(targetWeight - band.weight);
- if (diff < minDiff && band.count > 0) {
-      minDiff = diff;
-      closestBand = band;
- }
- }
-
- return closestBand.weight;
  +}
  +```
- +## Complete Implementation
- +```typescript
  +export class WarmupCalculationService {
- static computeWarmups(
- recommendation: RecommendationModel,
- exercise: ExerciseModel,
- userSettings: UserSettings
- ): WarmUp[] {
- // No warmups if count is 0
- if (recommendation.WarmupsCount === 0) {
-      return [];
- }
-
- // Build configuration
- const config: WarmupCalculationConfig = {
-      warmupsCount: recommendation.WarmupsCount,
-      workingWeight: recommendation.Weight,
-      workingReps: recommendation.Reps,
-      incrementValue: recommendation.Increments?.kg || 1,
-      minWeight: recommendation.Min?.kg,
-      maxWeight: recommendation.Max?.kg,
-      isPlateAvailable: recommendation.isPlateAvailable,
-      isDumbbellAvailable: recommendation.isDumbbellAvailable,
-      isBandsAvailable: recommendation.isBandsAvailable,
-      isPulleyAvailable: recommendation.isPulleyAvailable,
-      isBodyweight: recommendation.IsBodyweight,
-      isWeighted: exercise.IsWeighted || false,
-      isAssisted: exercise.IsAssisted || false,
-      barbellWeight: this.getBarbellWeight(exercise, userSettings),
-      availablePlates: userSettings.availablePlates || '',
-      userBodyWeight: userSettings.bodyWeight || 0,
-      isKg: userSettings.unit === 'kg'
- };
-
- // Calculate based on exercise type
- let warmups: WarmUp[];
-
- if (config.isBodyweight) {
-      warmups = this.calculateBodyweightWarmups(config);
- } else if (config.isAssisted) {
-      warmups = this.calculateAssistedWarmups(config);
- } else {
-      warmups = this.calculateWeightedWarmups(config);
- }
-
- return warmups;
- }
-
- private static getBarbellWeight(
- exercise: ExerciseModel,
- userSettings: UserSettings
- ): number {
- // User setting takes precedence
- if (userSettings?.barbellWeight) {
-      return userSettings.barbellWeight;
- }
-
- const exerciseName = exercise.name.toLowerCase();
- const isKg = userSettings?.unit === 'kg';
-
- // Special barbells
- if (exerciseName.includes('ez bar') || exerciseName.includes('curl bar')) {
-      return isKg ? 7 : 15;
- }
-
- if (exerciseName.includes('trap bar') || exerciseName.includes('hex bar')) {
-      return isKg ? 25 : 55;
- }
-
- // Standard barbell
- return isKg ? 20 : 45;
- }
-
- private static roundToNearestIncrement(
- weight: number,
- increment: number,
- min?: number,
- max?: number
- ): number {
- if (increment === 0 || increment === 0.01) {
-      return weight;
- }
-
- const rounded = Math.round(weight / increment) \* increment;
-
- if (min && rounded < min) return min;
- if (max && rounded > max) return max;
-
- return rounded;
- }
  +}
  +```
- +## Testing Scenarios
- +### Test Case 1: Bench Press (3 Warmups)
  +```typescript
  +{
- exercise: "Bench Press",
- warmupsCount: 3,
- workingWeight: { kg: 100, lb: 220 },
- workingReps: 10,
- isPlateAvailable: true,
- isWeighted: true,
- barbellWeight: 20
  +}
- +// Expected warmups:
  +// Set 1: 0 kg × 8 reps (just empty bar = 20kg)
  +// Set 2: 50 kg × 6 reps (rounded to available plates)
  +// Set 3: 85 kg × 4 reps (rounded to available plates)
  +```
- +### Test Case 2: Push-ups (2 Warmups)
  +```typescript
  +{
- exercise: "Push-ups",
- warmupsCount: 2,
- workingWeight: { kg: 80, lb: 176 },
- workingReps: 20,
- isBodyweight: true
  +}
- +// Expected warmups:
  +// Set 1: 80 kg × 10 reps (50% of working reps)
  +// Set 2: 80 kg × 12 reps (60% of working reps)
  +```
- +### Test Case 3: Dumbbell Curls (2 Warmups)
  +```typescript
  +{
- exercise: "Dumbbell Curl",
- warmupsCount: 2,
- workingWeight: { kg: 15, lb: 33 },
- workingReps: 12,
- isDumbbellAvailable: true,
- isWeighted: true,
- availableDumbbells: "5_2_true|7.5_2_true|10_2_true|12.5_2_true|15_2_true"
  +}
- +// Expected warmups:
  +// Set 1: 0 kg × 9 reps
  +// Set 2: 12.5 kg × 5 reps (closest available dumbbell)
  +```
- +### Test Case 4: Assisted Pull-ups
  +```typescript
  +{
- exercise: "Assisted Pull-ups",
- warmupsCount: 2,
- workingWeight: { kg: 30, lb: 66 }, // 30kg assistance
- workingReps: 8,
- isAssisted: true,
- userBodyWeight: 80
  +}
- +// Expected warmups calculated based on resistance (bodyweight - assistance)
  +```
- +## Important Notes
- +1. **Deload Sets**: The `GetNormalDeload` and `GetRestPauseDeload` methods in the MAUI app handle deload weeks differently but warmup calculation remains the same
- +2. **Special Exercise IDs**: Some exercises have hardcoded behaviors:
- - **IsWeighted exercise IDs** (weighted bodyweight exercises):
-     - 18627: Weighted Pull-up
-     - 18628: Weighted Chin-up
-     - 21234: Weighted Chest Dip
-     - 14297: Weighted Triceps Dip
-     - 862, 6993, 13446: Weighted Crunch variations
-     - 863, 6992, 13449: Weighted Twisting Crunch variations
-     - Plus: Weighted Back Extension and other weighted bodyweight movements
- - Band exercise IDs: 16897-16907, 14279, 21508-21514
- +3. **Cardio Exercises**: Exercise ID 16508 and BodypartId 12 show "Brisk/Fast/Cooldown" instead of weights
- +4. **Manual Mode**: When `IsManual` is true and barbell weight exceeds target weight, special handling applies
- +5. **Plate Formatting**: The `availablePlates` string format is: `weight_count_isSystem|weight_count_isSystem`
- Example: `20_6_True|10_4_True|5_4_True|2.5_4_True`
- +6. **IsWeighted Design Rationale**: The `IsWeighted` flag provides a clever solution for exercises that bridge bodyweight and weighted training:
- - For weighted pull-ups: 0 kg = bodyweight pull-ups (not hanging from nothing!)
- - For weighted dips: 0 kg = bodyweight dips
- - This creates a logical warmup progression: bodyweight → partial added weight → full working weight
- - Without this override, the calculation might suggest impossible weights for the first warmup
- +This implementation matches the MAUI app's warmup calculation logic exactly, ensuring consistent behavior across platforms.
  \ No newline at end of file
