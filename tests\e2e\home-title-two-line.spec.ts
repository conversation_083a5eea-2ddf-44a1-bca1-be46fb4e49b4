import { test, expect } from '@playwright/test'

test.describe('Home Page Title Layout', () => {
  test('should display title and subtitle on separate lines with subtitle on single line', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Navigate to home page
    await page.goto('/')

    // Home page will redirect to login, but we can check the content before redirect
    // Wait for the header to be visible (short timeout as it redirects quickly)
    await page.waitForSelector('h1', { state: 'visible', timeout: 1000 })

    // Get the header element
    const header = await page.locator('h1')

    // Verify title only contains "Dr. Muscle X"
    const headerText = await header.textContent()
    expect(headerText).toBe('Dr. Muscle X')

    // Find subtitle paragraph
    const subtitle = await page
      .locator('p')
      .filter({ hasText: "World's Fastest AI Personal Trainer" })
    await expect(subtitle).toBeVisible()
    const subtitleText = await subtitle.textContent()
    expect(subtitleText).toBe("World's Fastest AI Personal Trainer")

    // Verify gold gradient on title
    const headerClasses = await header.getAttribute('class')
    expect(headerClasses).toContain('text-gradient-gold')
    expect(headerClasses).toContain('whitespace-nowrap')

    // Verify subtitle has whitespace-nowrap to prevent wrapping
    const subtitleClasses = await subtitle.getAttribute('class')
    expect(subtitleClasses).toContain('whitespace-nowrap')

    // Verify they are on different lines by checking positions
    const headerBox = await header.boundingBox()
    const subtitleBox = await subtitle.boundingBox()

    if (headerBox && subtitleBox) {
      // Subtitle should be below header
      expect(subtitleBox.y).toBeGreaterThan(headerBox.y + headerBox.height)
    }
  })

  test('should maintain two-line layout on desktop', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 })

    // Navigate to home page
    await page.goto('/')

    // Home page will redirect to login, but we can check the content before redirect
    // Wait for the header to be visible (short timeout as it redirects quickly)
    await page.waitForSelector('h1', { state: 'visible', timeout: 1000 })

    // Get the header element
    const header = await page.locator('h1')

    // Verify title and subtitle are separate
    const headerText = await header.textContent()
    expect(headerText).toBe('Dr. Muscle X')

    // Find subtitle
    const subtitle = await page
      .locator('p')
      .filter({ hasText: "World's Fastest AI Personal Trainer" })
    await expect(subtitle).toBeVisible()

    // Verify subtitle has whitespace-nowrap
    const subtitleClasses = await subtitle.getAttribute('class')
    expect(subtitleClasses).toContain('whitespace-nowrap')
  })
})
