import { Page, Locator } from '@playwright/test'

/**
 * Page Object Model for Workout Page
 * Provides methods to interact with workout page elements
 */
export class WorkoutPage {
  readonly page: Page

  readonly startWorkoutButton: Locator

  readonly workoutTitle: Locator

  readonly exerciseList: Locator

  readonly loadingIndicator: Locator

  constructor(page: Page) {
    this.page = page
    this.startWorkoutButton = page.locator(
      '[data-testid="start-workout-button"], button:has-text("Start Workout")'
    )
    this.workoutTitle = page.locator('[data-testid="workout-title"], h1')
    this.exerciseList = page.locator(
      '[data-testid="exercise-list"], .exercise-list'
    )
    this.loadingIndicator = page.locator(
      '[data-testid="workout-loading"], .loading'
    )
  }

  async waitForPageLoad() {
    // Wait for the workout page to load
    await this.page.waitForLoadState('networkidle')

    // Wait for loading indicator to disappear if present
    try {
      await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 })
    } catch {
      // Loading indicator might not be present, continue
    }

    // Wait for main content to be visible
    await this.page.waitForSelector(
      'main, [data-testid="workout-content"], .workout-content',
      {
        timeout: 15000,
      }
    )
  }

  async startWorkout() {
    // Look for start workout button and click it
    try {
      await this.startWorkoutButton.waitFor({ timeout: 10000 })
      await this.startWorkoutButton.click()
    } catch {
      // If no start button, workout might already be started
      console.log(
        'No start workout button found, workout may already be active'
      )
    }
  }

  async getWorkoutTitle(): Promise<string> {
    try {
      await this.workoutTitle.waitFor({ timeout: 5000 })
      return (await this.workoutTitle.textContent()) || ''
    } catch {
      return ''
    }
  }

  async getExerciseCount(): Promise<number> {
    try {
      const exercises = await this.exerciseList
        .locator('.exercise-item, [data-testid="exercise-item"]')
        .count()
      return exercises
    } catch {
      return 0
    }
  }

  async clickExercise(index: number) {
    const exercises = this.exerciseList.locator(
      '.exercise-item, [data-testid="exercise-item"]'
    )
    await exercises.nth(index).click()
  }

  async navigateToExercise(exerciseId: string | number) {
    await this.page.goto(`/workout/exercise/${exerciseId}`)
    await this.page.waitForLoadState('networkidle')
  }

  async isWorkoutStarted(): Promise<boolean> {
    // Check if we're in an active workout state
    const url = this.page.url()
    return url.includes('/workout') && !url.includes('/complete')
  }
}
