import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('V2 Button Pre-loading', () => {
  test('should pre-load recommendation when clicking Try the new UI button', async ({
    page,
  }) => {
    // Setup authenticated user
    await setupAuthenticatedUser(page)

    // Track API calls
    const recommendationCalls: string[] = []
    await page.route('**/api/v1/exercise/recommendation', async (route) => {
      const postData = route.request().postData()
      if (postData) {
        const body = JSON.parse(postData)
        recommendationCalls.push(`recommendation-${body.ExerciseId}`)
      }

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 12,
          Weight: { Kg: 50, Lb: 110 },
          Percent1RM: 0.75,
          SetMode: 1,
          AllOut: false,
        }),
      })
    })

    // Mock workout template groups with V1 API
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 100,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Find and click the "Try the new UI" button
    const tryNewUIButton = page.locator('text=Start with new exercise view →')
    await expect(tryNewUIButton).toBeVisible()

    // Click the button
    await tryNewUIButton.click()

    // Wait for navigation to exercise-v2 page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)

    // Verify recommendation was pre-loaded BEFORE navigation
    expect(recommendationCalls).toContain('recommendation-100')

    // Verify we're on the v2 page
    expect(page.url()).toContain('/workout/exercise-v2/')

    // Verify exercise loads without transition screen
    await expect(
      page.locator('[data-testid="exercise-transition-screen"]')
    ).not.toBeVisible()
    await expect(
      page.locator('[data-testid="exercise-page-container"]')
    ).toBeVisible()
  })

  test('should handle pre-load failure gracefully', async ({ page }) => {
    // Setup authenticated user
    await setupAuthenticatedUser(page)

    // Mock recommendation API to fail
    await page.route('**/api/v1/exercise/recommendation', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' }),
      })
    })

    // Mock workout template groups with V1 API
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 100,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Find and click the "Try the new UI" button
    const tryNewUIButton = page.locator('text=Start with new exercise view →')
    await expect(tryNewUIButton).toBeVisible()

    // Click the button
    await tryNewUIButton.click()

    // Should still navigate despite pre-load failure
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    expect(page.url()).toContain('/workout/exercise-v2/')
  })

  test('should use same loading mechanism as Start Workout button', async ({
    page,
  }) => {
    // Setup authenticated user
    await setupAuthenticatedUser(page)

    // Track API calls with timestamps
    const apiCalls: { type: string; exerciseId: number; timestamp: number }[] =
      []

    await page.route('**/api/v1/workout/start', async (route) => {
      apiCalls.push({
        type: 'start-workout',
        exerciseId: 0,
        timestamp: Date.now(),
      })
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Exercises: [{ Id: 100, Label: 'Bench Press' }],
        }),
      })
    })

    await page.route('**/api/v1/exercise/recommendation', async (route) => {
      const postData = route.request().postData()
      if (postData) {
        const body = JSON.parse(postData)
        apiCalls.push({
          type: 'recommendation',
          exerciseId: body.ExerciseId,
          timestamp: Date.now(),
        })
      }

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 12,
          Weight: { Kg: 50, Lb: 110 },
          Percent1RM: 0.75,
          SetMode: 1,
          AllOut: false,
        }),
      })
    })

    // Mock workout template groups with V1 API
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 100,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Click Try the new UI button
    const tryNewUIButton = page.locator('text=Start with new exercise view →')
    await tryNewUIButton.click()

    // Wait for navigation
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)

    // Verify the sequence: start workout -> load recommendation -> navigate
    expect(apiCalls.length).toBeGreaterThanOrEqual(2)
    expect(apiCalls[0].type).toBe('start-workout')
    expect(apiCalls[1].type).toBe('recommendation')
    expect(apiCalls[1].exerciseId).toBe(100)

    // Verify recommendation was loaded before navigation completed
    const recommendationTime =
      apiCalls.find((c) => c.type === 'recommendation')?.timestamp || 0
    const navigationTime = Date.now()
    expect(recommendationTime).toBeLessThan(navigationTime)
  })
})
