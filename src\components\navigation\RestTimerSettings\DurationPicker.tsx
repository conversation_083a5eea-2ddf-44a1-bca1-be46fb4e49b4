import React from 'react'

interface DurationPickerProps {
  currentDuration: number
  onSelect: (duration: number) => void
  formatDuration: (seconds: number) => string
}

export function DurationPicker({
  currentDuration,
  onSelect,
  formatDuration,
}: DurationPickerProps) {
  const durations = [5, 20, 30, 60, 90, 120, 180, 300]

  return (
    <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
      <div className="grid grid-cols-2 gap-2">
        {durations.map((seconds) => (
          <button
            key={seconds}
            onClick={() => onSelect(seconds)}
            className={`py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              currentDuration === seconds
                ? 'bg-blue-500 text-white'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
            }`}
          >
            {formatDuration(seconds)}
          </button>
        ))}
        <button
          onClick={() => {
            // eslint-disable-next-line no-alert
            const input = prompt(
              'Enter custom duration in seconds (5-600):',
              currentDuration.toString()
            )
            if (input) {
              const seconds = parseInt(input, 10)
              if (!Number.isNaN(seconds) && seconds >= 5 && seconds <= 600) {
                onSelect(seconds)
              } else {
                // eslint-disable-next-line no-alert
                alert('Please enter a valid number between 5 and 600 seconds')
              }
            }
          }}
          className="py-2 px-3 rounded-md text-sm font-medium transition-colors bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
        >
          Custom
        </button>
      </div>
    </div>
  )
}
