import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test'
import { mockApi } from '../e2e/helpers/mock-api'
import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

describe('Mock Infrastructure Integration', () => {
  let browser: Browser
  let context: BrowserContext
  let page: Page

  beforeAll(async () => {
    browser = await chromium.launch({ headless: true })
    context = await browser.newContext()
    page = await context.newPage()
  })

  afterAll(async () => {
    await context.close()
    await browser.close()
  })

  describe('Fixture Files', () => {
    it('should have all required fixture files', () => {
      const fixturesDir = join(process.cwd(), 'tests', 'fixtures')
      const requiredFixtures = [
        'POST/login.json',
        'GET/workout.json',
        'GET/workout/exercise/123.json',
        'GET/recommendations/123.json',
      ]

      requiredFixtures.forEach((fixture) => {
        const fixturePath = join(fixturesDir, fixture)
        expect(existsSync(fixturePath)).toBe(true)
      })
    })

    it('should have valid JSON structure in fixtures', () => {
      const loginFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/POST/login.json'),
          'utf-8'
        )
      )
      expect(loginFixture.Token).toBeDefined()
      expect(loginFixture.User).toBeDefined()
      expect(loginFixture.User.Email).toBe('<EMAIL>')

      const workoutFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/workout.json'),
          'utf-8'
        )
      )
      expect(Array.isArray(workoutFixture)).toBe(true)
      expect(workoutFixture[0].Id).toBeDefined()

      const exerciseFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/workout/exercise/123.json'),
          'utf-8'
        )
      )
      expect(exerciseFixture.Id).toBe(123)
      expect(exerciseFixture.Name).toBeDefined()

      const recommendationFixture = JSON.parse(
        readFileSync(
          join(process.cwd(), 'tests/fixtures/GET/recommendations/123.json'),
          'utf-8'
        )
      )
      expect(recommendationFixture.Weight).toBeDefined()
      expect(recommendationFixture.Reps).toBeDefined()
    })
  })

  describe('Route Interception', () => {
    it('should intercept all API routes', async () => {
      const interceptedRoutes: string[] = []

      await page.route('**/api/**', async (route, request) => {
        interceptedRoutes.push(`${request.method()} ${request.url()}`)
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ intercepted: true }),
        })
      })

      await page.goto('data:text/html,<html><body>Test</body></html>')

      // Make test API calls
      await page.request.get('/api/test/endpoint')
      await page.request.post('/api/another/endpoint')

      expect(interceptedRoutes.length).toBeGreaterThan(0)
      expect(
        interceptedRoutes.some((route) => route.includes('/api/test/endpoint'))
      ).toBe(true)
      expect(
        interceptedRoutes.some((route) =>
          route.includes('/api/another/endpoint')
        )
      ).toBe(true)
    })

    it('should serve correct fixture data for known endpoints', async () => {
      await mockApi(page)
      await page.goto('data:text/html,<html><body>Test</body></html>')

      // Test login endpoint
      const loginResponse = await page.request.post('/api/Account/Login')
      expect(loginResponse.status()).toBe(200)
      const loginData = await loginResponse.json()
      expect(loginData.Token).toBe('TEST_TOKEN_12345')

      // Test workout endpoint
      const workoutResponse = await page.request.get(
        '/api/Workout/GetUserWorkoutTemplateGroup'
      )
      expect(workoutResponse.status()).toBe(200)
      const workoutData = await workoutResponse.json()
      expect(workoutData[0].Label).toBe('Test Workout A')
    })

    it('should handle missing fixtures gracefully', async () => {
      await mockApi(page)
      await page.goto('data:text/html,<html><body>Test</body></html>')

      const response = await page.request.get('/api/missing/endpoint')
      expect(response.status()).toBe(404)

      const errorData = await response.json()
      expect(errorData.error).toBe('Mock not found')
      expect(errorData.fixture).toBe('GET/missing/endpoint.json')
    })

    it('should handle different HTTP methods correctly', async () => {
      await mockApi(page)
      await page.goto('data:text/html,<html><body>Test</body></html>')

      // Test different methods
      const getResponse = await page.request.get('/api/nonexistent')
      expect(getResponse.status()).toBe(404)
      const getData = await getResponse.json()
      expect(getData.fixture).toBe('GET/nonexistent.json')

      const postResponse = await page.request.post('/api/nonexistent')
      expect(postResponse.status()).toBe(404)
      const postData = await postResponse.json()
      expect(postData.fixture).toBe('POST/nonexistent.json')

      const putResponse = await page.request.put('/api/nonexistent')
      expect(putResponse.status()).toBe(404)
      const putData = await putResponse.json()
      expect(putData.fixture).toBe('PUT/nonexistent.json')
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed fixture files gracefully', async () => {
      // This test would require creating a malformed fixture file
      // For now, we'll test the error handling path by mocking the file system
      await mockApi(page)
      await page.goto('data:text/html,<html><body>Test</body></html>')

      // Test with a path that should exist but might have issues
      const response = await page.request.get('/api/test/error')
      expect([404, 500]).toContain(response.status())
    })
  })
})
