import { renderHook, act, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useWorkoutActions } from '../useWorkoutActions'
import { useWorkoutStore } from '@/stores/workoutStore'
import { WorkoutCache } from '@/utils/workoutCache'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('@/utils/workoutCache')
vi.mock('@/utils/logger')

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  // eslint-disable-next-line react/function-component-definition
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useWorkoutActions - Workout Completion', () => {
  const mockCompleteWorkoutStore = vi.fn()
  const mockResetWorkout = vi.fn()
  const mockSetError = vi.fn()

  const mockWorkoutSession = {
    id: 'session-123',
    startTime: new Date(),
    exercises: [
      {
        exerciseId: 1001,
        name: 'Bench Press',
        sets: [
          {
            setNumber: 1,
            reps: 10,
            weight: 135,
            rir: 2,
            timestamp: new Date(),
          },
          {
            setNumber: 2,
            reps: 8,
            weight: 145,
            rir: 1,
            timestamp: new Date(),
          },
        ],
      },
    ],
    exerciseRIRStatus: { 1001: true },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useWorkoutStore).mockReturnValue({
      completeWorkout: mockCompleteWorkoutStore,
      resetWorkout: mockResetWorkout,
      setError: mockSetError,
      workoutSession: mockWorkoutSession,
    } as any)

    vi.mocked(WorkoutCache.clear).mockImplementation(() => {})
  })

  it('should NOT call resetWorkout immediately after completing workout', async () => {
    // Test rationale: Workout data must remain available for summary page
    // Reset should only happen when user navigates away from summary
    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      await result.current.completeWorkout()
    })

    // Verify the correct sequence of calls
    await waitFor(() => {
      // 1. Workout should be marked as complete
      expect(mockCompleteWorkoutStore).toHaveBeenCalledTimes(1)

      // 2. Workout cache should be cleared
      expect(WorkoutCache.clear).toHaveBeenCalledTimes(1)

      // 3. Workout session should NOT be reset yet
      expect(mockResetWorkout).not.toHaveBeenCalled()
    })
  })

  it('should handle workout completion when no active session exists', async () => {
    // Test rationale: Prevent errors when completing workout without active session
    vi.mocked(useWorkoutStore).mockReturnValue({
      completeWorkout: mockCompleteWorkoutStore,
      resetWorkout: mockResetWorkout,
      setError: mockSetError,
      workoutSession: null, // No active session
    } as any)

    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await expect(result.current.completeWorkout()).rejects.toThrow(
      'No active workout session'
    )

    // Should not call store methods when no session
    expect(mockCompleteWorkoutStore).not.toHaveBeenCalled()
    expect(mockResetWorkout).not.toHaveBeenCalled()
    expect(WorkoutCache.clear).not.toHaveBeenCalled()
  })

  it('should complete workout without resetting data for summary page', async () => {
    // Test rationale: Ensure workout data remains for summary page
    // The data should persist until user navigates away
    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      await result.current.completeWorkout()
    })

    // Verify workout is completed but data is preserved
    await waitFor(() => {
      expect(mockCompleteWorkoutStore).toHaveBeenCalledTimes(1)
      expect(mockResetWorkout).not.toHaveBeenCalled()
    })
  })

  it('should invalidate queries after successful completion', async () => {
    // Test rationale: Ensure fresh data is fetched after workout completion
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries')

    // eslint-disable-next-line react/function-component-definition
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )

    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper,
    })

    await act(async () => {
      await result.current.completeWorkout()
    })

    await waitFor(() => {
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['userWorkouts'],
      })
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['todaysWorkout'],
      })
    })
  })

  it('should maintain atomicity - all or nothing completion', async () => {
    // Test rationale: Ensure partial completion doesn't leave inconsistent state
    mockCompleteWorkoutStore.mockImplementationOnce(() => {
      throw new Error('Store operation failed')
    })

    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await expect(result.current.completeWorkout()).rejects.toThrow()

    // Nothing should be called if the first operation fails
    expect(WorkoutCache.clear).not.toHaveBeenCalled()
    expect(mockResetWorkout).not.toHaveBeenCalled()
  })
})
