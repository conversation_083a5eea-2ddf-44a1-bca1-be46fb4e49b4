import { Page, Locator } from '@playwright/test'

/**
 * Page Object Model for Exercise Page
 * Provides methods to interact with exercise page elements
 */
export class ExercisePage {
  readonly page: Page

  readonly exerciseTitle: Locator

  readonly restTimerButton: Locator

  readonly restTimerModal: Locator

  readonly restTimerDurationSelector: Locator

  readonly saveButton: Locator

  readonly addSetButton: Locator

  readonly setRows: Locator

  readonly nextExerciseButton: Locator

  readonly finishWorkoutButton: Locator

  constructor(page: Page) {
    this.page = page
    this.exerciseTitle = page.locator('[data-testid="exercise-title"], h1')
    this.restTimerButton = page.locator(
      '[data-testid="rest-timer-button"], button:has-text("Rest Timer")'
    )
    this.restTimerModal = page.locator(
      '[data-testid="rest-timer-modal"], .rest-timer-modal'
    )
    this.restTimerDurationSelector = page.locator(
      '[data-testid="rest-timer-duration"], select, input[type="range"]'
    )
    this.saveButton = page.locator(
      '[data-testid="save-button"], button:has-text("Save")'
    )
    this.addSetButton = page.locator(
      '[data-testid="add-set-button"], button:has-text("Add Set")'
    )
    this.setRows = page.locator('[data-testid="set-row"], .set-row')
    this.nextExerciseButton = page.locator(
      '[data-testid="next-exercise"], button:has-text("Next Exercise")'
    )
    this.finishWorkoutButton = page.locator(
      '[data-testid="finish-workout"], button:has-text("Finish Workout")'
    )
  }

  async waitForPageLoad() {
    // Wait for the exercise page to load
    await this.page.waitForLoadState('networkidle')

    // Wait for exercise title to be visible
    try {
      await this.exerciseTitle.waitFor({ timeout: 15000 })
    } catch {
      // Fallback: wait for any main content
      await this.page.waitForSelector(
        'main, [data-testid="exercise-content"]',
        { timeout: 15000 }
      )
    }
  }

  async getExerciseTitle(): Promise<string> {
    try {
      await this.exerciseTitle.waitFor({ timeout: 5000 })
      return (await this.exerciseTitle.textContent()) || ''
    } catch {
      return ''
    }
  }

  async openRestTimer() {
    await this.restTimerButton.waitFor({ timeout: 10000 })
    await this.restTimerButton.click()
    await this.restTimerModal.waitFor({ state: 'visible', timeout: 5000 })
  }

  async setRestTimerDuration(duration: string | number) {
    await this.restTimerDurationSelector.waitFor({ timeout: 5000 })

    // Handle different types of duration selectors
    const tagName = await this.restTimerDurationSelector.evaluate((el) =>
      el.tagName.toLowerCase()
    )

    if (tagName === 'select') {
      await this.restTimerDurationSelector.selectOption(duration.toString())
    } else if (tagName === 'input') {
      await this.restTimerDurationSelector.fill(duration.toString())
    }
  }

  async closeRestTimer() {
    // Look for close button or click outside modal
    const closeButton = this.page.locator(
      '[data-testid="close-rest-timer"], .close-button, button:has-text("Close")'
    )

    try {
      await closeButton.waitFor({ timeout: 3000 })
      await closeButton.click()
    } catch {
      // Fallback: press Escape key
      await this.page.keyboard.press('Escape')
    }

    await this.restTimerModal.waitFor({ state: 'hidden', timeout: 5000 })
  }

  async addSet() {
    await this.addSetButton.waitFor({ timeout: 5000 })
    await this.addSetButton.click()
  }

  async getSetCount(): Promise<number> {
    try {
      return await this.setRows.count()
    } catch {
      return 0
    }
  }

  async fillSetData(setIndex: number, reps: string, weight: string) {
    const setRow = this.setRows.nth(setIndex)

    // Fill reps
    const repsInput = setRow.locator(
      '[data-testid="reps-input"], input[placeholder*="reps" i]'
    )
    await repsInput.waitFor({ timeout: 5000 })
    await repsInput.fill(reps)

    // Fill weight
    const weightInput = setRow.locator(
      '[data-testid="weight-input"], input[placeholder*="weight" i]'
    )
    await weightInput.waitFor({ timeout: 5000 })
    await weightInput.fill(weight)
  }

  async saveExercise() {
    await this.saveButton.waitFor({ timeout: 5000 })
    await this.saveButton.click()
  }

  async goToNextExercise() {
    try {
      await this.nextExerciseButton.waitFor({ timeout: 5000 })
      await this.nextExerciseButton.click()
    } catch {
      // Might be the last exercise
      console.log('No next exercise button found')
    }
  }

  async finishWorkout() {
    await this.finishWorkoutButton.waitFor({ timeout: 5000 })
    await this.finishWorkoutButton.click()
  }

  async isRestTimerVisible(): Promise<boolean> {
    try {
      await this.restTimerModal.waitFor({ state: 'visible', timeout: 2000 })
      return true
    } catch {
      return false
    }
  }
}
