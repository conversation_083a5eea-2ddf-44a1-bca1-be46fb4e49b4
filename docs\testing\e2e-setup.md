# E2E Test Setup Guide

## Overview

The E2E tests can run against either a local development server or the production environment. The test environment is controlled by the `PLAYWRIGHT_BASE_URL` environment variable.

When running locally, the development server automatically proxies API requests to the production backend, avoiding CORS issues while testing with real data.

## Running E2E Tests

### Against Local Development Server (Default)

1. **Start the development server:**

   ```bash
   npm run dev
   ```

2. **In another terminal, run the tests:**

   ```bash
   npm run test:e2e:local
   ```

   This will run tests against `http://localhost:3000` by default.

### Against Production

To run tests against the production environment:

```bash
npm run test:production
```

This uses a separate `playwright.production.config.ts` that specifically targets `https://x.dr-muscle.com`.

## Test Credentials

- Email: `<EMAIL>`
- Password: `Dr123456`

**Note:** These credentials work with the production API. The local development server automatically proxies API requests to the production backend.

## Environment Variables

- `PLAYWRIGHT_BASE_URL`: Override the base URL for tests
  ```bash
  PLAYWRIGHT_BASE_URL=http://localhost:3000 npm run test:e2e
  ```

## API Proxy Configuration

The development server includes automatic API proxying configured in `next.config.js`:

- All requests to `/api/*` are proxied to `https://drmuscle.azurewebsites.net`
- This only works in development mode (`NODE_ENV=development`)
- The proxy handles all Dr. Muscle API endpoints including:
  - `/api/Account/*` - Authentication
  - `/api/Exercise/*` - Exercise data
  - `/api/UserWorkout/*` - Workout management
  - `/api/Sets/*` - Set logging
  - And more...

## Common Issues

### CORS Errors

If you see CORS errors when running tests:

- **Cause**: Tests are running against production URL which has CORS restrictions
- **Solution**: Use local development server with `npm run test:e2e:local`

### Authentication Failures

If authentication fails during tests:

- **Local Dev**: The dev server automatically proxies API requests. If issues persist, check that the dev server is running.
- **Production**: Verify test credentials are still valid

## CI/CD Configuration

In CI environments, set the appropriate `PLAYWRIGHT_BASE_URL`:

- For PR checks: Use a preview URL
- For production tests: Use `https://x.dr-muscle.com`
