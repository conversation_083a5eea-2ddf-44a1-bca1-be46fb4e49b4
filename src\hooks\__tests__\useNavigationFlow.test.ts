import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useRouter, usePathname } from 'next/navigation'
import { useNavigationFlow } from '../useNavigationFlow'
import { ROUTES } from '@/lib/navigation'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  usePathname: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  })),
}))

// Mock authStore
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
    hasHydrated: true,
  })),
}))

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  prefetch: vi.fn(),
}

describe('useNavigationFlow', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(usePathname).mockReturnValue(ROUTES.PROGRAM)

    // Clear sessionStorage
    sessionStorage.clear()

    // Mock document.referrer
    Object.defineProperty(document, 'referrer', {
      value: '',
      configurable: true,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Navigation methods', () => {
    it('should navigate to program page', () => {
      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        result.current.navigateToProgram()
      })

      expect(mockRouter.push).toHaveBeenCalledWith(ROUTES.PROGRAM)
    })

    it('should navigate to workout page', () => {
      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        result.current.navigateToWorkout()
      })

      expect(mockRouter.push).toHaveBeenCalledWith(ROUTES.WORKOUT)
    })

    it('should preserve query parameters when navigating', () => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: {
          href: 'http://localhost:3000/program?source=email&campaign=welcome',
        },
        writable: true,
      })

      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        result.current.navigateToWorkout(true)
      })

      expect(mockRouter.push).toHaveBeenCalledWith(
        '/workout?source=email&campaign=welcome'
      )
    })

    it('should navigate back from workout to program', () => {
      // Reset window.location to avoid query params from previous test
      Object.defineProperty(window, 'location', {
        value: { href: 'http://localhost:3000/workout' },
        writable: true,
      })

      vi.mocked(usePathname).mockReturnValue(ROUTES.WORKOUT)

      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        result.current.navigateBack()
      })

      expect(mockRouter.push).toHaveBeenCalledWith(ROUTES.PROGRAM)
    })
  })

  describe('Direct access handling', () => {
    it('should redirect to program when directly accessing workout', () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.WORKOUT)

      renderHook(() => useNavigationFlow({ checkDirectAccess: true }))

      expect(mockRouter.replace).toHaveBeenCalledWith(ROUTES.PROGRAM)
      expect(sessionStorage.getItem('intendedDestination')).toBe(ROUTES.WORKOUT)
    })

    it('should not redirect if coming from program page', () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.WORKOUT)

      // Mock referrer to be from program page
      Object.defineProperty(document, 'referrer', {
        value: 'http://localhost:3000/program',
        configurable: true,
      })

      renderHook(() => useNavigationFlow({ checkDirectAccess: true }))

      expect(mockRouter.replace).not.toHaveBeenCalled()
    })

    it('should not check direct access when disabled', () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.WORKOUT)

      renderHook(() => useNavigationFlow({ checkDirectAccess: false }))

      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })

  describe('Route prefetching', () => {
    it('should prefetch program route when on login page', () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.LOGIN)

      renderHook(() => useNavigationFlow({ prefetchRoutes: true }))

      expect(mockRouter.prefetch).toHaveBeenCalledWith(ROUTES.PROGRAM)
    })

    it('should prefetch workout route when on program page', () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.PROGRAM)

      renderHook(() => useNavigationFlow({ prefetchRoutes: true }))

      expect(mockRouter.prefetch).toHaveBeenCalledWith(ROUTES.WORKOUT)
    })

    it('should not prefetch when disabled', () => {
      renderHook(() => useNavigationFlow({ prefetchRoutes: false }))

      expect(mockRouter.prefetch).not.toHaveBeenCalled()
    })
  })

  describe('Intended destination', () => {
    it('should check and navigate to intended destination', () => {
      sessionStorage.setItem('intendedDestination', '/workout/exercise/123')

      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        const hasIntended = result.current.checkIntendedDestination()
        expect(hasIntended).toBe(true)
      })

      expect(mockRouter.push).toHaveBeenCalledWith('/workout/exercise/123')
      expect(sessionStorage.getItem('intendedDestination')).toBeNull()
    })

    it('should return false when no intended destination', () => {
      const { result } = renderHook(() => useNavigationFlow())

      act(() => {
        const hasIntended = result.current.checkIntendedDestination()
        expect(hasIntended).toBe(false)
      })

      expect(mockRouter.push).not.toHaveBeenCalled()
    })
  })

  describe('Authentication handling', () => {
    it('should not redirect when not authenticated', async () => {
      vi.mocked(usePathname).mockReturnValue(ROUTES.WORKOUT)

      // Mock unauthenticated state
      const { useAuthStore } = await import('@/stores/authStore')
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        hasHydrated: true,
      } as any)

      renderHook(() => useNavigationFlow())

      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })
})
