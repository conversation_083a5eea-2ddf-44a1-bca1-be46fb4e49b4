# ADR-002: Warmup Set Data Model Separation and Exercise Page Component Hierarchy

## Status

**Accepted** - January 2025

## Context

### Problem Statement

The Dr. Muscle X web application was experiencing two critical issues:

1. **Warmup sets displaying incorrect values** - showing identical values to work sets instead of calculated warmup values
2. **Inconsistent component hierarchy** - multiple implementations for displaying exercise sets causing maintenance issues

### Technical Details

**Issue 1: Warmup Set Data Model**

**Root Cause:** The `generateAllSets` function was incorrectly setting both `Reps` and `WarmUpReps` properties on warmup sets. The UI's `ExerciseSetsGrid` component reads the `Reps` property when available, causing it to display work set values instead of warmup values.

**Symptoms:**

- Warmup sets showing 100% of work weight instead of ~50%
- Warmup sets showing 100% of work reps instead of ~75%
- Values persisting incorrectly when navigating between sets

**Issue 2: Component Hierarchy**

**Legacy Implementation:**

- Multiple components: `SetScreen`, `SetListMobile`, `SetScreenWithGrid`
- Conditional rendering logic to switch between implementations
- Duplicate code and inconsistent behavior

## Decision

### Decision 1: Strict Warmup Set Data Model Separation

**Warmup sets will use ONLY warmup-specific properties:**

```typescript
// Warmup sets - ONLY these properties
{
  WarmUpReps: number,        // Calculated warmup reps (~75% of work reps)
  WarmUpWeightSet: {         // Calculated warmup weights (~50% of work weight)
    Kg: number,
    Lb: number
  },
  Weight: { Kg: 0, Lb: 0 }, // Always zero for warmups
  IsWarmups: true
  // NO Reps property - this causes UI to read wrong values
}

// Work sets - standard properties
{
  Reps: number,              // Target reps
  Weight: {                  // Working weight
    Kg: number,
    Lb: number
  },
  IsWarmups: false
}
```

### Decision 2: Single Component Hierarchy

**Exercise page will use a single, consistent component hierarchy:**

```
ExercisePageClient → SetScreenWithGrid → ExerciseSetsGrid
```

**Actions taken:**

- Removed `SetScreen` component completely
- Removed `SetListMobile` component completely
- Consolidated all functionality into `ExerciseSetsGrid`

## Alternatives Considered

### For Data Model Issue

**Option 1: Modify UI to check IsWarmups flag**

- **Pros:** No data model changes needed
- **Cons:** Complex conditional logic in UI, error-prone
- **Decision:** Rejected - violates separation of concerns

**Option 2: Use single property set for both warmup and work**

- **Pros:** Simpler data model
- **Cons:** Loss of warmup-specific information, breaks existing API contract
- **Decision:** Rejected - would require API changes

### For Component Hierarchy

**Option 1: Keep multiple implementations with feature flags**

- **Pros:** A/B testing capability, gradual rollout
- **Cons:** Maintenance overhead, code duplication
- **Decision:** Rejected - complexity outweighs benefits

**Option 2: Refactor existing components**

- **Pros:** Preserve some existing code
- **Cons:** Technical debt, inconsistent patterns
- **Decision:** Rejected - clean slate approach preferred

## Consequences

### Positive

- ✅ **Clear data model** - No ambiguity about which properties to use
- ✅ **Correct warmup calculations** - Shows proper ~50% weight and ~75% reps
- ✅ **Single source of truth** - One component hierarchy to maintain
- ✅ **Reduced complexity** - No conditional rendering logic
- ✅ **Better testability** - Clear expectations for data structure
- ✅ **Improved performance** - Less code to bundle and execute

### Negative

- ❌ **Breaking change** - Any code expecting `Reps` on warmups will break
- ❌ **Migration effort** - Need to update all warmup set generation code
- ❌ **Lost flexibility** - Can't easily switch between UI implementations

### Neutral

- 🔄 **API contract preserved** - Backend already uses this structure
- 🔄 **Learning curve** - Developers need to understand the data model

## Implementation Details

### Code Changes

1. **Updated `createWorkoutSetsMAUI.ts`:**
   - Ensure `WarmUpReps` and `WarmUpWeightSet` are set for warmups
   - Never set `Reps` property on warmup sets

2. **Fixed `useSetScreenLogic` hook:**
   - Added `currentSetIndex` to useEffect dependencies
   - Ensures data reloads when navigating between sets

3. **Removed legacy components:**
   - Deleted `SetScreen.tsx`
   - Deleted `SetListMobile.tsx`
   - Removed all imports and references

### Testing Strategy

- Unit tests verify warmup sets have correct properties
- E2E tests confirm warmup values display correctly
- Component hierarchy tests ensure single implementation path

## Success Criteria

- [x] Warmup sets display ~50% of work weight
- [x] Warmup sets display ~75% of work reps
- [x] Values update correctly when navigating between sets
- [x] No duplicate component implementations
- [x] All tests passing with new structure
- [x] No console warnings about React keys or missing properties

## Lessons Learned

1. **Data model clarity is crucial** - Ambiguous property usage leads to UI bugs
2. **Single implementation is better** - Multiple paths increase maintenance burden
3. **Effect dependencies matter** - Missing dependencies cause stale data issues
4. **Comprehensive testing pays off** - Tests caught edge cases during refactoring

## References

- Pull Request #308: Fix warmup set display issues
- Commit `a795612`: Ensure warmup sets use zero Weight property
- Commit `eb06c57`: Update warmup set data when navigating
- Commit `eff59c4`: Remove legacy SetScreen and SetListMobile components

---

**Decision Made By:** Development Team  
**Date:** January 2025  
**Supersedes:** None  
**Superseded By:** None
