'use client'

import React from 'react'
import type { WorkoutLogSerieModel, RecommendationModel } from '@/types'
import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'
import { formatWeightForDisplay } from '@/utils/weightUtils'

// Extended type to include warmup properties
type ExtendedWorkoutLogSerieModel = WorkoutLogSerieModel &
  Partial<WorkoutLogSerieModelRef> & {
    WarmUpReps?: number
    WarmUpWeightSet?: { Lb: number; Kg: number }
    IsSkipped?: boolean
  }

interface NextSetsPreviewProps {
  nextSets: ExtendedWorkoutLogSerieModel[]
  unit: 'kg' | 'lbs'
  currentSetIndex: number
  recommendation?: RecommendationModel | null
}

export function NextSetsPreview({
  nextSets,
  unit,
  currentSetIndex,
  recommendation,
}: NextSetsPreviewProps) {
  if (nextSets.length === 0) {
    return null
  }

  return (
    <div className="w-full max-w-md bg-surface-secondary/50 rounded-lg p-2 sm:p-3">
      <h3 className="text-sm font-medium text-text-secondary mb-1.5 sm:mb-2">
        Next sets
      </h3>
      <div className="space-y-1">
        {nextSets.map((set, index) => {
          const isWarmup = set.IsWarmups
          const reps = isWarmup ? set.WarmUpReps : set.Reps
          const weightValue = isWarmup
            ? set.WarmUpWeightSet?.[unit === 'kg' ? 'Kg' : 'Lb']
            : set.Weight?.[unit === 'kg' ? 'Kg' : 'Lb']
          const setNumber = currentSetIndex + index + 2

          // For warmup sets, simply count warmups within the nextSets array
          let warmupNumber = 1
          if (isWarmup) {
            // Count how many warmup sets come before this one in the nextSets array
            warmupNumber = nextSets
              .slice(0, index + 1)
              .filter((s) => s.IsWarmups).length
          }

          const setLabel = isWarmup ? `W${warmupNumber}` : `Set ${setNumber}`

          // Get increment for proper weight formatting
          const increment =
            recommendation?.Increments?.[unit === 'kg' ? 'Kg' : 'Lb']
          const formattedWeight = formatWeightForDisplay(weightValue, increment)

          return (
            <div
              key={set.Id || index}
              className="flex items-center justify-between py-1 px-2 sm:py-1.5 sm:px-2.5 bg-surface-primary/50 rounded-md"
            >
              <span className="text-sm font-medium text-text-primary">
                {setLabel}
              </span>
              <div className="flex items-center gap-1.5 sm:gap-2 text-sm text-text-secondary">
                <span>{reps || 0} reps</span>
                <span className="text-text-tertiary">×</span>
                <span>
                  {formattedWeight} {unit}
                </span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
