import { describe, it, expect, beforeEach, afterEach } from 'vitest'

describe('Playwright CI Config', () => {
  let originalEnv: string | undefined

  beforeEach(() => {
    originalEnv = process.env.CI_FULL_SUITE
    // Clear the module cache to ensure fresh config loading
    delete require.cache[
      require.resolve('../../../playwright.ci.optimized.config.ts')
    ]
  })

  afterEach(() => {
    if (originalEnv !== undefined) {
      process.env.CI_FULL_SUITE = originalEnv
    } else {
      delete process.env.CI_FULL_SUITE
    }
    // Clear the module cache again
    delete require.cache[
      require.resolve('../../../playwright.ci.optimized.config.ts')
    ]
  })

  it('should include all projects when CI_FULL_SUITE is not set', async () => {
    delete process.env.CI_FULL_SUITE

    // Dynamically import the config to get fresh evaluation
    const config = await import('../../../playwright.ci.optimized.config.ts')
    const playwrightConfig = config.default

    expect(playwrightConfig.projects).toBeDefined()
    expect(Array.isArray(playwrightConfig.projects)).toBe(true)
    expect(playwrightConfig.projects.length).toBeGreaterThan(1)

    // Should include both Chrome and Safari projects
    const projectNames = playwrightConfig.projects.map((p: any) => p.name)
    expect(projectNames.some((name: string) => name.includes('Chrome'))).toBe(
      true
    )
    expect(projectNames.some((name: string) => name.includes('Safari'))).toBe(
      true
    )
  })

  it('should filter to only Chromium projects when CI_FULL_SUITE is set', async () => {
    process.env.CI_FULL_SUITE = '1'

    // Dynamically import the config to get fresh evaluation
    const config = await import('../../../playwright.ci.optimized.config.ts')
    const playwrightConfig = config.default

    expect(playwrightConfig.projects).toBeDefined()
    expect(Array.isArray(playwrightConfig.projects)).toBe(true)

    // Should only include Chrome projects
    const projectNames = playwrightConfig.projects.map((p: any) => p.name)
    projectNames.forEach((name: string) => {
      expect(name.toLowerCase()).toContain('chrome')
    })

    // Should not include Safari projects
    expect(projectNames.some((name: string) => name.includes('Safari'))).toBe(
      false
    )
  })

  it('should handle different CI_FULL_SUITE values correctly', async () => {
    const testCases = [
      { value: '1', shouldFilter: true },
      { value: 'true', shouldFilter: false },
      { value: '0', shouldFilter: false },
      { value: '', shouldFilter: false },
    ]

    await Promise.all(
      testCases.map(async ({ value, shouldFilter }) => {
        process.env.CI_FULL_SUITE = value

        // Clear cache and reimport
        delete require.cache[
          require.resolve('../../../playwright.ci.optimized.config.ts')
        ]
        const config = await import(
          '../../../playwright.ci.optimized.config.ts'
        )
        const playwrightConfig = config.default

        const projectNames = playwrightConfig.projects.map((p: any) => p.name)

        if (shouldFilter) {
          // Should only have Chrome projects
          projectNames.forEach((name: string) => {
            expect(name.toLowerCase()).toContain('chrome')
          })
        } else {
          // Should have both Chrome and Safari projects
          expect(
            projectNames.some((name: string) => name.includes('Chrome'))
          ).toBe(true)
          expect(
            projectNames.some((name: string) => name.includes('Safari'))
          ).toBe(true)
        }
      })
    )
  })

  it('should maintain project structure when filtering', async () => {
    process.env.CI_FULL_SUITE = '1'

    const config = await import('../../../playwright.ci.optimized.config.ts')
    const playwrightConfig = config.default

    // Each project should have required properties
    playwrightConfig.projects.forEach((project: any) => {
      expect(project.name).toBeDefined()
      expect(project.use).toBeDefined()
      expect(typeof project.retries).toBe('number')
      expect(typeof project.workers).toBe('number')
    })
  })
})
