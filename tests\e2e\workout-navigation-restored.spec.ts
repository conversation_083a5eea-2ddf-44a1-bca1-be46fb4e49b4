import { test, expect } from '@playwright/test'

test.describe('Workout Navigation - Original Exercise Page', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Dr123456')
    await page.getByRole('button', { name: /log in/i }).click()

    // Navigate to workout
    await page.waitForURL('/')
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 30000,
    })
  })

  test('tapping exercise card navigates to original exercise page', async ({
    page,
  }) => {
    // Given: User is on workout overview with exercises
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()
    await expect(firstExerciseCard).toBeVisible()

    // When: User taps on exercise card
    await firstExerciseCard.click()

    // Then: Should navigate to original exercise page (not v2)
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise\/\d+/)
    expect(currentUrl).not.toContain('exercise-v2')
  })

  test('Start Workout button navigates to original exercise page', async ({
    page,
  }) => {
    // Given: User is on workout overview
    const startButton = page.getByRole('button', { name: /Start Workout/i })
    await expect(startButton).toBeVisible()

    // When: User taps Start Workout
    await startButton.click()

    // Then: Should navigate to first exercise using original page
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise\/\d+/)
    expect(currentUrl).not.toContain('exercise-v2')
  })

  test('Continue Workout button navigates to original exercise page', async ({
    page,
  }) => {
    // Start workout first
    const startButton = page.getByRole('button', { name: /Start Workout/i })
    await startButton.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Go back to workout overview
    await page.goBack()
    await page.waitForSelector('[data-testid="exercise-card"]')

    // Given: Workout is active (button should say Continue)
    const continueButton = page.getByRole('button', {
      name: /Continue Workout/i,
    })
    await expect(continueButton).toBeVisible()

    // When: User taps Continue Workout
    await continueButton.click()

    // Then: Should navigate to exercise using original page
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise\/\d+/)
    expect(currentUrl).not.toContain('exercise-v2')
  })

  test('Try new UI button navigates to V2 exercise page', async ({ page }) => {
    // Given: User sees the Try new UI section
    await page.waitForTimeout(1000) // Let page fully load
    await expect(page.locator('text=Try our new UI')).toBeVisible({
      timeout: 10000,
    })
    const tryNewUIButton = page.getByRole('button', {
      name: /Start with new exercise view/i,
    })
    await expect(tryNewUIButton).toBeVisible()

    // When: User taps Try new UI button
    await tryNewUIButton.click()

    // Then: Should navigate to V2 exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise-v2\/\d+/)
    expect(currentUrl).toContain('exercise-v2')
  })

  test('exercise click with active workout navigates to original page', async ({
    page,
  }) => {
    // Start workout first
    const startButton = page.getByRole('button', { name: /Start Workout/i })
    await startButton.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })

    // Go back to workout overview
    await page.goBack()
    await page.waitForSelector('[data-testid="exercise-card"]')

    // Given: Workout is active, tap a different exercise
    const secondExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .nth(1)
    await expect(secondExerciseCard).toBeVisible()

    // When: User taps on second exercise card
    await secondExerciseCard.click()

    // Then: Should navigate to original exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 10000 })
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout\/exercise\/\d+/)
    expect(currentUrl).not.toContain('exercise-v2')
  })
})
