import React from 'react'
import { vi } from 'vitest'

// Create a proper mock for framer-motion that filters out motion-specific props
export const createMotionComponent = (element: string) => {
  return React.forwardRef(
    (props: Record<string, unknown>, ref: React.Ref<HTMLElement>) => {
      // Filter out motion-specific props to avoid React warnings
      const {
        drag,
        dragConstraints,
        onDragStart,
        onDragEnd,
        animate,
        initial,
        exit,
        transition,
        whileHover,
        whileTap,
        whileDrag,
        whileFocus,
        whileInView,
        variants,
        custom,
        inherit,
        layout,
        layoutId,
        onAnimationStart,
        onAnimationComplete,
        onDragTransitionEnd,
        onLayoutAnimationComplete,
        onViewportEnter,
        onViewportLeave,
        viewport,
        style,
        ...domProps
      } = props

      // Include style but without motion values
      if (style) {
        domProps.style = style
      }

      return React.createElement(element, { ...domProps, ref })
    }
  )
}

export const frameMotionMock = {
  motion: {
    div: createMotionComponent('div'),
    span: createMotionComponent('span'),
    button: createMotionComponent('button'),
    section: createMotionComponent('section'),
    article: createMotionComponent('article'),
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
  useAnimation: () => ({
    start: vi.fn(),
    stop: vi.fn(),
    set: vi.fn(),
  }),
  useMotionValue: (initialValue: unknown) => ({
    get: () => initialValue,
    set: vi.fn(),
    subscribe: vi.fn(),
  }),
  useTransform: vi.fn((value) => value),
  useSpring: vi.fn((value) => value),
  useVelocity: vi.fn((value) => value),
  useScroll: () => ({
    scrollX: { get: () => 0 },
    scrollY: { get: () => 0 },
    scrollXProgress: { get: () => 0 },
    scrollYProgress: { get: () => 0 },
  }),
}
