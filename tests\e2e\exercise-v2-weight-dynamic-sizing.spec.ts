import { test, expect } from '@playwright/test'

test.describe('Exercise V2 - Weight Input Dynamic Sizing', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and login
    await page.goto('/login')
    await page
      .getByRole('textbox', { name: 'Email' })
      .fill('<EMAIL>')
    await page.getByRole('textbox', { name: 'Password' }).fill('Dr123456')
    await page.getByRole('button', { name: 'Login' }).click()

    // Wait for program page and navigate to workout
    await page.waitForURL('/program')
    await page.getByTestId('start-workout-button').click()

    // Navigate to exercise v2 view
    await page.waitForURL('/workout')
    await page
      .getByRole('button', { name: 'Start with new exercise view →' })
      .click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForSelector('[data-testid="exercise-page-container"]')
  })

  test('should dynamically resize weight text based on value', async ({
    page,
  }) => {
    // Click on weight input to focus
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    await weightInput.click()

    // Test 1: Small whole number (should use largest size)
    await weightInput.fill('35')
    const smallNumberClass = await weightInput.getAttribute('class')
    expect(smallNumberClass).toContain('text-7xl')

    // Test 2: Decimal number (should use smaller size)
    await weightInput.selectText()
    await weightInput.fill('17.50')
    const decimalNumberClass = await weightInput.getAttribute('class')
    expect(decimalNumberClass).toContain('text-5xl')

    // Test 3: Three-digit number (should use medium size)
    await weightInput.selectText()
    await weightInput.fill('150')
    const threeDigitClass = await weightInput.getAttribute('class')
    expect(threeDigitClass).toContain('text-6xl')

    // Test 4: Large decimal number (should use smaller size)
    await weightInput.selectText()
    await weightInput.fill('999.99')
    const largeDecimalClass = await weightInput.getAttribute('class')
    expect(largeDecimalClass).toContain('text-4xl')
  })

  test('should maintain consistent width while changing text size', async ({
    page,
  }) => {
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    await weightInput.click()

    // Check that width class remains consistent
    await weightInput.fill('35')
    const class1 = await weightInput.getAttribute('class')
    expect(class1).toContain('w-36')

    await weightInput.selectText()
    await weightInput.fill('17.50')
    const class2 = await weightInput.getAttribute('class')
    expect(class2).toContain('w-36')

    await weightInput.selectText()
    await weightInput.fill('150')
    const class3 = await weightInput.getAttribute('class')
    expect(class3).toContain('w-36')
  })

  test('should handle zero weight appropriately', async ({ page }) => {
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    await weightInput.click()

    await weightInput.fill('0')
    const zeroClass = await weightInput.getAttribute('class')
    expect(zeroClass).toContain('text-7xl') // Zero should use largest size
  })

  test('should handle very small decimals', async ({ page }) => {
    const weightInput = page.getByRole('spinbutton', { name: 'Weight' })
    await weightInput.click()

    await weightInput.fill('0.5')
    const smallDecimalClass = await weightInput.getAttribute('class')
    expect(smallDecimalClass).toContain('text-5xl')

    await weightInput.selectText()
    await weightInput.fill('0.25')
    const verySmallDecimalClass = await weightInput.getAttribute('class')
    expect(verySmallDecimalClass).toContain('text-5xl')
  })
})
