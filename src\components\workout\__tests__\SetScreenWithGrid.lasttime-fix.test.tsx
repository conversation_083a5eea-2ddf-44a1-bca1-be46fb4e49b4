import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import type {
  RecommendationModel,
  ExerciseModel,
  WorkoutLogSerieModel,
} from '@/types'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/workout/exercise/123',
}))

// Mock the hooks
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lb' }),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 123,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsTimeBased: false,
}

const mockRecommendation: RecommendationModel = {
  Reps: 11,
  Weight: { Lb: 108, Kg: 49 },
  Series: 6,
  WarmupsCount: 0,
  FirstWorkSetReps: 5,
  FirstWorkSetWeight: { Lb: 132, Kg: 60 },
}

const mockSetData = {
  reps: 11,
  weight: '108',
  rir: 0,
}

const mockCompletedSets: WorkoutLogSerieModel[] = []

const mockUseSetScreenLogic = vi.mocked(useSetScreenLogic)

describe('SetScreenWithGrid - Last Time Info Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUseSetScreenLogic.mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      isTransitioning: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      completedSets: mockCompletedSets,
      setData: mockSetData,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    })
  })

  it('should NOT display ExerciseInfo recommendations section at page level', () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // The recommendations section (ExerciseInfo testid) should NOT exist at page level
    expect(screen.queryByTestId('recommendations')).not.toBeInTheDocument()

    // Note: "Last time" text may still appear in ExplainerBox under active set, which is correct
    // We're only testing that the page-level ExerciseInfo component is not rendered
  })

  it('should not render page-level ExerciseInfo component with recommendations testid', () => {
    render(
      <NavigationProvider>
        <SetScreenWithGrid exerciseId={123} />
      </NavigationProvider>
    )

    // The ExerciseInfo component (with recommendations testid) should not be rendered at page level
    expect(screen.queryByTestId('recommendations')).not.toBeInTheDocument()

    // Note: Recommendation and last time info should appear under active set via ExplainerBox,
    // but that's tested elsewhere. This test focuses on removing the page-level ExerciseInfo.
  })
})
