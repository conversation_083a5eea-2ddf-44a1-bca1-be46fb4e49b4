/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  // Only run one test file
  testMatch: 'core-flow-smoke.spec.ts',

  // Fast timeout for test execution only
  timeout: 10000,

  // Don't retry on failure (for speed)
  retries: 0,

  // Run tests in parallel
  workers: 1,

  // Quiet output
  reporter: [['dot']],

  use: {
    // Base URL for all tests
    baseURL: 'http://localhost:3000',

    // Don't wait for animations
    actionTimeout: 3000,

    // No screenshots/videos for speed
    screenshot: 'only-on-failure',
    video: 'off',
    trace: 'off',
  },

  // Only test in one browser for speed
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Mobile viewport for consistency with main tests
        viewport: { width: 390, height: 844 },
      },
    },
  ],

  // No webServer - test will check if server is running
})
