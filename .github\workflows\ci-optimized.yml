name: Streamlined CI Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop, staging]

# Cancel outdated runs when new commits are pushed
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' && github.ref != 'refs/heads/develop' && github.ref != 'refs/heads/staging' }}

env:
  NODE_VERSION: '20.x'
  NODE_OPTIONS: '--max_old_space_size=6144 --max-semi-space-size=512 --expose-gc'
  WEBKIT_DISABLE_COMPOSITING: '1'
  WEBKIT_FORCE_COMPOSITING_MODE: '0'
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '0'
  UV_THREADPOOL_SIZE: '128'

jobs:
  # Validation: Linting, type checking, and build (macOS for stability)
  validation:
    name: Validation & Build
    runs-on: [self-hosted, macos]
    timeout-minutes: 15

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Validate critical tests
        run: node scripts/validate-critical-tests.js

      - name: Run validation checks in parallel
        run: |
          echo "Running validation checks..."
          npm run typecheck &
          npm run lint &
          npm run prettier:check &
          npm run check:file-sizes &
          wait
          echo "✅ All validation checks completed"

      - name: Build application
        run: |
          echo "Building application..."
          npm run build
          echo "✅ Build completed successfully"

      - name: Verify build output
        run: |
          if [ ! -d ".next" ]; then
            echo "::error::.next directory not found after build"
            exit 1
          fi
          echo "✅ Build output verified ($(du -sh .next/))"

      - name: Bundle analysis
        run: |
          echo "Analyzing bundle size..."
          npm run analyze > bundle-analysis.txt 2>&1 || true
          if grep -q "First Load JS" bundle-analysis.txt; then
            BUNDLE_SIZE=$(grep "First Load JS" bundle-analysis.txt | grep -oE '[0-9]+(\.[0-9]+)? kB' | head -1 | grep -oE '[0-9]+(\.[0-9]+)?')
            if [ -n "$BUNDLE_SIZE" ]; then
              echo "Bundle size: ${BUNDLE_SIZE}KB"
              if (( $(echo "$BUNDLE_SIZE > 150" | bc -l) )); then
                echo "Bundle size exceeds 150KB limit!"
              fi
            fi
          fi

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build-output
          path: .next/
          retention-days: 3
          compression-level: 6

  # Unit tests with coverage (Ubicloud for compute efficiency)
  test-unit:
    name: Unit Tests
    runs-on: ubicloud-standard-2
    timeout-minutes: 15

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run smart unit tests
        run: |
          echo "Running unit tests..."
          export NODE_OPTIONS="--max_old_space_size=4096 --max-semi-space-size=256"
          export NODE_ENV=test
          export NEXT_PUBLIC_DISABLE_OAUTH=true

          if [ "${{ github.event_name }}" == "pull_request" ]; then
            echo "Running affected tests for PR..."
            npm run test:changed -- --reporter=verbose || {
              echo "Affected tests failed, running full suite..."
              npx vitest run --coverage --config vitest.config.parallel.mjs
            }
          else
            echo "Running full test suite with coverage..."
            npx vitest run --coverage --config vitest.config.parallel.mjs
          fi
          echo "✅ Unit tests completed"

      - name: Upload coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: coverage/
          retention-days: 3

  # E2E Tests - Only on main/staging branches (Ubicloud for Playwright stability)
  e2e-tests:
    name: E2E Tests
    runs-on: ubicloud-standard-2
    timeout-minutes: 30
    needs: [validation]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging' || contains(github.event.pull_request.labels.*.name, 'full-test')

    env:
      USE_API_MOCK: '1'
      CI_FULL_SUITE: '1'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifact
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: build-output
          path: .next/

      - name: Verify build exists
        run: |
          if [ ! -d ".next" ]; then
            echo "Build artifact not found, running build..."
            npm run build
          fi
          echo "✅ Build ready for E2E tests"

      - name: Setup Playwright
        run: |
          echo "Setting up Playwright..."
          sudo apt-get update -qq
          npx playwright install --with-deps chromium
          sudo lsof -ti:3000 | xargs sudo kill -9 || true
          echo "✅ Playwright setup completed"

      - name: Start development server
        run: |
          echo "Starting development server..."
          npm run start &
          SERVER_PID=$!
          echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV

          for i in {1..30}; do
            if curl -f http://localhost:3000 >/dev/null 2>&1; then
              echo "✅ Server ready on port 3000"
              break
            fi
            echo "Waiting for server... ($i/30)"
            sleep 2
          done

      - name: Run E2E tests
        run: |
          echo "Running E2E test suite with API mocking..."
          npx playwright test --config=playwright.ci.optimized.config.ts --shard=1/2
        env:
          CI: true
          PLAYWRIGHT_RETRIES: 2

      - name: Stop development server
        if: always()
        run: |
          echo "Stopping development server..."
          if [ -n "$SERVER_PID" ]; then
            kill $SERVER_PID || true
          fi

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-results
          path: playwright-report/
          retention-days: 3

  # Quality gates and comprehensive summary (macOS for final reporting)
  summary:
    name: CI Summary & Quality Gates
    runs-on: [self-hosted, macos]
    timeout-minutes: 5
    needs: [validation, test-unit, e2e-tests]
    if: always()

    steps:
      - name: Download coverage
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          name: coverage-report
          path: coverage/

      - name: Generate comprehensive CI summary
        run: |
          echo "# DrMuscleWebApp CI Pipeline Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** \`${{ github.ref_name }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Commit:** \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## Job Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Job | Status | Duration | Runner |" >> $GITHUB_STEP_SUMMARY
          echo "|-----|--------|----------|--------|" >> $GITHUB_STEP_SUMMARY

          # Validation
          if [[ "${{ needs.validation.result }}" == "success" ]]; then
            echo "| Validation & Build | ✅ Passed | ~15min | macOS |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.validation.result }}" == "failure" ]]; then
            echo "| Validation & Build | 🔴 Failed | ~15min | macOS |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Validation & Build | Skipped | - | macOS |" >> $GITHUB_STEP_SUMMARY
          fi

          # Unit Tests
          if [[ "${{ needs.test-unit.result }}" == "success" ]]; then
            echo "| Unit Tests | ✅ Passed | ~15min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.test-unit.result }}" == "failure" ]]; then
            echo "| Unit Tests | 🔴 Failed | ~15min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Unit Tests | Skipped | - | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          fi

          # E2E Tests
          if [[ "${{ needs.e2e-tests.result }}" == "success" ]]; then
            echo "| E2E Tests | ✅ Passed | ~25min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          elif [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then
            echo "| E2E Tests | 🔴 Failed | ~25min | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| E2E Tests | Skipped (PR branch) | - | Ubicloud |" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY

          # Overall Status
          echo "## Overall Status" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          FAILED_JOBS=0
          if [[ "${{ needs.validation.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi
          if [[ "${{ needs.test-unit.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi
          if [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then ((FAILED_JOBS++)); fi

          if [[ $FAILED_JOBS -gt 0 ]]; then
            echo "### 🔴 **PIPELINE FAILED**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**$FAILED_JOBS job(s) failed**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Next steps:**" >> $GITHUB_STEP_SUMMARY
            echo "1. Check the failed job logs above" >> $GITHUB_STEP_SUMMARY
            echo "2. Fix the identified issues" >> $GITHUB_STEP_SUMMARY
            echo "3. Push your fixes to re-trigger the pipeline" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ✅ **PIPELINE PASSED**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "All required checks completed successfully!" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## CI Optimizations Active" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **API Mocking**: 60% faster E2E tests" >> $GITHUB_STEP_SUMMARY
          echo "- **Smart Testing**: E2E only on main/staging branches" >> $GITHUB_STEP_SUMMARY
          echo "- **Hybrid Runners**: macOS builds, Ubicloud tests" >> $GITHUB_STEP_SUMMARY
          echo "- **Parallel Execution**: Concurrent job execution" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "---" >> $GITHUB_STEP_SUMMARY
          echo "*Generated at $(date -u '+%Y-%m-%d %H:%M:%S UTC')*" >> $GITHUB_STEP_SUMMARY

      - name: Final status check
        run: |
          echo "Performing final status evaluation..."

          if [[ "${{ needs.validation.result }}" != "success" ||
                "${{ needs.test-unit.result }}" != "success" ]]; then
            echo "🔴 Core checks failed!"
            echo "- Validation: ${{ needs.validation.result }}"
            echo "- Unit tests: ${{ needs.test-unit.result }}"
            exit 1
          fi

          if [[ "${{ needs.e2e-tests.result }}" == "failure" ]]; then
            echo "🔴 E2E tests failed!"
            exit 1
          fi

          echo "✅ All required checks passed!"
