#!/usr/bin/env node

/**
 * Test script to verify WebKit fixes are working
 * This script tests the WebKit browser launch capability before running full tests
 */

const { webkit } = require('@playwright/test');

async function testWebKitLaunch() {
  console.log('🔧 Testing WebKit browser launch with new configuration...');
  
  let browser = null;
  let context = null;
  let page = null;
  
  try {
    // Test basic WebKit launch
    console.log('1. Testing basic WebKit launch...');
    browser = await webkit.launch({
      headless: true,
      timeout: 90000,
      slowMo: 100,
      args: [], // No Chrome-specific args
      env: {
        ...process.env,
        WEBKIT_DISABLE_COMPOSITING: '1',
        WEBKIT_FORCE_COMPOSITING_MODE: '0',
      },
      chromiumSandbox: false,
      handleSIGINT: false,
      handleSIGTERM: false,
      devtools: false,
    });
    
    console.log('✅ WebKit browser launched successfully');
    
    // Test context creation
    console.log('2. Testing context creation...');
    context = await browser.newContext({
      viewport: { width: 390, height: 844 },
      reducedMotion: 'reduce',
      serviceWorkers: 'block',
      locale: 'en-US',
      permissions: [],
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      javaScriptEnabled: true,
      strictSelectors: false,
    });
    
    console.log('✅ WebKit context created successfully');
    
    // Test page creation
    console.log('3. Testing page creation...');
    page = await context.newPage();
    
    console.log('✅ WebKit page created successfully');
    
    // Test navigation
    console.log('4. Testing navigation...');
    await page.goto('data:text/html,<html><body><h1>WebKit Test</h1></body></html>', {
      timeout: 30000,
    });
    
    const title = await page.textContent('h1');
    if (title === 'WebKit Test') {
      console.log('✅ WebKit navigation and content access working');
    } else {
      throw new Error('Navigation test failed - content not found');
    }
    
    console.log('🎉 All WebKit tests passed! The fixes are working.');
    return true;
    
  } catch (error) {
    console.error('❌ WebKit test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  } finally {
    // Cleanup
    try {
      if (page) await page.close();
      if (context) await context.close();
      if (browser) await browser.close();
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
  }
}

async function main() {
  console.log('🚀 Starting WebKit fixes verification...');
  console.log('Environment:', {
    NODE_ENV: process.env.NODE_ENV,
    CI: process.env.CI,
    WEBKIT_DISABLE_COMPOSITING: process.env.WEBKIT_DISABLE_COMPOSITING,
  });
  
  const success = await testWebKitLaunch();
  
  if (success) {
    console.log('\n✅ WebKit fixes verification completed successfully!');
    console.log('You can now run your E2E tests with improved WebKit stability.');
    process.exit(0);
  } else {
    console.log('\n❌ WebKit fixes verification failed!');
    console.log('Please check the error messages above and ensure:');
    console.log('1. Playwright browsers are installed: npx playwright install webkit');
    console.log('2. System dependencies are installed: npx playwright install-deps webkit');
    console.log('3. No other processes are using WebKit');
    process.exit(1);
  }
}

// Run the test
main().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
