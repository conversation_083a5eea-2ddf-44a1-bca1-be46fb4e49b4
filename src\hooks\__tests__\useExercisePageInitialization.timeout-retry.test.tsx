import { renderHook, waitFor, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'next/navigation'

vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  }),
}))

describe('useExercisePageInitialization - Timeout and Retry Logic', () => {
  const mockRouter = {
    replace: vi.fn(),
  }

  const mockLoadRecommendation = vi.fn()
  const mockUseWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [{ Id: 123, Label: 'Test Exercise' }],
          },
        ],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [{ Id: 123, Label: 'Test Exercise' }],
    workoutSession: { id: 'session-123' },
    loadRecommendation: mockLoadRecommendation,
    updateExerciseWorkSets: vi.fn(),
  }

  const mockUseWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  const mockUseAuthStore = {
    token: 'valid-token',
    isAuthenticated: true,
  }

  let coordinator: RecommendationLoadingCoordinator

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkout).mockReturnValue(mockUseWorkout as any)
    vi.mocked(useWorkoutStore).mockReturnValue(mockUseWorkoutStore as any)
    vi.mocked(useAuthStore).mockReturnValue(mockUseAuthStore as any)

    // Reset coordinator singleton
    RecommendationLoadingCoordinator['instance'] = null
    coordinator = RecommendationLoadingCoordinator.getInstance()
  })

  afterEach(() => {
    coordinator.reset()
    vi.useRealTimers()
  })

  it('should set timeout when starting recommendation loading', async () => {
    const exerciseId = 123
    const startLoadingSpy = vi.spyOn(coordinator, 'startLoading')

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(
      () => {
        expect(result.current.isInitializing).toBe(false)
      },
      { timeout: 5000 }
    )

    // Verify that startLoading was called with timeout option
    expect(startLoadingSpy).toHaveBeenCalledWith(exerciseId, { timeout: 30000 })
  })

  it('should track retry count correctly', async () => {
    const exerciseId = 123

    // Mock loadRecommendation to fail
    mockLoadRecommendation.mockRejectedValue(new Error('Network error'))

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(
      () => {
        expect(result.current.isInitializing).toBe(false)
      },
      { timeout: 5000 }
    )

    // Initial retry count should be 0
    expect(result.current.retryCount).toBe(0)

    // Trigger retry
    await act(async () => {
      await result.current.retryInitialization()
    })

    // Retry count should increment
    expect(result.current.retryCount).toBe(1)
  })

  it('should implement auto-login fallback after multiple failures', async () => {
    const exerciseId = 123

    // Mock loadRecommendation to fail with non-auth error
    const networkError = new Error('Network error')
    mockLoadRecommendation.mockRejectedValue(networkError)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(
      () => {
        expect(result.current.isInitializing).toBe(false)
      },
      { timeout: 5000 }
    )

    // Trigger retry 4 times to exceed the limit
    await act(async () => {
      await result.current.retryInitialization()
    })
    await act(async () => {
      await result.current.retryInitialization()
    })
    await act(async () => {
      await result.current.retryInitialization()
    })
    await act(async () => {
      await result.current.retryInitialization()
    })

    // After 3 failures, should redirect to login
    expect(mockRouter.replace).toHaveBeenCalledWith('/login')
    expect(result.current.retryCount).toBeGreaterThanOrEqual(3)
  })

  it('should handle immediate auth errors', async () => {
    const exerciseId = 123

    // Mock loadRecommendation to fail with 401 error
    const authError = new Error('Unauthorized')
    ;(authError as any).response = { status: 401 }
    mockLoadRecommendation.mockRejectedValue(authError)

    const { result } = renderHook(() =>
      useExercisePageInitialization(exerciseId)
    )

    await waitFor(
      () => {
        expect(result.current.isInitializing).toBe(false)
      },
      { timeout: 5000 }
    )

    // Trigger retry with auth error
    await act(async () => {
      await result.current.retryInitialization()
    })

    // Should redirect to login immediately on auth error
    expect(mockRouter.replace).toHaveBeenCalledWith('/login')
  })
})
