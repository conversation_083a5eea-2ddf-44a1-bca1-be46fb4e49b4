name: Test Ubicloud Runner

on:
  workflow_dispatch:

jobs:
  test-ubicloud:
    name: Test Ubicloud Runner
    runs-on: ubicloud-standard-2
    timeout-minutes: 5
    
    steps:
      - name: Test runner
        run: |
          echo "Testing ubicloud runner..."
          echo "Runner OS: $RUNNER_OS"
          echo "Runner architecture: $RUNNER_ARCH"
          uname -a
          echo "✅ Ubicloud runner is working!"
