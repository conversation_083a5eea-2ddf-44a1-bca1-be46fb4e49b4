import { test, expect } from '@playwright/test'

test.describe('Custom Duration Modal Mobile Fix', () => {
  test('modal should be visible and centered on mobile viewport', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Go directly to exercise page with mocked data
    await page.route('**/api/**', async (route) => {
      if (route.request().url().includes('/GetRecommendation')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            NbRepsInReserve: 2,
            Weight: { Kg: 50, Lb: 110 },
            Reps: 10,
            WeightIncrement: 5,
            Sets: 3,
          }),
        })
      } else if (route.request().url().includes('/exercise')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: 1,
            Label: 'Bench Press',
            IsFinished: false,
          }),
        })
      } else {
        await route.continue()
      }
    })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load with data
    await page.waitForSelector('input[placeholder*="Reps"]', { timeout: 10000 })

    // Fill in set data and save to trigger rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer
    await expect(page.getByTestId('rest-timer-container')).toBeVisible({
      timeout: 5000,
    })

    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Check modal is visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Get modal bounding box
    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBox && viewport) {
      // Modal should be within viewport
      expect(modalBox.x).toBeGreaterThanOrEqual(0)
      expect(modalBox.x + modalBox.width).toBeLessThanOrEqual(viewport.width)

      // Modal should be centered (with tolerance)
      const modalCenter = modalBox.x + modalBox.width / 2
      const viewportCenter = viewport.width / 2
      expect(Math.abs(modalCenter - viewportCenter)).toBeLessThan(30)

      // Modal should have margins
      expect(modalBox.x).toBeGreaterThanOrEqual(16)
      expect(
        viewport.width - (modalBox.x + modalBox.width)
      ).toBeGreaterThanOrEqual(16)
    }

    // Test input functionality
    await page.fill('#duration-input', '120')
    await page.click('button:has-text("Confirm")')

    // Modal should close
    await expect(modal).not.toBeVisible()
  })
})
