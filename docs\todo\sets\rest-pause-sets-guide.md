Rest-Pause Sets Implementation Guide for Dr. Muscle Web App
Overview
Rest-Pause is a training method where users perform a main set followed by multiple mini-sets with very short rest periods (15-30 seconds). The MAUI app implements this as an efficient alternative to traditional multiple sets.

Step 1: Understanding the Data Models
RecommendationModel Properties for Rest-Pause
typescript

interface RecommendationModel {
// Standard properties
Series: number; // Number of regular sets
Reps: number; // Reps for regular sets
Weight: MultiUnityWeight;

// Rest-Pause specific properties
NbPauses: number; // Number of rest-pause mini-sets
NbRepsPauses: number; // Reps for each rest-pause mini-set
RpRest: number; // Rest time between mini-sets (in seconds)

// Other properties...
}
WorkoutLogSerieModel for Saving Sets
typescript

interface WorkoutLogSerieModel {
Id: number;
Exercice: ExerciceModel;
Reps: number;
Weight: MultiUnityWeight;
IsWarmups: boolean;
NbPause: number; // Indicates if this is a rest-pause set
// Other properties...
}
Step 2: API Endpoints for Rest-Pause
Get Rest-Pause Recommendation
typescript

// Endpoint: POST /api/Exercise/GetRecommendationRestPauseForExercise
const getRestPauseRecommendation = async (exerciseId: number, workoutId: number) => {
const request: GetRecommendationForExerciseModel = {
Username: userEmail,
ExerciseId: exerciseId,
WorkoutId: workoutId,
IsQuickMode: false,
// Other properties...
};

const response = await apiClient.post(
'/api/Exercise/GetRecommendationRestPauseForExercise',
request
);

return response.data as RecommendationModel;
};
Step 3: Determining When to Use Rest-Pause
typescript

const shouldUseRestPause = async (): Promise<boolean> => {
// Check user's set style preference
const setStyle = await localDB.getSetting('SetStyle');

if (setStyle?.value === 'RestPause') {
return true;
}

// Check for specific conditions (e.g., bodyweight with pyramid)
const isPyramid = await localDB.getSetting('IsPyramid');
const currentExercise = getCurrentExercise();

if (isPyramid?.value === 'true' && currentExercise.IsBodyweight) {
return true;
}

return false;
};
Step 4: Generating Rest-Pause Sets
typescript

interface WorkoutSet {
setNumber: number;
weight: MultiUnityWeight;
reps: number;
isWarmup: boolean;
isRestPause: boolean;
setTitle?: string;
restTime?: number;
}

const generateRestPauseSets = (recommendation: RecommendationModel): WorkoutSet[] => {
const sets: WorkoutSet[] = [];
let setNumber = 1;

// Add warmup sets if needed
if (recommendation.WarmUpsList) {
recommendation.WarmUpsList.forEach((warmup, index) => {
sets.push({
setNumber: setNumber++,
weight: warmup.Weight,
reps: warmup.Reps,
isWarmup: true,
isRestPause: false,
setTitle: `Warm-up set ${index + 1}`
});
});
}

// Add main working set(s)
for (let i = 0; i < recommendation.Series; i++) {
sets.push({
setNumber: setNumber++,
weight: recommendation.Weight,
reps: recommendation.Reps,
isWarmup: false,
isRestPause: false,
setTitle: `Set ${i + 1}`,
restTime: 180 // Standard rest time in seconds
});
}

// Add rest-pause mini-sets
for (let j = 0; j < recommendation.NbPauses; j++) {
sets.push({
setNumber: setNumber++,
weight: recommendation.Weight,
reps: recommendation.NbRepsPauses,
isWarmup: false,
isRestPause: true,
setTitle: j === 0 ? "All right! Now let's try:" : `Rest-pause set ${j + 1}`,
restTime: recommendation.RpRest // Short rest time (15-30 seconds)
});
}

return sets;
};
Step 5: UI Display Components
Set Display Component
tsx

const RestPauseSetDisplay: React.FC<{ set: WorkoutSet }> = ({ set }) => {
return (

<div className={`set-container ${set.isRestPause ? 'rest-pause-set' : ''}`}>
<div className="set-header">
{set.isRestPause && (
<span className="rest-pause-indicator">
<Icon name="lightning" color="orange" /> Rest-pause set
</span>
)}
<span className="set-number">Set {set.setNumber}</span>
</div>

      <div className="set-details">
        <span className="weight">{set.weight.Value} {set.weight.Unit}</span>
        <span className="reps">× {set.reps} reps</span>
      </div>

      {set.setTitle && (
        <div className="set-title">{set.setTitle}</div>
      )}
    </div>

);
};
Timer Component for Rest-Pause
tsx

const RestPauseTimer: React.FC<{
restTime: number;
onComplete: () => void;
}> = ({ restTime, onComplete }) => {
const [timeLeft, setTimeLeft] = useState(restTime);

useEffect(() => {
if (timeLeft <= 0) {
onComplete();
return;
}

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);

}, [timeLeft, onComplete]);

return (

<div className="rest-pause-timer">
<h3>Rest-Pause Rest</h3>
<div className="timer-display">
{Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
</div>
<p>Get ready for the next mini-set!</p>
</div>
);
};
Step 6: Saving Rest-Pause Sets
typescript

const saveRestPauseSets = async (
exerciseId: number,
completedSets: WorkoutSet[]
): Promise<void> => {
// Convert to API format
const setLogs: WorkoutLogSerieModel[] = completedSets
.filter(set => !set.isWarmup) // Only save working sets
.map((set, index) => ({
Id: 0,
Exercice: { Id: exerciseId },
Reps: set.reps,
Weight: set.weight,
IsWarmups: false,
NbPause: set.isRestPause ? 1 : 0, // Mark rest-pause sets
LogDate: new Date().toISOString(),
// Other required properties...
}));

// Save all sets
await apiClient.post('/api/Exercise/AddWorkoutLogSerieListNew', setLogs);
};
Step 7: Complete Workout Flow
typescript

const RestPauseWorkoutFlow: React.FC<{ exercise: ExerciceModel }> = ({ exercise }) => {
const [recommendation, setRecommendation] = useState<RecommendationModel | null>(null);
const [sets, setSets] = useState<WorkoutSet[]>([]);
const [currentSetIndex, setCurrentSetIndex] = useState(0);
const [isResting, setIsResting] = useState(false);

useEffect(() => {
loadRecommendation();
}, [exercise]);

const loadRecommendation = async () => {
const isRestPause = await shouldUseRestPause();

    let reco: RecommendationModel;
    if (isRestPause) {
      reco = await getRestPauseRecommendation(exercise.Id, currentWorkoutId);
    } else {
      reco = await getNormalRecommendation(exercise.Id, currentWorkoutId);
    }

    setRecommendation(reco);
    setSets(generateRestPauseSets(reco));

};

const completeSet = async (actualReps: number) => {
// Update the current set with actual reps
const updatedSets = [...sets];
updatedSets[currentSetIndex] = {
...updatedSets[currentSetIndex],
reps: actualReps
};
setSets(updatedSets);

    // Check if there are more sets
    if (currentSetIndex < sets.length - 1) {
      setIsResting(true);
    } else {
      // All sets complete, save to server
      await saveRestPauseSets(exercise.Id, updatedSets);
      // Navigate to next exercise
    }

};

const onRestComplete = () => {
setIsResting(false);
setCurrentSetIndex(prev => prev + 1);
};

if (!recommendation || sets.length === 0) {
return <LoadingSpinner />;
}

const currentSet = sets[currentSetIndex];

return (

<div className="workout-container">
<ExerciseHeader exercise={exercise} />

      {isResting ? (
        <RestPauseTimer
          restTime={currentSet.restTime || 180}
          onComplete={onRestComplete}
        />
      ) : (
        <div>
          <RestPauseSetDisplay set={currentSet} />
          <SetCompletionForm
            onComplete={completeSet}
            suggestedReps={currentSet.reps}
          />
        </div>
      )}

      <SetProgressIndicator
        totalSets={sets.length}
        currentSet={currentSetIndex + 1}
      />
    </div>

);
};
Step 8: User Education
typescript

const RestPauseInfoModal: React.FC = () => {
return (
<Modal>

<h2>Rest-Pause Sets</h2>
<p>
Save time with rest-pause. Short rests, fewer reps.
Scientists have found that 1 rest-pause set is just as
effective as 3 normal sets.
</p>
<ul>
<li>Perform your main set</li>
<li>Rest for 15-30 seconds</li>
<li>Perform mini-sets with fewer reps</li>
<li>Repeat for all mini-sets</li>
</ul>
</Modal>
);
};
Key Implementation Considerations
Set Style Detection: Always check user preferences and exercise type before using Rest-Pause
Timer Accuracy: Ensure rest timers are accurate and continue running if app is backgrounded
Set Tracking: Clearly differentiate between regular sets and rest-pause mini-sets in the UI
Data Persistence: Save all sets (including mini-sets) as separate entries to maintain workout history
Visual Indicators: Use clear visual cues (colors, icons) to indicate rest-pause sets
User Education: Show explanatory tooltips/modals for users unfamiliar with rest-pause training
Testing Checklist
Verify correct API endpoint is called based on set style preference
Confirm proper generation of warmup + working + rest-pause sets
Test timer functionality for short rest periods
Validate all sets are saved correctly to the server
Check UI clearly indicates rest-pause sets vs normal sets
Test switching between normal and rest-pause modes
Verify workout history displays rest-pause sets appropriately

Answers to Rest-Pause Implementation Questions
Based on my analysis of the mobile app code, here are the detailed answers:

1. API Response Validation
   Typical Values:
   Series: For rest-pause, this is typically 1 (one main working set)
   NbPauses: Usually 2-4 (common values are 2 or 3)
   Example response structure:
   json

{
"Series": 1,
"Reps": 12,
"Weight": {"Value": 50, "Unit": "kg"},
"NbPauses": 3,
"NbRepsPauses": 8,
"RpRest": 15,
"IsNormalSets": false,
"IsPyramid": false,
"IsReversePyramid": false
} 2. Set Generation Logic
The total number of sets displayed = Series + NbPauses

From the code at KenkoSingleExercisePage.xaml.cs:

Lines 4960-5246: Generate normal working sets (Series count)
Lines 5247-5337: Generate rest-pause mini-sets (NbPauses count)
Each rest-pause set is displayed as a separate row in the UI.

Set Labeling:
First work set: "1st work set" or coach tip
Rest-pause sets: "Rest-pause set" with orange header image 3. Rest Time Between Sets
First normal set: Standard rest time (typically 180-300 seconds based on exercise)
Rest-pause sets: Use RpRest value (15-30 seconds)
The rest timer is NOT automatically triggered - user must complete the set first 4. Set Properties
First Set (Normal Work Set):
csharp

{
Reps: recommendation.Reps,
Weight: recommendation.Weight,
IsWarmups: false,
SetTitle: "1st work set",
IsFirstWorkSet: true,
// Standard rest time applies
}
Rest-Pause Sets:
csharp

{
Reps: recommendation.NbRepsPauses, // Different rep count!
Weight: recommendation.Weight, // Same weight as main set
IsWarmups: false,
SetTitle: "Rest-pause set",
HeaderImage: "orange.png",
// RpRest time applies after completion
} 5. Debugging Help
Key Conditions to Check:
Rest-Pause Detection (line 5288):
csharp

if (m.RecoModel.IsNormalSets == false &&
m.RecoModel.IsPyramid == false &&
m.RecoModel.IsReversePyramid == false &&
rec.IsBackOffSet == false)
{
// This is a rest-pause set
}
NbPauses can be 0: The loop at line 5247 runs from 0 to NbPauses, so if NbPauses is 0, no rest-pause sets are added

No filtering: The mobile app doesn't filter out rest-pause sets - they're all added to the display list

6. Example Scenario Answer
   Given:

Series: 1
NbPauses: 3
Reps: 12
NbRepsPauses: 8
RpRest: 15
User should see 4 total sets:

Set 1: 12 reps @ full weight (normal work set)
Set 2: 8 reps @ full weight (rest-pause set, 15s rest after)
Set 3: 8 reps @ full weight (rest-pause set, 15s rest after)
Set 4: 8 reps @ full weight (rest-pause set, 15s rest after) 7. SetStyle vs IsNormalSets
Primary check: Use IsNormalSets == false combined with checking pyramid flags
SetStyle: This is a user preference stored in settings
Authoritative source: The combination check in the code (IsNormalSets == false && IsPyramid == false && IsReversePyramid == false) 8. User Experience
Visual Distinction:
Orange header image for rest-pause sets
"Rest-pause set" title
Popup explanation on first encounter
Rest Timer:
NOT automatic - user must tap to complete set first
After completion, short rest timer (RpRest) is shown
Timer can be skipped if user is ready
What You Might Be Missing
Based on the code analysis, here's what might cause the "only 1 set" issue:

Check the loop boundaries: The rest-pause generation loop should be:
javascript

for (let j = 0; j < recommendation.NbPauses; j++) {
// Generate rest-pause set
}
Verify NbPauses value: Log the actual NbPauses value from the API response - it might be 0 or undefined

Check the conditional logic: Ensure you're checking:

javascript

if (!recommendation.IsNormalSets &&
!recommendation.IsPyramid &&
!recommendation.IsReversePyramid) {
// This is rest-pause mode
}
Set numbering: The mobile app uses setList.Where(x=>x.IsWarmups == false).Count() + 1 for set numbers

Important: The first loop generates Series number of sets, then the second loop adds NbPauses additional sets. Make sure both loops are executing.

The most likely issue is that either NbPauses is 0/undefined in your API response, or the rest-pause generation loop isn't executing due to a conditional check.
