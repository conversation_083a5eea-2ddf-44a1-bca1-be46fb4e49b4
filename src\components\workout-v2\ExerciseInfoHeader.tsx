'use client'

interface ExerciseInfoHeaderProps {
  currentSet: number
  totalSets: number
  completedSets?: number
}

export function ExerciseInfoHeader({
  currentSet,
  totalSets,
  completedSets,
}: ExerciseInfoHeaderProps) {
  // If completedSets is provided, use it for progress. Otherwise derive from currentSet
  const completed = completedSets !== undefined ? completedSets : currentSet - 1
  const progressPercentage = totalSets > 0 ? (completed / totalSets) * 100 : 0

  return (
    <div className="px-4 py-3 bg-bg-primary" data-testid="exercise-info-header">
      <div className="flex items-center gap-3">
        <span className="text-lg text-text-secondary">
          Set {currentSet} of {totalSets}
        </span>
        <div
          className="flex-1 h-1 bg-surface-secondary rounded-full"
          data-testid="exercise-progress-bar"
        >
          <div
            className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  )
}
