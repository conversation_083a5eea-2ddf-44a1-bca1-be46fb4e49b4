Drop Sets Implementation Guide for Dr. Muscle Web App
Overview
Drop sets are a training technique where you perform a set to near failure, then immediately reduce the weight and continue for additional reps. The MAUI app implements drop sets with automatic 10% weight reductions between sets, allowing users to achieve muscle exhaustion efficiently.

Step 1: Understanding the Data Models
RecommendationModel Properties for Drop Sets
typescript

interface RecommendationModel {
// Standard properties
Series: number; // Number of total sets (including drop sets)
Reps: number; // Base reps for sets
Weight: MultiUnityWeight;

// Drop Set specific property
IsDropSet: boolean; // Indicates if drop sets should be used

// Other properties...
}
UserInfosModel for User Preferences
typescript

interface UserInfosModel {
// User profile properties...

// Training style preferences
IsDropSet: boolean; // User's drop set preference
IsPyramid: boolean; // Pyramid set preference (mutually exclusive)
IsNormalSets: boolean; // Normal sets preference

// Other properties...
}
WorkoutLogSerieModel for Saving Sets
typescript

interface WorkoutLogSerieModel {
Id: number;
Exercice: ExerciceModel;
Reps: number;
Weight: MultiUnityWeight;
IsWarmups: boolean;
// Note: Drop set status is implicit from the set sequence
// Other properties...
}
Step 2: Drop Set Configuration
Checking User Preferences
typescript

const isDropSetEnabled = async (): Promise<boolean> => {
// Check user's training style preference
const userInfo = await getUserInfo();

// Drop sets are mutually exclusive with pyramid and normal sets
if (userInfo.IsDropSet && !userInfo.IsPyramid && !userInfo.IsNormalSets) {
return true;
}

return false;
};
Setting Drop Set Preference
typescript

const setDropSetPreference = async (enabled: boolean): Promise<void> => {
const userInfo = await getUserInfo();

// Update user preferences
const updatedInfo: UserInfosModel = {
...userInfo,
IsDropSet: enabled,
IsPyramid: false, // Disable pyramid if enabling drop sets
IsNormalSets: false // Disable normal sets if enabling drop sets
};

// Save via API
await apiClient.post('/api/Account/SetUserSetStyle', updatedInfo);
};
Step 3: Drop Set Weight Calculations
Core Weight Reduction Logic
typescript

const calculateDropSetWeight = (
previousWeight: MultiUnityWeight,
isAssisted: boolean = false,
dropPercentage: number = 0.1 // 10% default
): MultiUnityWeight => {
const weightValue = previousWeight.Value;

let newWeight: number;
if (isAssisted) {
// For assisted exercises (e.g., band-assisted pull-ups), increase assistance
newWeight = weightValue + (weightValue _ dropPercentage);
} else {
// For normal exercises, reduce weight
newWeight = weightValue - (weightValue _ dropPercentage);
}

// Ensure minimum weight constraints
const minWeight = getMinimumWeight(previousWeight.Unit);
newWeight = Math.max(newWeight, minWeight);

return {
Value: Math.round(newWeight \* 2) / 2, // Round to nearest 0.5
Unit: previousWeight.Unit
};
};

const getMinimumWeight = (unit: string): number => {
// Minimum weights based on equipment
switch (unit) {
case 'kg':
return 2.5; // Minimum 2.5kg
case 'lb':
return 5; // Minimum 5lb
default:
return 0;
}
};
Step 4: Generating Drop Sets
typescript

interface WorkoutSet {
setNumber: number;
weight: MultiUnityWeight;
reps: number;
isWarmup: boolean;
isDropSet: boolean;
setTitle?: string;
headerImage?: string;
}

const generateDropSets = (
recommendation: RecommendationModel,
exercise: ExerciceModel
): WorkoutSet[] => {
const sets: WorkoutSet[] = [];
let setNumber = 1;

// Add warmup sets if needed
if (recommendation.WarmUpsList) {
recommendation.WarmUpsList.forEach((warmup, index) => {
sets.push({
setNumber: setNumber++,
weight: warmup.Weight,
reps: warmup.Reps,
isWarmup: true,
isDropSet: false,
setTitle: `Warm-up set ${index + 1}`
});
});
}

// Calculate reps for drop sets
let dropSetReps = recommendation.Reps;
if (recommendation.Reps <= 5) {
// For low rep ranges, use 1/3 of the reps (rounded up)
dropSetReps = Math.ceil(recommendation.Reps / 3);
} else {
// For higher rep ranges, use 1/3 of the reps (rounded down)
dropSetReps = Math.floor(recommendation.Reps / 3);
}

// Add main working set
sets.push({
setNumber: setNumber++,
weight: recommendation.Weight,
reps: dropSetReps, // First set uses calculated reps
isWarmup: false,
isDropSet: false,
setTitle: "First work set"
});

// Add drop sets (typically 2-3 additional sets)
let currentWeight = recommendation.Weight;
const dropSetCount = recommendation.Series - 1; // Remaining sets are drop sets

for (let i = 0; i < dropSetCount; i++) {
// Calculate reduced weight
currentWeight = calculateDropSetWeight(
currentWeight,
exercise.IsAssisted
);

    sets.push({
      setNumber: setNumber++,
      weight: currentWeight,
      reps: dropSetReps, // Same reps for all drop sets
      isWarmup: false,
      isDropSet: true,
      setTitle: "Drop set",
      headerImage: "orange" // Visual indicator
    });

}

return sets;
};
Step 5: UI Display Components
Drop Set Display Component
tsx

const DropSetDisplay: React.FC<{ set: WorkoutSet }> = ({ set }) => {
return (

<div className={`set-container ${set.isDropSet ? 'drop-set' : ''}`}>
<div className="set-header">
{set.isDropSet && (
<span className="drop-set-indicator">
<Icon name="arrow-down" color="orange" /> Drop set
</span>
)}
<span className="set-number">Set {set.setNumber}</span>
</div>

      <div className="set-details">
        <span className="weight">
          {set.weight.Value} {set.weight.Unit}
          {set.isDropSet && (
            <span className="weight-reduction"> (-10%)</span>
          )}
        </span>
        <span className="reps">× {set.reps} reps</span>
      </div>

      {set.setTitle && (
        <div className="set-title">{set.setTitle}</div>
      )}
    </div>

);
};
Drop Set Information Modal
tsx

const DropSetInfoModal: React.FC<{ onClose: () => void }> = ({ onClose }) => {
return (
<Modal onClose={onClose}>

<h2>Drop Sets</h2>
<p>
Do a few more reps with a lower weight. Studies show similar
hypertrophy in 1/3 the time.
</p>
<div className="drop-set-explanation">
<h3>How it works:</h3>
<ol>
<li>Perform your first set with regular weight</li>
<li>Immediately reduce weight by 10%</li>
<li>Continue with the same number of reps</li>
<li>Repeat for all drop sets</li>
</ol>
</div>
<div className="modal-actions">
<Button onClick={onClose}>Got it</Button>
<Link href="https://dr-muscle.com/use-drop-sets-build-muscle/">
Learn more
</Link>
</div>
</Modal>
);
};
Step 6: Drop Set Workout Flow
typescript

const DropSetWorkoutFlow: React.FC<{ exercise: ExerciceModel }> = ({ exercise }) => {
const [sets, setSets] = useState<WorkoutSet[]>([]);
const [currentSetIndex, setCurrentSetIndex] = useState(0);
const [showDropSetInfo, setShowDropSetInfo] = useState(false);

useEffect(() => {
loadDropSets();
}, [exercise]);

const loadDropSets = async () => {
const isDropSet = await isDropSetEnabled();

    if (!isDropSet) {
      // Load normal sets instead
      return;
    }

    // Get recommendation
    const recommendation = await getExerciseRecommendation(exercise.Id);

    // Generate drop sets
    const dropSets = generateDropSets(recommendation, exercise);
    setSets(dropSets);

    // Show info modal for first-time users
    const hasSeenDropSetInfo = await localDB.getSetting('HasSeenDropSetInfo');
    if (!hasSeenDropSetInfo) {
      setShowDropSetInfo(true);
      await localDB.setSetting('HasSeenDropSetInfo', 'true');
    }

};

const completeSet = async (actualReps: number) => {
// Update current set with actual reps
const updatedSets = [...sets];
updatedSets[currentSetIndex] = {
...updatedSets[currentSetIndex],
reps: actualReps
};
setSets(updatedSets);

    // Move to next set
    if (currentSetIndex < sets.length - 1) {
      // For drop sets, minimal rest between sets
      if (sets[currentSetIndex + 1].isDropSet) {
        // Quick transition animation
        await animateWeightChange();
      }
      setCurrentSetIndex(prev => prev + 1);
    } else {
      // All sets complete
      await saveDropSets(exercise.Id, updatedSets);
      navigateToNextExercise();
    }

};

const animateWeightChange = async () => {
// Show quick weight reduction animation
return new Promise(resolve => setTimeout(resolve, 1000));
};

const currentSet = sets[currentSetIndex];

return (

<div className="workout-container">
<ExerciseHeader exercise={exercise} />

      {currentSet && (
        <>
          <DropSetDisplay set={currentSet} />

          {currentSet.isDropSet && currentSetIndex > 0 && (
            <div className="drop-set-transition">
              <Icon name="lightning" color="orange" />
              <span>Quick! Reduce weight and continue</span>
            </div>
          )}

          <SetCompletionForm
            onComplete={completeSet}
            suggestedReps={currentSet.reps}
            showTimer={!currentSet.isDropSet} // No timer for drop sets
          />
        </>
      )}

      <SetProgressIndicator
        totalSets={sets.length}
        currentSet={currentSetIndex + 1}
        isDropSet={currentSet?.isDropSet}
      />

      {showDropSetInfo && (
        <DropSetInfoModal onClose={() => setShowDropSetInfo(false)} />
      )}
    </div>

);
};
Step 7: Saving Drop Sets
typescript

const saveDropSets = async (
exerciseId: number,
completedSets: WorkoutSet[]
): Promise<void> => {
// Filter out warmup sets and prepare for API
const workingSets = completedSets
.filter(set => !set.isWarmup)
.map((set, index) => ({
Id: 0,
Exercice: { Id: exerciseId },
Reps: set.reps,
Weight: set.weight,
IsWarmups: false,
LogDate: new Date().toISOString(),
// Additional properties as required
}));

// Save all sets at once
await apiClient.post('/api/Exercise/AddWorkoutLogSerieListNew', workingSets);
};
Step 8: Settings UI for Drop Sets
tsx

const DropSetSettings: React.FC = () => {
const [userInfo, setUserInfo] = useState<UserInfosModel | null>(null);

useEffect(() => {
loadUserInfo();
}, []);

const loadUserInfo = async () => {
const info = await getUserInfo();
setUserInfo(info);
};

const handleSetStyleChange = async (style: 'normal' | 'pyramid' | 'dropset') => {
if (!userInfo) return;

    const updatedInfo = {
      ...userInfo,
      IsNormalSets: style === 'normal',
      IsPyramid: style === 'pyramid',
      IsDropSet: style === 'dropset'
    };

    await apiClient.post('/api/Account/SetUserSetStyle', updatedInfo);
    setUserInfo(updatedInfo);

};

return (

<div className="settings-section">
<h3>Set Style</h3>
<RadioGroup
value={
userInfo?.IsDropSet ? 'dropset' :
userInfo?.IsPyramid ? 'pyramid' :
'normal'
}
onChange={handleSetStyleChange} >
<RadioOption value="normal">
Normal sets
<span className="description">Traditional sets with consistent weight</span>
</RadioOption>
<RadioOption value="pyramid">
Pyramid sets
<span className="description">Progressive weight/rep changes</span>
</RadioOption>
<RadioOption value="dropset">
Drop sets
<span className="description">10% weight reduction between sets</span>
</RadioOption>
</RadioGroup>
</div>
);
};
Key Implementation Considerations
Weight Calculations: Always ensure 10% reduction is applied correctly, with minimum weight constraints
Rep Adjustments: First set uses 1/3 of recommended reps (rounded appropriately)
Assisted Exercises: Reverse the weight calculation for band-assisted movements
User Education: Show explanatory modal on first use
Quick Transitions: Minimize rest between drop sets (immediate weight change)
Visual Indicators: Use orange color and down arrow icon for drop sets
Settings Management: Ensure mutual exclusivity with pyramid and normal sets
Differences from Other Set Types
Feature Drop Sets Pyramid Sets Rest-Pause Normal Sets
Weight Change -10% each set Variable Same weight Same weight
Reps Constant (1/3 of base) Progressive Variable mini-sets Constant
Rest Time Minimal Normal 15-30 sec Normal
Total Sets 3-4 typically 3-5 1 + mini-sets 3-5
Purpose Muscle exhaustion Progressive overload Time efficiency Standard training
Testing Checklist
Verify 10% weight reduction calculations
Test minimum weight constraints
Confirm rep calculations (1/3 of base reps)
Test assisted exercise weight increases
Validate settings mutual exclusivity
Check drop set visual indicators
Test quick transitions between sets
Verify all sets saved correctly
Test switching between set styles
Confirm user education modal appears
