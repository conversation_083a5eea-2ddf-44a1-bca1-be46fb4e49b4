import { test, expect } from '@playwright/test'
import { <PERSON>ginHelper } from './helpers/login-helper'
import { startWorkout, getFirstExercise } from './helpers/workout-helper'

test.describe('Exercise V2 Timer Scroll Behavior', () => {
  let loginHelper: LoginHelper

  test.beforeEach(async ({ page }) => {
    loginHelper = new LoginHelper(page)
    await page.goto('/login')
    await loginHelper.login()
    await page.waitForURL('/program')
  })

  test('should maintain scrollability when timer is active', async ({
    page,
  }) => {
    // Given: User has started a workout and navigated to exercise V2 page
    await startWorkout(page)
    const firstExercise = await getFirstExercise(page)
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Scroll to bottom to see Today's Sets
    await page.evaluate(() => {
      const container = document.querySelector('.overflow-y-auto')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    })

    // Verify Today's Sets is visible before timer
    const todaysSets = page.locator('[data-testid="todays-sets-container"]')
    await expect(todaysSets).toBeVisible()

    // Get the position of Today's Sets before timer
    const todaysSetsBoxBefore = await todaysSets.boundingBox()
    if (!todaysSetsBoxBefore) throw new Error("Today's Sets not found")

    // When: User saves a set to trigger timer
    await page.click('button:has-text("Save set")')
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Then: Should still be able to scroll to see Today's Sets
    await page.evaluate(() => {
      const container = document.querySelector('.overflow-y-auto')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    })

    // Verify Today's Sets is still visible
    await expect(todaysSets).toBeVisible()

    // Get timer height
    const timerContainer = page.locator('[data-testid="rest-timer-container"]')
    const timerBox = await timerContainer.boundingBox()
    if (!timerBox) throw new Error('Timer container not found')

    // Verify Today's Sets is above the timer (not covered by it)
    const todaysSetsBoxAfter = await todaysSets.boundingBox()
    if (!todaysSetsBoxAfter)
      throw new Error("Today's Sets not found after timer")

    // Today's Sets bottom should be above timer top
    const todaysSetsBottom = todaysSetsBoxAfter.y + todaysSetsBoxAfter.height
    const timerTop = timerBox.y

    expect(todaysSetsBottom).toBeLessThanOrEqual(timerTop)
  })

  test('should adjust content padding when timer appears and disappears', async ({
    page,
  }) => {
    // Given: User is on exercise V2 page
    await startWorkout(page)
    const firstExercise = await getFirstExercise(page)
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Get initial padding of scrollable container
    const scrollableContainer = page.locator('.overflow-y-auto').first()
    const initialPadding = await scrollableContainer.evaluate((el) => {
      return window.getComputedStyle(el).paddingBottom
    })

    // When: Timer becomes active
    await page.click('button:has-text("Save set")')
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // Then: Padding should increase
    const paddingWithTimer = await scrollableContainer.evaluate((el) => {
      return window.getComputedStyle(el).paddingBottom
    })

    expect(parseInt(paddingWithTimer)).toBeGreaterThan(parseInt(initialPadding))

    // When: Timer is hidden
    await page.click('button:has-text("Hide")')
    await expect(
      page.locator('[data-testid="rest-timer-container"]')
    ).not.toBeVisible()

    // Then: Padding should return to initial value
    const paddingAfterHide = await scrollableContainer.evaluate((el) => {
      return window.getComputedStyle(el).paddingBottom
    })

    expect(paddingAfterHide).toBe(initialPadding)
  })

  test('should allow interaction with bottom content when timer is active', async ({
    page,
  }) => {
    // Given: User is on exercise V2 page with timer active
    await startWorkout(page)
    const firstExercise = await getFirstExercise(page)
    await page.goto(`/workout/exercise-v2/${firstExercise.id}`)
    await page.waitForSelector('[data-testid="current-set-card"]')

    // Trigger timer
    await page.click('button:has-text("Save set")')
    await page.waitForSelector('[data-testid="rest-timer-container"]')

    // When: Scrolling to bottom
    await page.evaluate(() => {
      const container = document.querySelector('.overflow-y-auto')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    })

    // Then: Should be able to interact with Today's Sets
    const todaysSets = page.locator('[data-testid="todays-sets-container"]')
    await expect(todaysSets).toBeVisible()

    // Verify sets are clickable/viewable
    const firstSet = todaysSets.locator('[data-testid^="set-row-"]').first()
    await expect(firstSet).toBeVisible()

    // Get the bounding box to ensure it's not covered
    const setBox = await firstSet.boundingBox()
    const timerBox = await page
      .locator('[data-testid="rest-timer-container"]')
      .boundingBox()

    if (setBox && timerBox) {
      // Verify set is above timer
      expect(setBox.y + setBox.height).toBeLessThanOrEqual(timerBox.y)
    }
  })
})
