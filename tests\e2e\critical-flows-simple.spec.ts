import { test, expect } from '@playwright/test'

// Simplified setup that bypasses login and goes directly to program page
async function setupAuthenticatedSession(page: any) {
  // Mock all necessary APIs for authenticated session
  await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Test Program',
            RemainingToLevelUp: 3,
          },
          NextWorkoutTemplate: {
            Id: 101,
            Label: 'Workout A',
            IsSystemExercise: false,
          },
        },
        TotalWorkoutCompleted: 10,
        ConsecutiveWeeks: 3,
      }),
    })
  })

  // Mock user info API
  await page.route('**/api/Account/GetUserInfo*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Email: '<EMAIL>',
        FirstName: 'Test',
        LastName: 'User',
      }),
    })
  })

  // Mock auth token API (for cookie-based authentication)
  await page.route('**/api/auth/token*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        token: 'mock-jwt-token',
        authenticated: true,
      }),
    })
  })

  // Set authentication state in Zustand store to simulate logged-in state
  await page.addInitScript(() => {
    // Set the Zustand auth store state
    const authState = {
      state: {
        user: {
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        hasHydrated: true,
        cachedUserInfo: null,
        cacheVersion: 1,
        lastActivity: Date.now(),
      },
      version: 0,
    }
    localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
  })

  // Navigate directly to program page
  await page.goto('/program')
  await page.waitForLoadState('domcontentloaded')

  // Verify we're on the program page
  await expect(page).toHaveURL('/program')
}

test.describe('Critical E2E Flows - Simplified', () => {
  test('Login and start workout', async ({ page }) => {
    // Setup authenticated session (bypass login for now)
    await setupAuthenticatedSession(page)

    // Verify we're on program page
    await expect(page).toHaveURL('/program')

    // Wait for program page to be fully loaded
    await page.waitForLoadState('networkidle')

    // Wait for program data to load (indicated by the presence of the Open Workout button)
    await page.waitForSelector('button:has-text("Open Workout")', {
      timeout: 10000,
    })

    // Start workout - find the "Open Workout" button (FloatingCTAButton)
    const startButton = page
      .getByRole('button', { name: /open workout/i })
      .first()
    await expect(startButton).toBeVisible({ timeout: 5000 })

    // Debug: Clicking Open Workout button

    // Set up navigation monitoring
    const navigationPromise = page.waitForURL(/\/workout/, { timeout: 15000 })

    await startButton.click()

    // Wait for navigation to workout area (could be /workout or /workout/exercise/[id])
    await navigationPromise

    // Verify we reached workout area (either overview or exercise page)
    await expect(page).toHaveURL(/\/workout/)

    // Verify navigation completed successfully
    const currentUrl = page.url()
    expect(currentUrl).toMatch(/\/workout/)
  })

  test('Complete a set', async ({ page }) => {
    // Login and navigate to workout
    await login(page)

    // Start workout
    const startButton = page
      .getByRole('button', { name: /start|continue/i })
      .first()
    await startButton.click()
    await page.waitForURL(/\/workout/, { timeout: 10000 })

    // Click first exercise
    const firstExercise = page.locator('[data-testid*="exercise"]').first()
    await firstExercise.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise/, { timeout: 10000 })

    // Find and click save button
    const saveButton = page.getByRole('button', { name: /save/i }).first()
    await expect(saveButton).toBeVisible({ timeout: 10000 })
    await saveButton.click()

    // Verify save was successful (button changes or navigation occurs)
    await page.waitForTimeout(2000) // Give time for save to complete
  })
})
