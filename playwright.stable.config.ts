// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'

/**
 * Stable Playwright configuration for reliable E2E testing
 * Optimized to prevent timeouts and resource contention issues
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Run tests in files in parallel but limit concurrency */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 1,
  /* Use single worker to prevent resource contention */
  workers: 1,
  /* Reporter to use */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['list'],
  ],
  /* Global timeout for each test */
  timeout: 60000,
  /* Timeout for each action */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
    /* Video on failure */
    video: 'retain-on-failure',
    /* Timeout for each action */
    actionTimeout: 15000,
    /* Navigation timeout */
    navigationTimeout: 30000,
  },

  /* Configure projects for stable testing */
  projects: [
    /* Desktop Chrome - Most stable */
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        /* Increase timeouts for stability */
        actionTimeout: 20000,
        navigationTimeout: 40000,
      },
    },
    /* Mobile Chrome - Secondary priority */
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
        /* Increase timeouts for mobile */
        actionTimeout: 25000,
        navigationTimeout: 45000,
      },
    },
  ],

  /* Run local dev server before tests with extended timeout */
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: true,
    timeout: 180000, // 3 minutes for server startup
    /* Add environment variables for stable testing */
    env: {
      NODE_ENV: 'test',
      NEXT_PUBLIC_API_URL: 'https://drmuscle.azurewebsites.net',
      NEXT_PUBLIC_APP_ENV: 'development'
    }
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),
})
