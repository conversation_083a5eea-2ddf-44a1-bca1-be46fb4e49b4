#!/usr/bin/env node

const { spawn, execSync } = require('child_process');
const http = require('http');
const fs = require('fs');
const path = require('path');

const SERVER_PORT = 3000;
const PID_FILE = path.join(__dirname, '..', '.test-server.pid');
const SERVER_START_TIMEOUT = 60000; // 60 seconds
const CHECK_INTERVAL = 500; // Check every 500ms

// Check if server is already running
async function isServerRunning() {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${SERVER_PORT}`, (res) => {
      resolve(res.statusCode === 200 || res.statusCode === 304);
    });
    
    req.on('error', () => resolve(false));
    req.setTimeout(1000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

// Get PID from file
function getStoredPid() {
  try {
    if (fs.existsSync(PID_FILE)) {
      return parseInt(fs.readFileSync(PID_FILE, 'utf8').trim());
    }
  } catch (e) {
    // Ignore errors
  }
  return null;
}

// Check if process is running
function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

// Wait for server to be ready
async function waitForServer(timeout = SERVER_START_TIMEOUT) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await isServerRunning()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL));
  }
  
  return false;
}

// Start the dev server
async function startServer() {
  console.log('🚀 Starting dev server for tests...');
  
  // Start server in background
  const serverProcess = spawn('npm', ['run', 'dev'], {
    detached: true,
    stdio: 'ignore',
    env: {
      ...process.env,
      BROWSER: 'none', // Don't open browser
      CI: 'true', // Disable interactive features
    }
  });
  
  // Unref so parent can exit
  serverProcess.unref();
  
  // Save PID
  fs.writeFileSync(PID_FILE, serverProcess.pid.toString());
  
  // Wait for server to be ready
  if (await waitForServer()) {
    console.log('✅ Dev server is ready!');
    return true;
  } else {
    console.error('❌ Dev server failed to start in time');
    // Try to kill the process
    try {
      process.kill(serverProcess.pid);
    } catch (e) {
      // Ignore
    }
    return false;
  }
}

// Main function
async function main() {
  // Check if server is already running
  if (await isServerRunning()) {
    console.log('✅ Dev server is already running');
    process.exit(0);
  }
  
  // Check if we have a stored PID and if that process is still running
  const storedPid = getStoredPid();
  if (storedPid && isProcessRunning(storedPid)) {
    console.log('⏳ Dev server process exists, waiting for it to be ready...');
    if (await waitForServer(10000)) { // Give it 10 seconds
      console.log('✅ Dev server became ready');
      process.exit(0);
    }
  }
  
  // Start the server
  const success = await startServer();
  process.exit(success ? 0 : 1);
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  const pid = getStoredPid();
  if (pid) {
    try {
      process.kill(pid);
      fs.unlinkSync(PID_FILE);
    } catch (e) {
      // Ignore
    }
  }
  process.exit(0);
});

main().catch(console.error);