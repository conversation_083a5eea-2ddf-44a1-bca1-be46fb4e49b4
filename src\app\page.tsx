'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'

export default function Home() {
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/program')
      return
    }

    // Delay redirect to login to show animation
    const delay = process.env.NODE_ENV === 'test' ? 0 : 400
    const timer = setTimeout(() => {
      router.replace('/login')
    }, delay)

    return () => clearTimeout(timer)
  }, [isAuthenticated, router])

  return (
    <main className="flex min-h-[100dvh] flex-col items-center justify-center p-24">
      <div className="animate-pulse">
        <h1 className="text-2xl md:text-4xl font-bold text-gradient-gold whitespace-nowrap">
          Dr. Muscle X
        </h1>
        <p className="mt-2 text-lg md:text-2xl text-text-secondary font-medium whitespace-nowrap">
          World's Fastest AI Personal Trainer
        </p>
      </div>
    </main>
  )
}
