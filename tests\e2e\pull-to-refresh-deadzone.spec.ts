import { test, expect } from '@playwright/test'
import { login, waitForLoadingComplete } from './helpers'

test.describe('Pull-to-Refresh DeadZone', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should not trigger pull-to-refresh within 100px deadZone on workout page', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.goto('/workout')
    await waitForLoadingComplete(page)

    // Wait for workout content to load
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Get initial state
    const refreshIndicator = page.locator(
      '[role="progressbar"], svg.animate-spin'
    )

    // Simulate small downward swipe within deadZone (80px < 100px)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 180, { steps: 20 }) // 80px movement

    // Should NOT show refresh indicator within deadZone
    await expect(refreshIndicator).not.toBeVisible()

    await page.mouse.up()
  })

  test('should trigger pull-to-refresh beyond 100px deadZone on workout page', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.goto('/workout')
    await waitForLoadingComplete(page)

    // Wait for workout content to load
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Simulate downward swipe beyond deadZone (120px > 100px)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 220, { steps: 30 }) // 120px movement

    // Pull indicator should be visible when beyond deadZone
    const pullIndicator = page
      .locator('svg')
      .filter({ hasNot: page.locator('.animate-spin') })
    await expect(pullIndicator).toBeVisible()

    await page.mouse.up()
  })

  test('should not trigger pull-to-refresh within 100px deadZone on program page', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.goto('/program')
    await waitForLoadingComplete(page)

    // Wait for program content to load
    await page.waitForSelector('[data-testid="scroll-container"]', {
      timeout: 10000,
    })

    // Get initial state
    const refreshIndicator = page.locator(
      '[role="progressbar"], svg.animate-spin'
    )

    // Simulate small downward swipe within deadZone (90px < 100px)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 190, { steps: 20 }) // 90px movement

    // Should NOT show refresh indicator within deadZone
    await expect(refreshIndicator).not.toBeVisible()

    await page.mouse.up()
  })

  test('should not trigger pull-to-refresh within 100px deadZone on exercise-v2 page', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    // Navigate to workout page first
    await page.goto('/workout')
    await waitForLoadingComplete(page)

    // Click on first exercise to navigate to exercise page
    const firstExercise = page.locator('[data-testid="exercise-card"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 10000 })
    await waitForLoadingComplete(page)

    // Get initial state
    const refreshIndicator = page.locator(
      '[role="progressbar"], svg.animate-spin'
    )

    // Simulate small downward swipe within deadZone (95px < 100px)
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 195, { steps: 20 }) // 95px movement

    // Should NOT show refresh indicator within deadZone
    await expect(refreshIndicator).not.toBeVisible()

    await page.mouse.up()
  })
})
