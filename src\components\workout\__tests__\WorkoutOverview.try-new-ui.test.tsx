import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { WorkoutOverview } from '../WorkoutOverview'
import { useWorkout } from '@/hooks/useWorkout'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: vi.fn(() => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  })),
}))

// Mock debugLog to avoid console noise
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    previewExerciseSkips: new Set(),
  })),
}))

describe('WorkoutOverview - Try New UI Button', () => {
  const mockPush = vi.fn()
  const mockStartWorkout = vi.fn()
  const mockLoadExerciseRecommendation = vi.fn()

  const getMockWorkout = (
    exerciseIds = [111, 222, 333]
  ): WorkoutTemplateGroupModel[] => [
    {
      Id: 1,
      Label: 'Test Workout',
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Workout A',
          Exercises: exerciseIds.map((id, index) => ({
            Id: id,
            Label:
              ['Bench Press', 'Squats', 'Deadlifts'][index] || `Exercise ${id}`,
          })),
        },
      ],
    },
  ]

  const mockWorkout = getMockWorkout()

  const mockExercises = [
    {
      Id: 111,
      Label: 'Bench Press',
      IsFinished: false,
      IsNextExercise: true,
      isLoadingSets: false,
      setsError: null,
      lastSetsUpdate: 0,
      sets: [],
    },
    {
      Id: 222,
      Label: 'Squats',
      IsFinished: false,
      IsNextExercise: false,
      isLoadingSets: false,
      setsError: null,
      lastSetsUpdate: 0,
      sets: [],
    },
  ]

  const defaultMockUseWorkout = {
    todaysWorkout: mockWorkout,
    isLoadingWorkout: false,
    workoutError: null,
    startWorkout: mockStartWorkout,
    userProgramInfo: null,
    exercises: mockExercises,
    exerciseWorkSetsModels: mockExercises,
    expectedExerciseCount: 3,
    hasInitialData: true,
    isOffline: false,
    refreshWorkout: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
    workoutSession: null,
    finishWorkout: vi.fn(),
    isLoading: false,
    loadExerciseRecommendation: mockLoadExerciseRecommendation,
  }

  const setupSuccessfulWorkoutStart = () => {
    mockStartWorkout.mockResolvedValue({
      success: true,
      firstExerciseId: 111,
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue({ push: mockPush } as any)
    vi.mocked(useWorkout).mockReturnValue(defaultMockUseWorkout as any)
  })

  describe('Navigation flow differences', () => {
    it('should start workout and navigate to v2 when clicking Try New UI button', async () => {
      // Given: User on workout overview with no active session
      setupSuccessfulWorkoutStart()

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should start workout first
      await waitFor(() => {
        expect(mockStartWorkout).toHaveBeenCalledWith(mockWorkout)
      })

      // And: Should navigate to v2 page with exercise name
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/workout/exercise-v2/111?exerciseName=Bench%20Press'
        )
      })
    })

    it('should handle rapid clicks without multiple workout starts', async () => {
      // Given: Slow workout start
      mockStartWorkout.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ success: true, firstExerciseId: 111 }),
              500
            )
          )
      )

      render(<WorkoutOverview />)

      // When: User rapidly clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)
      fireEvent.click(tryNewUIButton)
      fireEvent.click(tryNewUIButton)

      // Then: Should only start workout once
      await waitFor(() => {
        expect(mockStartWorkout).toHaveBeenCalledTimes(1)
      })
    })

    it('should navigate to first incomplete exercise when workout already active', async () => {
      // Given: Active workout session with first exercise completed
      const exercisesWithStatus = [
        { ...mockExercises[0], IsFinished: true },
        { ...mockExercises[1], IsFinished: false },
      ]

      vi.mocked(useWorkout).mockReturnValue({
        ...defaultMockUseWorkout,
        workoutSession: { id: 'session-123', exercises: [] },
        exerciseWorkSetsModels: exercisesWithStatus,
        exercises: exercisesWithStatus,
      } as any)

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should navigate to second exercise without starting workout
      await waitFor(() => {
        expect(mockStartWorkout).not.toHaveBeenCalled()
        expect(mockPush).toHaveBeenCalledWith(
          '/workout/exercise-v2/222?exerciseName=Squats'
        )
      })
    })

    it('should handle workout start failure gracefully', async () => {
      // Given: Workout start will fail
      mockStartWorkout.mockResolvedValue({
        success: false,
        firstExerciseId: undefined,
      })

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should not navigate
      await waitFor(() => {
        expect(mockStartWorkout).toHaveBeenCalled()
      })
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should show loading state while starting workout', async () => {
      // Given: Slow workout start
      mockStartWorkout.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ success: true, firstExerciseId: 111 }),
              500
            )
          )
      )

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should show loading state
      expect(screen.getByText('Loading...')).toBeInTheDocument()

      // And: Button should be disabled
      expect(tryNewUIButton).toBeDisabled()

      // Wait for completion
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalled()
      })
    })

    it('should use same loading pattern as Start Workout button', async () => {
      // This test ensures the Try New UI button follows the same pattern
      // as the Start Workout button for consistent behavior

      setupSuccessfulWorkoutStart()

      render(<WorkoutOverview />)

      // Given: Both buttons available
      const startWorkoutButton = screen.getByTestId('start-workout-button')
      const tryNewUIButton = screen.getByText('Start with new exercise view →')

      // When: Clicking Try New UI
      fireEvent.click(tryNewUIButton)

      // Then: Should use isStartingWorkout state (same as Start Workout button)
      expect(tryNewUIButton).toBeDisabled()
      expect(startWorkoutButton).toBeDisabled() // Both should be disabled

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalled()
      })
    })
  })

  describe('Error handling', () => {
    it.skip('should handle missing firstExerciseId from startWorkout', async () => {
      // Given: startWorkout returns success but no exercise ID
      mockStartWorkout.mockReset()
      mockStartWorkout.mockResolvedValue({
        success: true,
        firstExerciseId: undefined,
      })

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should not navigate
      await waitFor(() => {
        expect(mockStartWorkout).toHaveBeenCalled()
      })
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should handle empty exercises array when workout is active', () => {
      // Given: Active workout but no exercises
      vi.mocked(useWorkout).mockReturnValue({
        ...defaultMockUseWorkout,
        todaysWorkout: getMockWorkout([]), // No exercises in workout
        workoutSession: { id: 'session-123', exercises: [] },
        exerciseWorkSetsModels: [],
      } as any)

      render(<WorkoutOverview />)

      // When: Try new UI button should not even show with no exercises
      const tryNewUIButton = screen.queryByText(
        'Start with new exercise view →'
      )

      // Then: Button should not be rendered when no exercises
      expect(tryNewUIButton).not.toBeInTheDocument()
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('Recommendation preloading coordination', () => {
    it('should wait for startWorkout completion before navigation', async () => {
      // This test verifies that v2 navigation waits for the same
      // recommendation preloading that happens in startWorkout

      let startWorkoutResolve: any
      const startWorkoutPromise = new Promise((resolve) => {
        startWorkoutResolve = resolve
      })

      mockStartWorkout.mockReturnValue(startWorkoutPromise as any)

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should not navigate immediately
      expect(mockPush).not.toHaveBeenCalled()

      // When: startWorkout completes (including recommendation loading)
      startWorkoutResolve({ success: true, firstExerciseId: 111 })

      // Then: Should navigate after startWorkout completes
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/workout/exercise-v2/111?exerciseName=Bench%20Press'
        )
      })
    })

    it('should preload recommendation when workout session already exists', async () => {
      // This test addresses the bug where clicking "Try new UI" with an existing
      // session would navigate without preloading recommendations first

      // Given: Active workout session with exercises
      const mockExercises = [
        { Id: 111, Label: 'Bench Press', IsFinished: false, sets: [] },
        { Id: 222, Label: 'Squats', IsFinished: true, sets: [] },
        { Id: 333, Label: 'Deadlifts', IsFinished: false, sets: [] },
      ]

      vi.mocked(useWorkout).mockReturnValue({
        ...defaultMockUseWorkout,
        todaysWorkout: getMockWorkout([111, 222, 333]),
        workoutSession: { id: 'session-123', exercises: mockExercises },
        exerciseWorkSetsModels: mockExercises,
        exercises: mockExercises,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
      } as any)

      mockLoadExerciseRecommendation.mockResolvedValue({
        Id: 111,
        Label: 'Bench Press',
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
      })

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should preload recommendation for first incomplete exercise
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(111)
      })

      // And: Should navigate to v2 page after preloading
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/workout/exercise-v2/111?exerciseName=Bench%20Press'
        )
      })
    })

    it('should handle recommendation loading failure gracefully', async () => {
      // Given: Active workout session and recommendation will fail
      const mockExercises = [
        { Id: 111, Label: 'Bench Press', IsFinished: false, sets: [] },
      ]

      vi.mocked(useWorkout).mockReturnValue({
        ...defaultMockUseWorkout,
        todaysWorkout: getMockWorkout([111]),
        workoutSession: { id: 'session-123', exercises: mockExercises },
        exerciseWorkSetsModels: mockExercises,
        exercises: mockExercises,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
      } as any)

      mockLoadExerciseRecommendation.mockRejectedValue(
        new Error('Failed to load recommendation')
      )

      render(<WorkoutOverview />)

      // When: User clicks Try New UI button
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)

      // Then: Should still navigate even if recommendation fails
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledWith(111)
      })

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/workout/exercise-v2/111?exerciseName=Bench%20Press'
        )
      })
    })

    it('should prevent multiple recommendation loads on rapid clicks', async () => {
      // Given: Active workout session
      const mockExercises = [
        { Id: 111, Label: 'Bench Press', IsFinished: false, sets: [] },
      ]

      vi.mocked(useWorkout).mockReturnValue({
        ...defaultMockUseWorkout,
        todaysWorkout: getMockWorkout([111]),
        workoutSession: { id: 'session-123', exercises: mockExercises },
        exerciseWorkSetsModels: mockExercises,
        exercises: mockExercises,
        loadExerciseRecommendation: mockLoadExerciseRecommendation,
      } as any)

      // Slow recommendation loading
      mockLoadExerciseRecommendation.mockImplementation(
        () => new Promise((resolve) => setTimeout(resolve, 500))
      )

      render(<WorkoutOverview />)

      // When: User rapidly clicks Try New UI button multiple times
      const tryNewUIButton = screen.getByText('Start with new exercise view →')
      fireEvent.click(tryNewUIButton)
      fireEvent.click(tryNewUIButton)
      fireEvent.click(tryNewUIButton)

      // Then: Should only trigger one recommendation load
      await waitFor(() => {
        expect(mockLoadExerciseRecommendation).toHaveBeenCalledTimes(1)
      })
    })
  })
})
