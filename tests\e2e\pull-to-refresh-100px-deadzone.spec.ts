import { test, expect } from '@playwright/test'
import { login, waitForLoadingComplete } from './helpers'

test.describe('Pull-to-Refresh 100px DeadZone', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should require 100px scroll before showing pull indicator on workout page', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.goto('/workout')
    await waitForLoadingComplete(page)

    // Wait for workout content to load
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Look for the pull-to-refresh indicator container
    const pullContainer = page.locator(
      'div.absolute.top-0.left-0.right-0.flex.justify-center.pointer-events-none.z-50'
    )

    // Test 80px movement (less than 100px) - should NOT show indicator
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 180, { steps: 20 }) // 80px movement

    // Pull indicator should NOT be visible
    await expect(pullContainer).not.toBeVisible()

    await page.mouse.up()

    // Now test movement beyond deadZone
    // With 100px deadZone and resistance 3.5, to see the indicator we need to:
    // 1. Move beyond deadZone (100px)
    // 2. Then the pull distance is calculated as (movement - deadZone) / resistance
    // So to get any visible pull indicator, we need > 100px movement
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 210, { steps: 20 }) // 110px movement (10px beyond deadZone)

    // Pull indicator should be visible beyond 100px deadZone
    await expect(pullContainer).toBeVisible()

    await page.mouse.up()
  })
})
