import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { LoginPageClient } from '../LoginPageClient'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    isAuthenticated: false,
    login: vi.fn(),
  }),
}))

// Mock useLoginPrefetch hook
vi.mock('@/hooks/useLoginPrefetch', () => ({
  useLoginPrefetch: () => ({
    startPrefetch: vi.fn(),
  }),
}))

// Mock LoginForm to avoid complex dependencies
vi.mock('../LoginForm', () => ({
  LoginForm: () => <div data-testid="login-form">Login Form</div>,
}))

// Mock QuickSuccessScreen
vi.mock('../auth/QuickSuccessScreen', () => ({
  QuickSuccessScreen: () => <div data-testid="success-screen">Success</div>,
}))

describe('LoginPageClient - Responsive Subheading', () => {
  // Helper to check if text would wrap at given viewport width
  const wouldTextWrap = (
    text: string,
    fontSize: number,
    viewportWidth: number,
    padding: number = 32
  ) => {
    // Rough estimate: average character width is about 0.5-0.6 of font size
    const avgCharWidth = fontSize * 0.55
    const textWidth = text.length * avgCharWidth
    const availableWidth = viewportWidth - padding
    return textWidth > availableWidth
  }

  it('should render title and subtitle on same line with separator', () => {
    const { container } = render(<LoginPageClient />)

    // Look for the header container
    const header = container.querySelector('h1')
    expect(header).toBeTruthy()

    // Title and subtitle should be on same line
    expect(header?.textContent).toContain('Dr. Muscle X')
    expect(header?.textContent).toContain("World's Fastest AI Personal Trainer")

    // Should have a separator (like • or |)
    expect(header?.textContent).toMatch(
      /Dr\. Muscle X\s*[•|]\s*World's Fastest AI Personal Trainer/
    )
  })

  it('should calculate if text would wrap on 320px viewport', () => {
    const text = "World's Fastest AI Personal Trainer"
    const mobileFontSize = 18 // text-lg
    const viewportWidth = 320

    // Current implementation would wrap
    const wraps = wouldTextWrap(text, mobileFontSize, viewportWidth)
    expect(wraps).toBe(true) // This confirms the issue exists
  })

  it('should not wrap with smaller font size on mobile', () => {
    const text = "World's Fastest AI Personal Trainer"
    const smallerFontSize = 14 // Proposed fix
    const viewportWidth = 320

    // With smaller font, should not wrap
    const wraps = wouldTextWrap(text, smallerFontSize, viewportWidth)
    expect(wraps).toBe(false)
  })

  it('should have fixed responsive classes to prevent wrapping', () => {
    const { container } = render(<LoginPageClient />)

    const heading = container.querySelector('h1')
    const classes = heading?.className || ''

    // After fix, should have nowrap on the heading
    expect(classes).toContain('whitespace-nowrap')
  })
})
