import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExplainerBox } from '../ExplainerBox'
import type { RecommendationModel } from '@/types'

describe('ExplainerBox - Weight Formatting', () => {
  it('should format last time weight with floating point errors', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 2,
      HistorySet: [
        {
          Reps: 5,
          Weight: { Lb: 35.00000000000004, Kg: 15.875732 },
          IsWarmups: true,
        },
        {
          Reps: 3,
          Weight: { Lb: 45.00000000000001, Kg: 20.41165 },
          IsWarmups: true,
        },
        {
          Reps: 8,
          Weight: { Lb: 135.99999999999997, Kg: 61.689466 },
          IsWarmups: false,
        },
      ],
      FirstWorkSetReps: 8,
      FirstWorkSetWeight: { Lb: 135.99999999999997, Kg: 61.689466 },
    } as RecommendationModel

    render(
      <ExplainerBox
        recommendation={recommendation}
        currentSetIndex={2} // First work set (after 2 warmups)
        isWarmup={false}
        isFirstWorkSet
        unit="lbs"
        currentReps={10}
        currentWeight={140}
      />
    )

    // Should show "Last time: 8 × 136 lbs" not "8 × 135.99999999999997 lbs"
    expect(screen.getByText(/Last time: 8 × 136 lbs/)).toBeInTheDocument()

    // Should not show the floating point error
    expect(screen.queryByText(/135\.99999999999997/)).not.toBeInTheDocument()
  })

  it('should format warmup weights with floating point errors', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 2,
      HistorySet: [
        {
          Reps: 7,
          Weight: { Lb: 35.00000000000004, Kg: 15.875732 },
          IsWarmups: true,
        },
        {
          Reps: 3,
          Weight: { Lb: 45.00000000000001, Kg: 20.41165 },
          IsWarmups: true,
        },
      ],
    } as RecommendationModel

    render(
      <ExplainerBox
        recommendation={recommendation}
        currentSetIndex={0} // First warmup
        isWarmup
        unit="lbs"
      />
    )

    // Should show "Last time: 7 × 35 lbs" not "7 × 35.00000000000004 lbs"
    expect(screen.getByText(/Last time: 7 × 35 lbs/)).toBeInTheDocument()

    // Should not show the floating point error
    expect(screen.queryByText(/35\.00000000000004/)).not.toBeInTheDocument()
  })

  it('should handle kg units correctly', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 0,
      HistorySet: [
        {
          Reps: 10,
          Weight: { Lb: 135.678901234, Kg: 61.58372999999 },
          IsWarmups: false,
        },
      ],
      FirstWorkSetReps: 10,
      FirstWorkSetWeight: { Lb: 135.678901234, Kg: 61.58372999999 },
    } as RecommendationModel

    render(
      <ExplainerBox
        recommendation={recommendation}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet
        unit="kg"
        currentReps={10}
        currentWeight={65}
      />
    )

    // Should show "Last time: 10 × 61.58 kg" with proper formatting
    expect(screen.getByText(/Last time: 10 × 61\.58 kg/)).toBeInTheDocument()

    // Should not show excessive decimals
    expect(screen.queryByText(/61\.58372999999/)).not.toBeInTheDocument()
  })
})
