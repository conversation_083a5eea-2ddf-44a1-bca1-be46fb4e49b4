import { test, expect } from '@playwright/test'

test.describe('OAuth Login Fix', () => {
  test('should use success screen pattern for OAuth login', async ({
    page,
  }) => {
    // Start on login page
    await page.goto('/login')

    // Wait for OAuth buttons to be visible
    await page.waitForSelector('button:has-text("Sign in with Google")', {
      state: 'visible',
    })

    // Mock OAuth success to test the flow
    await page.route('**/api/oauth/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock_token',
          userName: '<EMAIL>',
        }),
      })
    })

    // Mock the token exchange endpoint
    await page.route('**/api/auth/exchange', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true }),
      })
    })

    // Click Google sign in button
    const googleButton = page.locator('button:has-text("Sign in with <PERSON>")')
    await googleButton.click()

    // Should see quick success screen
    await expect(
      page.locator('[data-testid="quick-success-screen"]')
    ).toBeVisible()

    // Should see checkmark initially
    await expect(page.locator('.animate-fade-in').first()).toBeVisible()

    // Should eventually navigate to program page
    await page.waitForURL('**/program', { timeout: 5000 })

    // Verify we're on the program page and not back at login
    expect(page.url()).toContain('/program')
    expect(page.url()).not.toContain('/login')
  })

  test('should handle OAuth without onSuccess callback gracefully', async ({
    page,
  }) => {
    // This tests the default handler when onSuccess is not provided
    // The component should still work with the delayed navigation

    await page.goto('/login')
    await page.waitForSelector('button:has-text("Sign in with Google")')

    // Mock OAuth and API responses
    await page.route('**/api/oauth/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock_token',
          userName: '<EMAIL>',
        }),
      })
    })

    await page.route('**/api/auth/exchange', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true }),
      })
    })

    // Store initial URL
    const initialUrl = page.url()

    // Click Google button
    await page.locator('button:has-text("Sign in with Google")').click()

    // With the fix, navigation should happen after a delay
    // to ensure auth state persists
    await page.waitForTimeout(200)

    // Should navigate away from login
    expect(page.url()).not.toBe(initialUrl)
  })
})
