import { test, expect } from '@playwright/test'
import { mockStartWorkout, mockSaveSet } from './helpers/api-mocks'
import { mockRecommendationWithHistory } from './helpers/recommendation-mocks'
import { computeOneRM } from '@/utils/oneRmCalculator'

test.describe('1RM Progress Calculation', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 })

    // Mock login
    await page.goto('/login')
    await page.evaluate(() => {
      localStorage.setItem('dr-muscle-token', 'test-token')
      localStorage.setItem('dr-muscle-refresh-token', 'test-refresh-token')
      localStorage.setItem(
        'dr-muscle-user',
        JSON.stringify({
          Email: '<EMAIL>',
          MassUnit: 'kg',
        })
      )
    })
  })

  test('should show 0% progress when matching last workout exactly', async ({
    page,
  }) => {
    // Setup mock data with specific last workout: 5 reps × 60 kg
    const lastReps = 5
    const lastWeight = 60
    const last1RM = computeOneRM(lastWeight, lastReps)

    const recommendation = {
      ...mockRecommendationWithHistory,
      FirstWorkSetReps: lastReps,
      FirstWorkSetWeight: { Kg: lastWeight, Lb: lastWeight * 2.20462 },
      FirstWorkSet1RM: { Kg: last1RM, Lb: last1RM * 2.20462 },
    }

    // Mock API responses
    await mockStartWorkout(page, recommendation)
    await mockSaveSet(page)

    // Navigate to workout
    await page.goto('/workout')
    await page.click('[data-testid="start-workout-button"]')

    // Wait for exercise page to load
    await page.waitForURL('**/workout/exercise/**')
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Save warmup sets to get to first work set
    await page.click('[data-testid="floating-save-button"]')
    await page.waitForURL('**/rest-timer**')
    await page.click('button:has-text("Skip")')

    await page.waitForURL('**/workout/exercise/**')
    await page.click('[data-testid="floating-save-button"]')
    await page.waitForURL('**/rest-timer**')
    await page.click('button:has-text("Skip")')

    // Now we should be on the first work set
    await page.waitForURL('**/workout/exercise/**')

    // Verify initial state shows non-zero progress
    await expect(page.locator('text=/1RM Progress:.*%/')).toBeVisible()
    const initialProgress = await page
      .locator('text=/1RM Progress:.*%/')
      .textContent()
    expect(initialProgress).not.toContain('0.00%')

    // Update reps and weight to match last workout exactly
    const repsInput = page.locator('input[aria-label*="Reps for set 1"]')
    const weightInput = page.locator('input[aria-label*="Weight for set 1"]')

    // Clear and set reps
    await repsInput.click()
    await repsInput.fill('')
    await repsInput.fill(lastReps.toString())

    // Clear and set weight
    await weightInput.click()
    await weightInput.fill('')
    await weightInput.fill(lastWeight.toString())

    // Verify 1RM progress shows 0%
    await expect(page.locator('text="1RM Progress: 0.00%"')).toBeVisible()
  })

  test('should calculate positive progress when exceeding last workout', async ({
    page,
  }) => {
    // Setup mock data
    const lastReps = 10
    const lastWeight = 50
    const last1RM = computeOneRM(lastWeight, lastReps)

    const recommendation = {
      ...mockRecommendationWithHistory,
      FirstWorkSetReps: lastReps,
      FirstWorkSetWeight: { Kg: lastWeight, Lb: lastWeight * 2.20462 },
      FirstWorkSet1RM: { Kg: last1RM, Lb: last1RM * 2.20462 },
    }

    // Mock API responses
    await mockStartWorkout(page, recommendation)
    await mockSaveSet(page)

    // Navigate to first work set (same as above)
    await page.goto('/workout')
    await page.click('[data-testid="start-workout-button"]')
    await page.waitForURL('**/workout/exercise/**')

    // Skip warmups
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < 2; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.click('[data-testid="floating-save-button"]')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForURL('**/rest-timer**')
      // eslint-disable-next-line no-await-in-loop
      await page.click('button:has-text("Skip")')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForURL('**/workout/exercise/**')
    }

    // Update to higher values
    const newReps = 12
    const newWeight = 50

    const repsInput = page.locator('input[aria-label*="Reps for set 1"]')
    const weightInput = page.locator('input[aria-label*="Weight for set 1"]')

    await repsInput.click()
    await repsInput.fill('')
    await repsInput.fill(newReps.toString())

    await weightInput.click()
    await weightInput.fill('')
    await weightInput.fill(newWeight.toString())

    // Calculate expected progress
    const new1RM = computeOneRM(newWeight, newReps)
    const expectedProgress = ((new1RM - last1RM) / last1RM) * 100

    // Verify positive progress is shown
    const progressText = await page
      .locator('text=/1RM Progress:.*%/')
      .textContent()
    const progressMatch = progressText?.match(/1RM Progress: \+?(-?\d+\.\d+)%/)
    const actualProgress = progressMatch ? parseFloat(progressMatch[1]) : 0

    expect(actualProgress).toBeCloseTo(expectedProgress, 1)
    expect(actualProgress).toBeGreaterThan(0)
  })
})
