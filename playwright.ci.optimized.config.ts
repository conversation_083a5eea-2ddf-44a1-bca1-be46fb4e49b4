// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'
// import { webkitProjectConfig } from './tests/e2e/webkit-config' // Disabled for memory optimization

/**
 * Optimized CI-specific Playwright configuration
 * with enhanced WebKit stability and memory management
 */

// Define all projects for critical mobile paths - Chrome only for memory optimization
const allProjects = [
  /* Mobile Safari - DISABLED to save memory on 8GB runners
  {
    ...webkitProjectConfig,
    name: 'Mobile Safari Critical',
    testMatch: /.*@critical.*\.spec\.ts$/,
    use: {
      ...webkitProjectConfig.use,
      // Enhanced WebKit stability settings
      launchOptions: {
        ...webkitProjectConfig.use.launchOptions,
        timeout: 120000, // Reduced timeout for faster feedback
        slowMo: 250, // Reduced slowMo for faster execution
        args: [], // Remove all args - WebKit doesn't support Chrome flags
        env: {
          ...process.env,
          WEBKIT_DISABLE_COMPOSITING: '1',
          WEBKIT_FORCE_COMPOSITING_MODE: '0',
        },
        chromiumSandbox: false,
        handleSIGINT: false,
        handleSIGTERM: false,
        devtools: false,
      },
      contextOptions: {
        ...webkitProjectConfig.use.contextOptions,
        // Additional stability measures
        strictSelectors: false,
        ignoreHTTPSErrors: true,
        bypassCSP: true,
      },
      // Reasonable timeouts for WebKit
      actionTimeout: 20000,
      navigationTimeout: 40000,
    },
    retries: 3, // Reduced from 5 to prevent resource exhaustion
    workers: 1,
  }, */
  /* Mobile Chrome - Run for critical tests only as fallback */
  {
    name: 'Mobile Chrome Critical',
    use: {
      ...devices['Pixel 5'],
      // Chrome-specific options for stability
      launchOptions: {
        timeout: 180000,
        args: [
          '--disable-dev-shm-usage',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
        ],
        slowMo: 500,
      },
      actionTimeout: 30000,
      navigationTimeout: 60000,
    },
    testMatch: /.*@critical.*\.spec\.ts$/,
    retries: 3,
    workers: 1,
  },
  /* Mobile Safari Full - Use Chrome as fallback for stability */
  {
    name: 'Mobile Safari Full',
    use: {
      ...devices['Pixel 5'],
      // Chrome-specific options for stability (using Chrome instead of WebKit)
      launchOptions: {
        timeout: 180000,
        args: [
          '--disable-dev-shm-usage',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
        ],
        slowMo: 300,
      },
      actionTimeout: 25000,
      navigationTimeout: 50000,
    },
    testIgnore: /.*@critical.*\.spec\.ts$/,
    retries: 2,
    workers: 1,
  },
]

// Filter projects when CI_FULL_SUITE is set to only include Chromium projects
const filteredProjects =
  process.env.CI_FULL_SUITE === '1'
    ? (() => {
        console.warn(
          '🔧 CI_FULL_SUITE mode: filtering to Chromium projects only'
        )
        return allProjects.filter(
          (project) =>
            project.name && project.name.toLowerCase().includes('chrome')
        )
      })()
    : allProjects
export default defineConfig({
  testDir: './tests/e2e',
  /* Enable parallel execution for faster CI on GitHub-hosted runners */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  /* Reduced retries for faster feedback - WebKit needs fewer retries to avoid resource exhaustion */
  retries: 1,
  /* Use 1 worker for WebKit stability on self-hosted macOS runners */
  workers: 1,
  /* Support for sharding tests across multiple machines */
  /* Usage: --shard=1/4 --shard=2/4 etc. */
  /* Reporter configuration for CI */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  /* Reasonable timeout for E2E tests */
  timeout: 60000, // 1 minute per test (reduced from 3 minutes)
  /* Standard expect timeout */
  expect: {
    timeout: 15000, // 15 seconds for assertions (reduced from 45 seconds)
  },
  /* Output directory for test results */
  outputDir: 'test-results',
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for local testing in CI */
    baseURL: 'http://localhost:3000',
    /* Minimal tracing for faster execution */
    trace: 'retain-on-failure',
    /* Screenshot on failure only */
    screenshot: 'only-on-failure',
    /* No video recording for faster execution */
    video: 'off',
    /* Standard action timeout */
    actionTimeout: 15000, // 15 seconds (reduced from 45 seconds)
    /* Standard navigation timeout */
    navigationTimeout: 30000, // 30 seconds (reduced from 90 seconds)
    /* Browser launch options for CI stability */
    launchOptions: {
      timeout: 300000, // 5 minutes for browser launch
      // WebKit doesn't support Chrome/Chromium specific flags
      args: [],
      // Force single process for WebKit stability
      chromiumSandbox: false,
      // Reasonable slowMo for stability without excessive delays
      slowMo: 250,
    },
    /* Context options for stability */
    contextOptions: {
      // Reduce memory usage
      viewport: { width: 390, height: 844 },
      // Disable unnecessary features
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      // Reduce resource usage
      reducedMotion: 'reduce',
      // Disable service workers which can cause issues
      serviceWorkers: 'block',
      // Set explicit locale
      locale: 'en-US',
      // Disable permissions that might cause prompts
      permissions: [],
    },
    /* Global test setup */
    extraHTTPHeaders: {
      'X-Test-Context': 'playwright-ci',
      'Cache-Control': 'no-cache',
    },
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),

  /* Configure projects for critical mobile paths - Chrome only for memory optimization */
  projects: filteredProjects,

  /* Run local dev server before tests */
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: true, // Always reuse existing server to prevent port conflicts
    timeout: 180000, // 3 minutes for server startup
    env: {
      NODE_OPTIONS: '--max_old_space_size=4096', // Reduced for 8GB ubicloud runners
      NEXT_PUBLIC_DISABLE_OAUTH: 'true', // Disable OAuth in E2E tests
    },
  },
})
