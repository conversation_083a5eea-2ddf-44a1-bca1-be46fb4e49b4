import { describe, it, expect } from 'vitest'
import { getNavigationConfig } from '../navigationConfig'

describe('navigationConfig - exercise-v2 routes', () => {
  it('should return navigation config for exercise-v2 dynamic route', () => {
    // Given: User navigates to exercise-v2 page
    const pathname = '/workout/exercise-v2/123'

    // When: Navigation config is requested
    const config = getNavigationConfig(pathname)

    // Then: Returns proper config with back navigation
    expect(config).toEqual({
      title: 'Workout', // Static title for v2 route
      showBackButton: true,
      backRoute: '/workout',
      rightElement: 'UserAvatar',
      requiresAuth: true,
    })
  })

  it('should handle different exercise IDs in exercise-v2 route', () => {
    // Given: Different exercise ID
    const pathname = '/workout/exercise-v2/456'

    // When: Navigation config is requested
    const config = getNavigationConfig(pathname)

    // Then: Returns same config regardless of ID
    expect(config).not.toBeNull()
    expect(config?.showBackButton).toBe(true)
    expect(config?.backRoute).toBe('/workout')
  })

  it('should not match non-exercise-v2 routes', () => {
    // Given: Different route pattern
    const pathname = '/workout/exercise-v3/123'

    // When: Navigation config is requested
    const config = getNavigationConfig(pathname)

    // Then: Returns null for non-matching routes
    expect(config).toBeNull()
  })
})
