import { test, expect } from '@playwright/test'

test.describe('Exercise Loading Optimization', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 })
  })

  test('should load exercises only once when navigating through Try New UI', async ({
    page,
  }) => {
    // Track API calls
    const apiCalls = {
      login: 0,
      userInfo: 0,
      programInfo: 0,
      workoutTemplate: 0,
      recommendations: new Map<number, number>(), // exercise ID -> call count
      startWorkout: 0,
    }

    // Intercept and track all API calls
    await page.route('**/api/**', async (route) => {
      const url = route.request().url()

      // Track different API endpoints
      if (url.includes('/Account/Login')) {
        apiCalls.login++
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Token: 'test-token',
            RefreshToken: 'refresh-token',
            User: {
              Email: '<EMAIL>',
              FirstName: 'Test',
              LastName: 'User',
            },
          }),
        })
      } else if (url.includes('/Account/GetUserInfo')) {
        apiCalls.userInfo++
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Email: '<EMAIL>',
            MassUnit: 'lbs',
          }),
        })
      } else if (url.includes('/Workout/GetUserProgramInfo')) {
        apiCalls.programInfo++
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            GetUserProgramInfoResponseModel: {
              NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
            },
          }),
        })
      } else if (url.includes('/Workout/GetUserWorkoutTemplateGroup')) {
        apiCalls.workoutTemplate++
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 1,
                  Label: 'Day 1',
                  Exercices: [
                    { Id: 111, Label: 'Bench Press' },
                    { Id: 222, Label: 'Squat' },
                    { Id: 333, Label: 'Deadlift' },
                  ],
                },
              ],
            },
          ]),
        })
      } else if (url.includes('/Workout/StartNewWorkout')) {
        apiCalls.startWorkout++
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: 'workout-123',
            WorkoutTemplateId: 1,
            StartTime: new Date().toISOString(),
          }),
        })
      } else if (url.includes('/Workout/Getrecommendation')) {
        // Extract exercise ID from request body
        const requestBody = route.request().postDataJSON()
        const exerciseId = requestBody?.ExerciseId || 0

        // Track recommendation calls per exercise
        const currentCount = apiCalls.recommendations.get(exerciseId) || 0
        apiCalls.recommendations.set(exerciseId, currentCount + 1)

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: Math.random(),
            ExerciseId: exerciseId,
            Reps: 10,
            Weight: { Kg: 50, Lb: 110 },
            WeightIncrement: 5,
          }),
        })
      } else {
        await route.continue()
      }
    })

    // GIVEN user logs in
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button:has-text("Log In")')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Verify initial API calls during login
    expect(apiCalls.login).toBe(1)
    expect(apiCalls.userInfo).toBeGreaterThanOrEqual(1)
    expect(apiCalls.programInfo).toBeGreaterThanOrEqual(1)

    // WHEN user taps "Open Workout"
    await page.click('text=Open Workout')
    await page.waitForURL('/workout')

    // AND taps "Try New UI"
    await page.waitForSelector('text=Try our new UI')
    await page.click('text=Start with new exercise view')

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)

    // Allow time for any duplicate requests
    await page.waitForTimeout(2000)

    // THEN verify single load per exercise
    apiCalls.recommendations.forEach((count, exerciseId) => {
      expect(count, `Exercise ${exerciseId} loaded ${count} times`).toBe(1)
    })

    // Verify workout started only once
    expect(apiCalls.startWorkout).toBe(1)

    // Verify no duplicate template loads
    expect(apiCalls.workoutTemplate).toBeLessThanOrEqual(2) // Initial + possible refresh

    // Verify exercise content is displayed properly
    await expect(page.locator('text=Bench Press')).toBeVisible()

    // Verify no loading indicators after initial load
    await expect(page.locator('.loading-spinner')).not.toBeVisible()
    await expect(page.locator('text=Loading exercise')).not.toBeVisible()
  })

  test('should use cached data when navigating between exercises', async ({
    page,
  }) => {
    // Track recommendation calls
    const recommendationCalls = new Map<number, number>()

    await page.route('**/api/Workout/Getrecommendation', async (route) => {
      const requestBody = route.request().postDataJSON()
      const exerciseId = requestBody?.ExerciseId || 0

      const currentCount = recommendationCalls.get(exerciseId) || 0
      recommendationCalls.set(exerciseId, currentCount + 1)

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: Math.random(),
          ExerciseId: exerciseId,
          Reps: 10,
          Weight: { Kg: 50, Lb: 110 },
        }),
      })
    })

    // Setup other required mocks
    await page.route('**/api/Account/**', async (route) => {
      await route.fulfill({ status: 200, body: '{}' })
    })

    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup',
      async (route) => {
        await route.fulfill({
          status: 200,
          body: JSON.stringify([
            {
              WorkoutTemplates: [
                {
                  Exercices: [
                    { Id: 111, Label: 'Exercise 1' },
                    { Id: 222, Label: 'Exercise 2' },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to first exercise
    await page.goto('/workout/exercise-v2/111')
    await page.waitForTimeout(1000)

    // Navigate to second exercise
    await page.goto('/workout/exercise-v2/222')
    await page.waitForTimeout(1000)

    // Navigate back to first exercise
    await page.goto('/workout/exercise-v2/111')
    await page.waitForTimeout(1000)

    // Verify each exercise loaded only once
    expect(recommendationCalls.get(111)).toBe(1)
    expect(recommendationCalls.get(222)).toBe(1)
  })

  test('should show smooth transition without double loading', async ({
    page,
  }) => {
    let transitionScreenVisible = false
    let mainContentVisible = false
    let doubleLoadDetected = false

    // Monitor for transition screen and main content
    page.on('console', (msg) => {
      const text = msg.text()
      if (text.includes('Transition screen shown')) {
        if (mainContentVisible) {
          doubleLoadDetected = true
        }
        transitionScreenVisible = true
      }
      if (text.includes('Main content shown')) {
        if (transitionScreenVisible && mainContentVisible) {
          doubleLoadDetected = true
        }
        mainContentVisible = true
      }
    })

    // Mock all required endpoints
    await page.route('**/api/**', async (route) => {
      if (route.request().url().includes('Getrecommendation')) {
        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 500))
      }
      await route.fulfill({ status: 200, body: '{}' })
    })

    // Navigate directly to exercise page
    await page.goto('/workout/exercise-v2/123?exerciseName=Test%20Exercise')

    // Wait for page to stabilize
    await page.waitForTimeout(2000)

    // Verify no double loading occurred
    expect(doubleLoadDetected).toBe(false)
  })
})
