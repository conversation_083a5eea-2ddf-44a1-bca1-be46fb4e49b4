import { render, screen, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { TimerScreenWrapper } from '../TimerScreenWrapper'
import { useSearchParams } from 'next/navigation'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(),
}))

vi.mock('../TimerScreen', () => ({
  TimerScreen: ({
    soundEnabled,
    vibrationEnabled,
    isBetweenSets,
    restDuration,
  }: any) => (
    <div data-testid="timer-screen">
      <div data-testid="sound-enabled">{soundEnabled.toString()}</div>
      <div data-testid="vibration-enabled">{vibrationEnabled.toString()}</div>
      <div data-testid="is-between-sets">{isBetweenSets.toString()}</div>
      <div data-testid="rest-duration">{restDuration}</div>
    </div>
  ),
}))

describe('TimerScreenWrapper', () => {
  const mockNextSet = vi.fn()
  const mockSearchParams = new URLSearchParams()

  beforeEach(() => {
    vi.mocked(useSearchParams).mockReturnValue(mockSearchParams as any)
    vi.mocked(useWorkoutStore).mockReturnValue(mockNextSet)
    localStorage.clear()
    vi.clearAllMocks()
  })

  afterEach(() => {
    localStorage.clear()
  })

  describe('rest duration updates', () => {
    it('should pass initial rest duration from localStorage to TimerScreen', async () => {
      // Set localStorage before component mounts
      localStorage.setItem('soundEnabled', 'true')
      localStorage.setItem('vibrationEnabled', 'true')
      localStorage.setItem('restDuration', '90')

      render(<TimerScreenWrapper />)

      expect(screen.getByTestId('rest-duration')).toHaveTextContent('90')
    })

    it('should use default duration of 120 when no localStorage value exists', () => {
      render(<TimerScreenWrapper />)

      expect(screen.getByTestId('rest-duration')).toHaveTextContent('120')
    })

    it('should update rest duration when storage event is dispatched', async () => {
      render(<TimerScreenWrapper />)

      // Initial duration should be 120 (default)
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('120')

      // Change duration in localStorage and dispatch event
      act(() => {
        localStorage.setItem('restDuration', '180')
        window.dispatchEvent(new Event('storage'))
      })

      // Duration should update to 180
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('180')
    })

    it('should clamp rest duration to valid range (5-600 seconds)', async () => {
      render(<TimerScreenWrapper />)

      // Test minimum clamping
      act(() => {
        localStorage.setItem('restDuration', '2')
        window.dispatchEvent(new Event('storage'))
      })
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('5')

      // Test maximum clamping
      act(() => {
        localStorage.setItem('restDuration', '700')
        window.dispatchEvent(new Event('storage'))
      })
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('600')
    })

    it('should handle invalid duration values gracefully', async () => {
      render(<TimerScreenWrapper />)

      // Test NaN value
      act(() => {
        localStorage.setItem('restDuration', 'invalid')
        window.dispatchEvent(new Event('storage'))
      })
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('120') // Should use default

      // Test negative value
      act(() => {
        localStorage.setItem('restDuration', '-50')
        window.dispatchEvent(new Event('storage'))
      })
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('120') // Should use default
    })

    it('should update multiple preferences when storage event is dispatched', async () => {
      render(<TimerScreenWrapper />)

      // Change all preferences
      act(() => {
        localStorage.setItem('soundEnabled', 'false')
        localStorage.setItem('vibrationEnabled', 'false')
        localStorage.setItem('restDuration', '60')
        window.dispatchEvent(new Event('storage'))
      })

      expect(screen.getByTestId('sound-enabled')).toHaveTextContent('false')
      expect(screen.getByTestId('vibration-enabled')).toHaveTextContent('false')
      expect(screen.getByTestId('rest-duration')).toHaveTextContent('60')
    })
  })

  describe('existing functionality', () => {
    it('should load sound and vibration preferences from localStorage', () => {
      localStorage.setItem('soundEnabled', 'false')
      localStorage.setItem('vibrationEnabled', 'false')

      render(<TimerScreenWrapper />)

      expect(screen.getByTestId('sound-enabled')).toHaveTextContent('false')
      expect(screen.getByTestId('vibration-enabled')).toHaveTextContent('false')
    })

    it('should update preferences when storage event is dispatched', async () => {
      render(<TimerScreenWrapper />)

      act(() => {
        localStorage.setItem('soundEnabled', 'false')
        localStorage.setItem('vibrationEnabled', 'false')
        window.dispatchEvent(new Event('storage'))
      })

      expect(screen.getByTestId('sound-enabled')).toHaveTextContent('false')
      expect(screen.getByTestId('vibration-enabled')).toHaveTextContent('false')
    })
  })
})
