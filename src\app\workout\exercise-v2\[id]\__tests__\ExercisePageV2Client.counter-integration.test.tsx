import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import type { ExerciseModel } from '@/types'

// Mock all dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'kg' }),
  }),
}))

// Store mocks for interaction
let mockSaveSet: ReturnType<typeof vi.fn>
let mockNextSet: ReturnType<typeof vi.fn>
let mockWorkoutSession: any

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: () => ({
    isLoadingWorkout: false,
    workoutError: null,
    workoutSession: mockWorkoutSession,
    saveSet: mockSaveSet,
  }),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => ({
    loadingStates: new Map(),
    nextSet: mockNextSet,
    updateActivity: vi.fn(),
  }),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: () => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  }),
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

// Create dynamic mock for useSetScreenLogic
let currentCompletedSets = 0
let mockHandleSaveSet: ReturnType<typeof vi.fn>

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: () => ({
    currentExercise: mockExercise,
    exercises: [mockExercise],
    currentSetIndex: currentCompletedSets,
    isSaving: false,
    saveError: null,
    showComplete: false,
    showExerciseComplete: false,
    recommendation: {
      WarmupsCount: 0,
      Series: 6,
      Reps: 10,
      Weight: { Kg: 80, Lb: 175 },
    },
    isLoading: false,
    error: null,
    isLastExercise: false,
    isLastSet: false,
    isWarmup: false,
    isFirstWorkSet: false,
    completedSets: Array(currentCompletedSets).fill({
      Id: 1,
      ExerciseId: 1,
      Reps: 10,
      Weight: { Kg: 80, Lb: 175 },
      IsWarmups: false,
      IsFinished: true,
      IsNext: false,
      SetNo: '1',
    }),
    setData: { reps: 10, weight: 80, duration: 0 },
    setSetData: vi.fn(),
    setSaveError: vi.fn(),
    handleSaveSet: mockHandleSaveSet,
    refetchRecommendation: vi.fn(),
  }),
}))

// Mock the generateAllSets utility
vi.mock('@/utils/generateAllSets', () => ({
  generateAllSets: () => {
    const allSets = []
    const totalSets = 6

    // Add completed sets
    for (let i = 0; i < currentCompletedSets; i++) {
      allSets.push({
        Id: i + 1,
        SetNo: `${i + 1}`,
        Reps: 10,
        Weight: { Kg: 80, Lb: 175 },
        IsNext: false,
        IsWarmups: false,
        IsFinished: true,
      })
    }

    // Add current set (next to complete)
    if (currentCompletedSets < totalSets) {
      allSets.push({
        Id: currentCompletedSets + 1,
        SetNo: `${currentCompletedSets + 1}`,
        Reps: 10,
        Weight: { Kg: 80, Lb: 175 },
        IsNext: true,
        IsWarmups: false,
        IsFinished: false,
      })
    }

    // Add remaining sets
    for (let i = currentCompletedSets + 1; i < totalSets; i++) {
      allSets.push({
        Id: i + 1,
        SetNo: `${i + 1}`,
        Reps: 10,
        Weight: { Kg: 80, Lb: 175 },
        IsNext: false,
        IsWarmups: false,
        IsFinished: false,
      })
    }

    return allSets
  },
}))

vi.mock('@/components/workout-v2/RestTimer', () => ({
  RestTimer: () => null,
  useRestTimer: () => ({ startRestTimer: vi.fn() }),
}))

vi.mock('@/hooks/useExerciseV2Actions', () => ({
  useExerciseV2Actions: () => ({
    handleCompleteSet: vi.fn(() => {
      // Simulate incrementing completed sets
      currentCompletedSets++
      mockHandleSaveSet()
    }),
    handleSkipSet: vi.fn(),
  }),
}))

describe('ExercisePageV2Client - Set Counter Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    currentCompletedSets = 0

    mockSaveSet = vi.fn().mockResolvedValue(true)
    mockNextSet = vi.fn()
    mockHandleSaveSet = vi.fn()
    mockWorkoutSession = {
      id: 'test-123',
      exercises: [
        {
          exerciseId: 1,
          sets: [],
        },
      ],
    }
  })

  it('should increment set counter when Save set is tapped', async () => {
    // Given: Exercise page with 0 completed sets
    const { rerender } = render(<ExercisePageV2Client exerciseId={1} />)

    // When: Initial state shows 0/6 sets
    expect(screen.getByText('0/6 sets')).toBeInTheDocument()

    // When: User clicks Save set
    const saveButton = screen.getByRole('button', { name: 'Save set' })
    fireEvent.click(saveButton)

    // Wait for action to complete
    await waitFor(() => {
      expect(mockHandleSaveSet).toHaveBeenCalled()
    })

    // Then: Re-render with updated state
    rerender(<ExercisePageV2Client exerciseId={1} />)

    // Should show 1/6 sets after saving
    await waitFor(() => {
      expect(screen.getByText('1/6 sets')).toBeInTheDocument()
    })
  })

  it('should continue incrementing counter with each save', async () => {
    // Given: Starting with 2 completed sets
    currentCompletedSets = 2
    const { rerender } = render(<ExercisePageV2Client exerciseId={1} />)

    // When: Initial state shows 2/6 sets
    expect(screen.getByText('2/6 sets')).toBeInTheDocument()

    // When: User clicks Save set
    const saveButton = screen.getByRole('button', { name: 'Save set' })
    fireEvent.click(saveButton)

    // Wait for action
    await waitFor(() => {
      expect(mockHandleSaveSet).toHaveBeenCalled()
    })

    // Then: Re-render with updated state
    rerender(<ExercisePageV2Client exerciseId={1} />)

    // Should show 3/6 sets
    await waitFor(() => {
      expect(screen.getByText('3/6 sets')).toBeInTheDocument()
    })
  })

  it('should handle completing multiple sets up to 5/6', async () => {
    // Given: Starting with 4 completed sets
    currentCompletedSets = 4
    const { rerender } = render(<ExercisePageV2Client exerciseId={1} />)

    // When: Initial state shows 4/6 sets
    expect(screen.getByText('4/6 sets')).toBeInTheDocument()

    // When: User clicks Save set
    const saveButton = screen.getByRole('button', { name: 'Save set' })
    fireEvent.click(saveButton)

    // Wait for action
    await waitFor(() => {
      expect(mockHandleSaveSet).toHaveBeenCalled()
    })

    // Then: Re-render with updated state
    rerender(<ExercisePageV2Client exerciseId={1} />)

    // Should show 5/6 sets (not the last set, so no complete view)
    await waitFor(() => {
      expect(screen.getByText('5/6 sets')).toBeInTheDocument()
    })
  })
})
