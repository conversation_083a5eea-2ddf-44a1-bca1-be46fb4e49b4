# Web App Announcement Format

This document defines the standard format for Dr. Muscle web app update announcements. These announcements are used internally and are occasionally viewed by users and customers. They are indexed by Google for SEO.

## Format Template

```markdown
## [Main Feature/Improvement Title]

### ✨ New

- **[Feature Name]** - [User-focused description of the feature]
- **[Feature Name]** - [User-focused description of the feature]

### 🚀 Improvements

- **[Improvement Name]** - [Description of what was improved and why it matters]
- **[Another improvement]** - [Description]

### 🔧 Fixes

- Fixed [issue description in user-friendly terms]
- Fixed [issue description in user-friendly terms]

### 📊 Tech

- PR: [#NUMBER](https://github.com/dr-muscle/DrMuscleWebApp/pull/NUMBER) - [Brief technical description]
- Performance:

- Bundle size: [SIZE] ([CHANGE from previous])
- LCP: [TIME] (✅/⚠️/❌)
- Test coverage: [PERCENTAGE] ([CHANGE])
  - Version: [VERSION]

### 🔜 Coming soon

- [Upcoming feature or improvement]
```

## Title Guidelines

The title should highlight the main feature or improvement, following these patterns:

- "Added [Feature] to [Location]" - For new features
- "Unified Workout Tracking" - For major UI/UX changes
- "Better [Feature Name]" - For improvements
- "Fixed: [Specific Issue]" - For critical bug fixes
- "[Feature] Now Available" - For new feature releases
- "Improved [Area]" - For performance or compatibility updates

## Section Emojis

Use only one emoji per section header:

- ✨ **New** - New features section
- 🚀 **Improvements** - Improvements section
- 🔧 **Fixes** - Bug fixes section
- 📊 **Tech** - Technical details section
- 🔜 **Coming soon** - Upcoming features section

## Performance Indicators

Use these only in the Tech section:

- ✅ Meeting performance targets
- ⚠️ Close to limits
- ❌ Exceeding limits

## Writing Guidelines

1. **User-First Language**: Lead with what users care about, not technical implementation details
2. **Concise Descriptions**: One line per item, clear and scannable
3. **Use Dashes for Lists**: Use `-` instead of bullet points for proper markdown list formatting
4. **PR Links**: Always link PR numbers for developer reference
5. **Performance Transparency**: Include key metrics to show commitment to quality
6. **SEO Optimization**: Use descriptive, searchable terms in feature descriptions

## Performance Thresholds

- Bundle size: < 150KB (✅), 150-160KB (⚠️), > 160KB (❌)
- LCP: < 1s (✅), 1-1.5s (⚠️), > 1.5s (❌)
- Test coverage: > 90% (✅), 80-90% (⚠️), < 80% (❌)

## Example

```markdown
## Added "Today's Sets" to Exercise Page

### ✨ New

- **Today's Sets view** - All your sets in one unified view for easier tracking
- **Smart rest timer** - Automatically adjusts based on your performance

### 🚀 Improvements

- **Faster exercise switching** - 50% reduction in page load time between exercises
- **Larger text** - 30% bigger text for better readability

### 🔧 Fixes

- Fixed subscription not recognized after app reinstall
- Fixed rest timer continuing after workout completion

### 📊 Tech

- PR: [#511](https://github.com/dr-muscle/DrMuscleWebApp/pull/511) - Add unified sets view
- PR: [#512](https://github.com/dr-muscle/DrMuscleWebApp/pull/512) - Optimize exercise navigation
- Performance:

- Bundle size: 147KB (-3KB)
- LCP: 0.9s (✅)
- Test coverage: 92% (+2%)
  - Version: 0.2025.08.01.1

### 🔜 Coming soon

- Streamlined account creation with social login options
```
