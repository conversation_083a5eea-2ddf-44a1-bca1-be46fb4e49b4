import { test, expect } from '@playwright/test'
import { fillPasswordField } from './helpers/auth-helpers'

test.describe('E2E Test Suite Check', () => {
  test('should load login page without errors', async ({ page }) => {
    await page.goto('/login')

    // Check page loaded
    await expect(page).toHaveTitle(/Dr\. Muscle/)

    // Check form elements are visible
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.locator('#password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible()
  })

  test('should fill password field without selector conflicts', async ({
    page,
  }) => {
    await page.goto('/login')

    // This should not throw strict mode violation
    await fillPasswordField(page, 'testpassword')

    // Verify password was filled
    const passwordValue = await page.locator('#password').inputValue()
    expect(passwordValue).toBe('testpassword')
  })

  test('should handle empty form submission', async ({ page }) => {
    await page.goto('/login')

    // Try to submit empty form
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Button should be disabled initially
    await expect(loginButton).toBeDisabled()
  })
})
