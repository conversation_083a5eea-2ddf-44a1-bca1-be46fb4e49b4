import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import LoginPage from '../page'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}))

// Track props passed to LoginPageClient
let capturedReturnUrl: string | undefined

vi.mock('@/components/LoginPageClient', () => ({
  LoginPageClient: ({ returnUrl }: { returnUrl?: string }) => {
    capturedReturnUrl = returnUrl
    return <div data-testid="login-page-client">LoginPageClient Mock</div>
  },
}))

describe('LoginPage', () => {
  it('should always pass /program as returnUrl', async () => {
    capturedReturnUrl = undefined
    render(await LoginPage())

    expect(capturedReturnUrl).toBe('/program')
  })
})
