import { test, expect } from '@playwright/test'
import { login } from './helpers/auth-helper'

test.describe('Exercise V2 Text Size', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
  })

  test('should display larger explainer text on exercise V2 page', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', { name: /start/i })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)
    await page.waitForLoadState('networkidle')

    // Check "Set X of Y" text has text-lg class
    const setCounter = page.locator('text=/Set \\d+ of \\d+/')
    await expect(setCounter).toBeVisible()
    const setCounterClass = await setCounter.getAttribute('class')
    expect(setCounterClass).toContain('text-lg')
    expect(setCounterClass).not.toContain('text-sm')

    // Check "REPS" label has text-lg class
    const repsLabel = page.locator('text=REPS')
    await expect(repsLabel).toBeVisible()
    const repsLabelClass = await repsLabel.getAttribute('class')
    expect(repsLabelClass).toContain('text-lg')
    expect(repsLabelClass).not.toContain('text-sm')

    // Check unit label (KG/LBS) has text-lg class
    const unitLabel = page.locator('text=/^(KG|LBS)$/')
    await expect(unitLabel).toBeVisible()
    const unitLabelClass = await unitLabel.getAttribute('class')
    expect(unitLabelClass).toContain('text-lg')
    expect(unitLabelClass).not.toContain('text-sm')

    // Check swipe hint has text-lg class
    const swipeHint = page.locator(
      'text=Swipe left to skip · right to complete'
    )
    await expect(swipeHint).toBeVisible()
    const swipeHintClass = await swipeHint.getAttribute('class')
    expect(swipeHintClass).toContain('text-lg')
    expect(swipeHintClass).not.toContain('text-sm')

    // Check "Today's sets" header has text-lg class
    const todaysSetsHeader = page.locator("text=Today's sets")
    await expect(todaysSetsHeader).toBeVisible()
    const todaysSetsClass = await todaysSetsHeader.getAttribute('class')
    expect(todaysSetsClass).toContain('text-lg')
    expect(todaysSetsClass).not.toContain('text-sm')
  })
})
