import { test, expect } from '@playwright/test'

// Helper function to perform login
async function login(page: any) {
  await page.goto('/login')
  await page.fill('[type="email"]', '<EMAIL>')
  await page.fill('[type="password"]', 'Dr123456')
  await page.click('button[type="submit"]')
  await page.waitForURL('/program', { timeout: 30000 })

  // Navigate to workout page
  await page.goto('/workout')
}

test.describe('Skip Exercise Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Login to the app
    await login(page)

    // Wait for workout to load
    await page.waitForSelector('[data-testid="exercise-card"]', {
      state: 'visible',
      timeout: 30000,
    })
  })

  test('should skip exercise from overflow menu', async ({ page }) => {
    // Find the first exercise card
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()

    // Open the overflow menu
    await firstExerciseCard.locator('[aria-label="Exercise options"]').click()

    // Wait for menu to be visible
    await page.waitForSelector('[role="menu"]', { state: 'visible' })

    // Click skip exercise
    await page.locator('text=Skip Exercise').click()

    // Verify the exercise is marked as completed
    await expect(firstExerciseCard).toHaveClass(/opacity-60/)

    // Check for completion indicators
    const statusDot = firstExerciseCard.locator('.text-brand-primary').first()
    await expect(statusDot).toBeVisible()
    await expect(statusDot).toHaveText('●')

    // Check for checkmark
    const checkmark = firstExerciseCard.locator('.text-brand-primary').last()
    await expect(checkmark).toContainText('✓')
  })

  test('should maintain skip state after page refresh', async ({ page }) => {
    // Skip the first exercise
    const firstExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()
    await firstExerciseCard.locator('[aria-label="Exercise options"]').click()
    await page.waitForSelector('[role="menu"]', { state: 'visible' })
    await page.locator('text=Skip Exercise').click()

    // Verify it's marked as completed
    await expect(firstExerciseCard).toHaveClass(/opacity-60/)

    // Reload the page
    await page.reload()
    await page.waitForSelector('[data-testid="exercise-card"]', {
      state: 'visible',
      timeout: 30000,
    })

    // Verify the exercise is still marked as completed
    const refreshedExerciseCard = page
      .locator('[data-testid="exercise-card"]')
      .first()
    await expect(refreshedExerciseCard).toHaveClass(/opacity-60/)

    // Check completion indicators are still present
    const statusDot = refreshedExerciseCard
      .locator('.text-brand-primary')
      .first()
    await expect(statusDot).toBeVisible()
    await expect(statusDot).toHaveText('●')
  })

  test('should handle skipping multiple exercises', async ({ page }) => {
    // Get all exercise cards
    const exerciseCards = page.locator('[data-testid="exercise-card"]')
    const exerciseCount = await exerciseCards.count()

    // Skip first two exercises
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < Math.min(2, exerciseCount); i++) {
      const exerciseCard = exerciseCards.nth(i)
      // eslint-disable-next-line no-await-in-loop
      await exerciseCard.locator('[aria-label="Exercise options"]').click()
      // eslint-disable-next-line no-await-in-loop
      await page.waitForSelector('[role="menu"]', { state: 'visible' })
      // eslint-disable-next-line no-await-in-loop
      await page.locator('text=Skip Exercise').click()

      // Wait for menu to close
      // eslint-disable-next-line no-await-in-loop
      await page.waitForSelector('[role="menu"]', { state: 'hidden' })

      // Verify the exercise is marked as completed
      // eslint-disable-next-line no-await-in-loop
      await expect(exerciseCard).toHaveClass(/opacity-60/)
    }

    // Verify both exercises show completion state
    if (exerciseCount >= 2) {
      await expect(exerciseCards.nth(0)).toHaveClass(/opacity-60/)
      await expect(exerciseCards.nth(1)).toHaveClass(/opacity-60/)
    }
  })
})
