import { test, expect, Page } from '@playwright/test'

test.describe('Exercise V2 - UI Spacing Improvements', () => {
  let page: Page

  test.beforeEach(async ({ browser }) => {
    // Create a new page with mobile viewport
    const context = await browser.newContext({
      viewport: { width: 375, height: 667 }, // iPhone SE size
      userAgent:
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
    })
    page = await context.newPage()

    // Mock API responses
    await page.route('**/api/auth/token', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          User: {
            Id: 1,
            Email: '<EMAIL>',
            MassUnit: 'kg',
          },
          RefreshToken: 'mock-refresh-token',
          Token: 'mock-token',
        },
      })
    })

    await page.route('**/api/userinfo', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: 1,
          Email: '<EMAIL>',
          MassUnit: 'kg',
        },
      })
    })

    await page.route('**/api/exercise/1', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: 1,
          Label: 'Bench Press',
          IsTimeBased: false,
          IsFinished: false,
        },
      })
    })

    await page.route('**/api/recommendation/1', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          ExerciseId: 1,
          Reps: 10,
          WeightInKg: 80,
          WeightInLb: 175,
          WeightIncrement: 2.5,
        },
      })
    })

    await page.route('**/api/sets/exercise/1', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          warmup: [],
          work: [
            {
              Id: -2000,
              Reps: 10,
              Weight: { Kg: 80, Lb: 175 },
              IsWarmups: false,
              IsNext: true,
              IsFinished: false,
            },
          ],
        },
      })
    })

    await page.route('**/api/exercise/*/current-set', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          Id: -2000,
          Reps: 10,
          Weight: { Kg: 80, Lb: 175 },
          IsWarmups: false,
          IsNext: true,
          IsFinished: false,
        },
      })
    })

    // Navigate to exercise page
    await page.goto('http://localhost:3000/workout/exercise-v2/1')
    await page.waitForLoadState('networkidle')
  })

  test('should have reduced spacing between explainer text and save button', async () => {
    // Wait for the current set card to be visible
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
    })

    // Get the explainer text element
    const explainerText = await page.locator(
      'text=Swipe left to skip · right to complete'
    )
    await expect(explainerText).toBeVisible()

    // Get the save button
    const saveButton = await page.locator('button:has-text("Save set")')
    await expect(saveButton).toBeVisible()

    // Verify the explainer container has mt-2 class (reduced from mt-4)
    const explainerContainer = await explainerText.locator('..')
    await expect(explainerContainer).toHaveClass(/mt-2/)

    // Verify the button wrapper has mt-2 class (reduced from mt-4)
    const buttonWrapper = await saveButton.locator('..').filter({ hasText: '' })
    await expect(buttonWrapper).toHaveClass(/mt-2/)

    // Take a screenshot for visual verification
    await page.screenshot({
      path: 'test-results/exercise-v2-spacing-reduced.png',
      fullPage: false,
      clip: {
        x: 0,
        y: 400, // Focus on the bottom area with explainer and button
        width: 375,
        height: 200,
      },
    })
  })

  test('should display explainer text with smaller font size', async () => {
    // Wait for the explainer text to be visible
    const explainerText = await page.locator(
      'text=Swipe left to skip · right to complete'
    )
    await expect(explainerText).toBeVisible()

    // Verify text has text-base class (reduced from text-lg)
    await expect(explainerText).toHaveClass(/text-base/)
  })

  test('should have consistent label spacing between REPS and weight unit', async () => {
    // Wait for the input controls to be visible
    await page.waitForSelector('[data-testid="input-controls-container"]', {
      state: 'visible',
    })

    // Check REPS label
    const repsLabel = await page.locator('text=REPS')
    await expect(repsLabel).toBeVisible()
    await expect(repsLabel).toHaveClass(/-bottom-6/)

    // Check weight unit label (KG)
    const unitLabel = await page.locator('text=KG')
    await expect(unitLabel).toBeVisible()
    await expect(unitLabel).toHaveClass(/-bottom-6/)

    // Take a screenshot showing the aligned labels
    await page.screenshot({
      path: 'test-results/exercise-v2-label-alignment.png',
      fullPage: false,
      clip: {
        x: 0,
        y: 150, // Focus on the input area with labels
        width: 375,
        height: 300,
      },
    })
  })

  test('should maintain proper visual hierarchy with all spacing changes', async () => {
    // Wait for the page to be fully loaded
    await page.waitForSelector('[data-testid="current-set-card"]', {
      state: 'visible',
    })

    // Take a full screenshot of the mobile viewport
    await page.screenshot({
      path: 'test-results/exercise-v2-complete-layout.png',
      fullPage: false, // Just the viewport
    })

    // Verify all elements are visible and properly spaced
    await expect(page.locator('[data-testid="current-set-card"]')).toBeVisible()
    await expect(
      page.locator('text=Swipe left to skip · right to complete')
    ).toBeVisible()
    await expect(page.locator('button:has-text("Save set")')).toBeVisible()
    await expect(page.locator('text=REPS')).toBeVisible()
    await expect(page.locator('text=KG')).toBeVisible()

    // Check that the overall height is reasonable (not too cramped or spaced out)
    const card = await page.locator('[data-testid="current-set-card"]')
    const cardBox = await card.boundingBox()
    expect(cardBox).not.toBeNull()

    // The card should be reasonably sized for mobile
    if (cardBox) {
      expect(cardBox.height).toBeGreaterThan(200)
      expect(cardBox.height).toBeLessThan(400)
    }
  })
})
