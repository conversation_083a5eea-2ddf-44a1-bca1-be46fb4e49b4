import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { SetCellHeader } from '../SetCellHeader'

describe('SetCellHeader', () => {
  it('should render with black background (bg-bg-primary) instead of gold (bg-brand-primary)', () => {
    const { container } = render(<SetCellHeader unit="lbs" />)

    const headerDiv = container.firstChild as HTMLElement

    // Test will fail with current implementation that uses bg-brand-primary
    expect(headerDiv).toHaveClass('bg-bg-primary')
    expect(headerDiv).not.toHaveClass('bg-brand-primary')
  })

  it('should maintain white text color (text-white)', () => {
    const { container } = render(<SetCellHeader unit="kg" />)

    const headerDiv = container.firstChild as HTMLElement
    expect(headerDiv).toHaveClass('text-white')
  })

  it('should display column headers correctly', () => {
    const { getByText } = render(<SetCellHeader unit="lbs" />)

    expect(getByText('SET')).toBeInTheDocument()
    expect(getByText('REPS')).toBeInTheDocument()
    expect(getByText('*')).toBeInTheDocument()
    expect(getByText('LBS')).toBeInTheDocument()
  })

  it('should display unit in uppercase', () => {
    const { getByText, rerender } = render(<SetCellHeader unit="kg" />)
    expect(getByText('KG')).toBeInTheDocument()

    rerender(<SetCellHeader unit="lbs" />)
    expect(getByText('LBS')).toBeInTheDocument()
  })
})
