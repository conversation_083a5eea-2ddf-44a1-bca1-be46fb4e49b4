# OAuth Deployment Configuration

## Required Environment Variables

For <PERSON>Auth (Google and Apple Sign-In) to work in production, the following environment variables MUST be set:

### Google OAuth

```bash
NEXT_PUBLIC_GOOGLE_CLIENT_ID=707210235326-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com
```

### Apple Sign-In

```bash
NEXT_PUBLIC_APPLE_TEAM_ID=7AAXZ47995
NEXT_PUBLIC_APPLE_BUNDLE_ID=com.drmaxmuscle.max
NEXT_PUBLIC_APPLE_SERVICES_ID=com.drmaxmuscle.web
```

## Deployment Checklist

1. **Vercel Deployment**:
   - Go to Project Settings → Environment Variables
   - Add all required OAuth variables
   - Redeploy the application

2. **Other Platforms**:
   - Ensure all `NEXT_PUBLIC_*` variables are available at build time
   - These are client-side variables and will be embedded in the built JavaScript

## Troubleshooting

### OAuth buttons not showing

- Check if environment variables are set in production
- Verify by checking the browser console for OAuth configuration
- The buttons are conditionally rendered based on `oauthConfig.hasAnyProvider()`

### Google Sign-In not working

- Ensure the Google Client ID is correct
- Verify the domain is authorized in Google Cloud Console
- Check browser console for Firebase initialization errors

### Apple Sign-In not working

- Verify Team ID and Services ID match Apple Developer configuration
- Ensure the domain is registered with Apple
- Check for CORS issues with Apple's OAuth endpoints

## Important Notes

- PR #423 fixed an issue where OAuth was blocked by overly broad user agent detection
- The OAuth functionality is fully implemented and working
- If buttons don't appear, it's always due to missing environment variables
