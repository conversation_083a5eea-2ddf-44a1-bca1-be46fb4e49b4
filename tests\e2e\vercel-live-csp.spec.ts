import { test, expect } from '@playwright/test'

test.describe('Vercel Live CSP Integration', () => {
  test('should not block Vercel Live iframe with CSP error', async ({
    page,
  }) => {
    // Collect console errors
    const consoleErrors: string[] = []
    page.on('console', (message) => {
      if (message.type() === 'error') {
        consoleErrors.push(message.text())
      }
    })

    // Navigate to the app
    await page.goto('/')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Check that there are no CSP errors related to vercel.live
    const cspErrors = consoleErrors.filter(
      (error) =>
        error.includes('Content Security Policy') &&
        error.includes('vercel.live')
    )

    expect(cspErrors).toHaveLength(0)
  })

  test('should have vercel.live in CSP frame-src header', async ({ page }) => {
    const response = await page.goto('/')

    // Get CSP header from response
    const cspHeader = response?.headers()['content-security-policy']
    expect(cspHeader).toBeDefined()

    // Extract frame-src directive
    const frameSrcMatch = cspHeader?.match(/frame-src([^;]+)/)
    expect(frameSrcMatch).toBeTruthy()

    const frameSrc = frameSrcMatch![1].trim()

    // Verify vercel.live is included
    expect(frameSrc).toContain('https://vercel.live')
  })
})
