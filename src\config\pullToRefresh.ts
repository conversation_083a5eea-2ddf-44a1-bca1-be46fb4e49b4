/**
 * Global configuration for pull-to-refresh behavior across the app
 */
export const PULL_TO_REFRESH_CONFIG = {
  /** Dead zone in pixels before pull starts registering */
  deadZone: 50,
  /** Threshold in pixels to trigger refresh */
  threshold: 120,
  /** Resistance factor for pull (higher = more resistance) */
  resistance: 3.5,
} as const

export type PullToRefreshConfig = typeof PULL_TO_REFRESH_CONFIG
