import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Exercise V2 Page Scrolling', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should allow scrolling to see all content including next sets', async ({
    page,
  }) => {
    // Start workout to get to exercise page
    const startButton = page.getByRole('button', { name: /start workout/i })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Wait for navigation to exercise V2 page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)

    // Wait for content to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Get the main content area
    const mainContent = page.locator(
      '.flex-1.flex.flex-col.px-4.overflow-y-auto'
    )
    await expect(mainContent).toBeVisible()

    // Verify it has overflow-y-auto class for scrolling
    await expect(mainContent).toHaveClass(/overflow-y-auto/)

    // Check that next sets section exists
    const nextSetsSection = page.getByText('Next sets')

    // Scroll down to ensure next sets are visible
    await page.evaluate(() => {
      const scrollableElement = document.querySelector('.overflow-y-auto')
      if (scrollableElement) {
        scrollableElement.scrollTop = scrollableElement.scrollHeight
      }
    })

    // Wait a bit for scroll to complete
    await page.waitForTimeout(300)

    // Verify we can see the next sets section
    await expect(nextSetsSection).toBeInViewport({ timeout: 5000 })
  })
})
