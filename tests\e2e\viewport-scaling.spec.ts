import { test, expect } from '@playwright/test'

test.describe('Viewport Scaling', () => {
  test('should allow user scaling for screenshot capability', async ({
    page,
  }) => {
    // Navigate to any page
    await page.goto('/login')

    // Get viewport meta tag
    const viewportContent = await page.evaluate(() => {
      const viewportMeta = document.querySelector('meta[name="viewport"]')
      return viewportMeta?.getAttribute('content') || ''
    })

    // Verify user-scalable is not set to no/false
    expect(viewportContent).not.toContain('user-scalable=no')
    expect(viewportContent).not.toContain('user-scalable=false')
    expect(viewportContent).not.toContain('user-scalable=0')

    // Verify other viewport properties are still present
    expect(viewportContent).toContain('width=device-width')
    expect(viewportContent).toContain('initial-scale=1')
  })

  test('should maintain viewport configuration across all pages', async ({
    page,
  }) => {
    // Test multiple pages to ensure consistency
    const pages = ['/login', '/workout', '/program']

    // Use Promise.all to avoid await in loop
    await Promise.all(
      pages.map(async (path) => {
        await page.goto(path)

        const viewportContent = await page.evaluate(() => {
          const viewportMeta = document.querySelector('meta[name="viewport"]')
          return viewportMeta?.getAttribute('content') || ''
        })

        // Verify scaling is allowed on all pages
        expect(viewportContent).not.toContain('user-scalable=no')
        expect(viewportContent).not.toContain('user-scalable=false')
      })
    )
  })
})
