import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useExercisePageInitialization } from '../useExercisePageInitialization'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('../useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('next/navigation')
vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
  }),
}))
vi.mock('@/utils/RecommendationLoadingCoordinator', () => ({
  RecommendationLoadingCoordinator: {
    getInstance: vi.fn(() => ({
      canStartLoading: vi.fn(() => true),
      startLoading: vi.fn(),
    })),
  },
}))

describe('useExercisePageInitialization - Auth Expiry', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockWorkout = {
    todaysWorkout: [
      {
        WorkoutTemplates: [{ id: 1, name: 'Test Workout' }],
      },
    ],
    isLoadingWorkout: false,
    startWorkout: vi.fn().mockResolvedValue({ success: true }),
    exercises: [
      { Id: 123, Label: 'Bench Press' },
      { Id: 456, Label: 'Squat' },
    ],
    workoutSession: { id: 'session-1' },
    loadRecommendation: vi.fn(),
    updateExerciseWorkSets: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    loadingStates: new Map(),
    getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useWorkout as any).mockReturnValue(mockWorkout)
    ;(useWorkoutStore as any).mockReturnValue(mockWorkoutStore)
  })

  describe('Auth Token Validation', () => {
    it('should redirect to login when no auth token is present', async () => {
      // Given: No auth token in store
      ;(useAuthStore as any).mockReturnValue({
        token: null,
        isAuthenticated: false,
      })

      // When: Exercise page initializes
      renderHook(() => useExercisePageInitialization(123))

      // Then: Should redirect to login immediately
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/login')
      })

      // And: Should not attempt to load exercise data
      expect(mockWorkout.startWorkout).not.toHaveBeenCalled()
      expect(mockWorkoutStore.setCurrentExerciseById).not.toHaveBeenCalled()
      expect(mockWorkout.loadRecommendation).not.toHaveBeenCalled()
    })

    it('should redirect to login when token exists but is expired (API returns 401)', async () => {
      // Given: Token exists but API will return 401
      ;(useAuthStore as any).mockReturnValue({
        token: 'expired-token',
        isAuthenticated: true,
      })

      // Mock no workout session to trigger startWorkout
      const workoutWithNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutWithNoSession)

      // Mock startWorkout to simulate 401 error
      workoutWithNoSession.startWorkout.mockRejectedValueOnce({
        response: { status: 401 },
        message: 'Unauthorized',
      })

      // When: Exercise page initializes
      renderHook(() => useExercisePageInitialization(123))

      // Then: Should attempt workout start but handle 401 gracefully
      await waitFor(() => {
        expect(workoutWithNoSession.startWorkout).toHaveBeenCalled()
        expect(mockRouter.replace).toHaveBeenCalledWith('/login')
      })
    })

    it('should proceed with initialization when valid token is present', async () => {
      // Given: Valid auth token
      ;(useAuthStore as any).mockReturnValue({
        token: 'valid-token',
        isAuthenticated: true,
      })

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should proceed with normal initialization
      await waitFor(() => {
        expect(result.current.isInitializing).toBe(false)
      })

      // And: Should set current exercise and load recommendations
      expect(mockWorkoutStore.setCurrentExerciseById).toHaveBeenCalledWith(123)
      expect(mockWorkout.loadRecommendation).toHaveBeenCalledWith(
        123,
        'Bench Press'
      )

      // And: Should NOT redirect
      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })

    it('should handle race condition when token expires during initialization', async () => {
      // Given: Token starts as valid
      const authStore = {
        token: 'valid-token',
        isAuthenticated: true,
      }
      ;(useAuthStore as any).mockReturnValue(authStore)

      // Mock workout without session to trigger initialization
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // When: Exercise page starts initializing
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Wait for initial render
      await waitFor(() => {
        expect(workoutNoSession.startWorkout).toHaveBeenCalled()
      })

      // And: Token expires and next call fails with 401
      workoutNoSession.startWorkout.mockRejectedValueOnce({
        response: { status: 401 },
      })

      // Trigger retry
      await result.current.retryInitialization()

      // Then: Should redirect to login
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/login')
      })
    })

    it('should validate auth state before any API calls', async () => {
      // Given: No auth token
      ;(useAuthStore as any).mockReturnValue({
        token: null,
        isAuthenticated: false,
      })

      // When: Retry initialization is called
      const { result } = renderHook(() => useExercisePageInitialization(123))

      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/login')
      })

      // Reset mocks
      vi.clearAllMocks()

      // When: Retry is attempted
      await result.current.retryInitialization()

      // Then: Should check auth and redirect again
      expect(mockRouter.replace).toHaveBeenCalledWith('/login')
      expect(mockWorkout.startWorkout).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should differentiate between auth errors and other errors', async () => {
      // Given: Valid token but network error
      ;(useAuthStore as any).mockReturnValue({
        token: 'valid-token',
        isAuthenticated: true,
      })

      // Mock workout without session to trigger initialization
      const workoutNoSession = { ...mockWorkout, workoutSession: null }
      ;(useWorkout as any).mockReturnValue(workoutNoSession)

      // Mock network error (not auth error)
      workoutNoSession.startWorkout.mockRejectedValueOnce(
        new Error('Network unavailable')
      )

      // When: Exercise page initializes
      const { result } = renderHook(() => useExercisePageInitialization(123))

      // Then: Should set error but NOT redirect to login
      await waitFor(() => {
        expect(result.current.loadingError).toBeTruthy()
        expect(result.current.loadingError?.message).toContain('Network')
      })

      expect(mockRouter.replace).not.toHaveBeenCalledWith('/login')
    })
  })
})
