'use client'

import React, { useState, useRef, useEffect } from 'react'
import { KebabMenuIcon } from '@/components/icons/KebabMenuIcon'
import { DurationPicker } from './DurationPicker'
import { ToggleSwitch } from './ToggleSwitch'

export function RestTimerSettings() {
  // Load preferences from localStorage
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [vibrationEnabled, setVibrationEnabled] = useState(true)
  const [restDuration, setRestDuration] = useState(120) // Default 2 minutes
  const [isDurationPickerOpen, setIsDurationPickerOpen] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const savedSoundPref = localStorage.getItem('soundEnabled')
    if (savedSoundPref !== null) {
      setSoundEnabled(savedSoundPref === 'true')
    }

    const savedVibrationPref = localStorage.getItem('vibrationEnabled')
    if (savedVibrationPref !== null) {
      setVibrationEnabled(savedVibrationPref === 'true')
    }

    const savedDuration = localStorage.getItem('restDuration')
    if (savedDuration !== null) {
      const duration = parseInt(savedDuration, 10)
      if (!Number.isNaN(duration) && duration > 0) {
        setRestDuration(Math.max(5, Math.min(600, duration)))
      }
    }
  }, [])

  const onSoundToggle = () => {
    const newValue = !soundEnabled
    setSoundEnabled(newValue)
    localStorage.setItem('soundEnabled', newValue.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))
  }

  const onVibrationToggle = () => {
    const newValue = !vibrationEnabled
    setVibrationEnabled(newValue)
    localStorage.setItem('vibrationEnabled', newValue.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))
  }

  const onDurationSelect = (duration: number) => {
    setRestDuration(duration)
    localStorage.setItem('restDuration', duration.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))
    setIsDurationPickerOpen(false)
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // Close menu on outside click
  useEffect(() => {
    if (!isOpen) return

    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isOpen])

  // Close menu on escape key
  useEffect(() => {
    if (!isOpen) return

    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen])

  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center p-2 -mr-2 rounded-lg hover:bg-bg-secondary transition-colors"
        aria-label="Timer settings"
      >
        <KebabMenuIcon size={24} className="text-text-primary" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Menu */}
          <div
            ref={menuRef}
            className="fixed top-14 right-4 z-50 w-64 bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-800 overflow-hidden animate-in slide-in-from-top-2 duration-200"
            role="dialog"
            aria-label="Timer settings menu"
          >
            <div className="py-2">
              {/* Sound Toggle */}
              <button
                onClick={onSoundToggle}
                className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
                    />
                  </svg>
                  Sound
                </div>
                <ToggleSwitch enabled={soundEnabled} />
              </button>

              {/* Vibration Toggle */}
              <button
                onClick={onVibrationToggle}
                className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Vibration
                </div>
                <ToggleSwitch enabled={vibrationEnabled} />
              </button>

              {/* Rest Duration */}
              <button
                onClick={() => setIsDurationPickerOpen(!isDurationPickerOpen)}
                className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Rest Duration
                </div>
                <div className="flex items-center">
                  <span className="text-gray-500 dark:text-gray-400 mr-2">
                    {formatDuration(restDuration)}
                  </span>
                  <svg
                    className={`w-4 h-4 transform transition-transform ${
                      isDurationPickerOpen ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </button>

              {/* Duration Picker */}
              {isDurationPickerOpen && (
                <DurationPicker
                  currentDuration={restDuration}
                  onSelect={onDurationSelect}
                  formatDuration={formatDuration}
                />
              )}
            </div>
          </div>
        </>
      )}
    </>
  )
}
