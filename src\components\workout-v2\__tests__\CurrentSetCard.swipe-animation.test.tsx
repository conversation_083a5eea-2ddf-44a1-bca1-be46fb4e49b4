import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Create a mock for animation controls
const mockAnimationControls = {
  start: vi.fn().mockResolvedValue(undefined),
}

// Mock framer-motion with proper animation tracking
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, onDragEnd, ...props }: any) => {
      // Store the onDragEnd handler for testing
      if (onDragEnd) {
        ;(global as any).__mockOnDragEnd = onDragEnd
      }
      return <div {...props}>{children}</div>
    },
  },
  useAnimation: () => mockAnimationControls,
}))

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsTimeBased: false,
  IsFinished: false,
}

const mockCurrentSet: WorkoutLogSerieModel = {
  Id: 1,
  Reps: 10,
  Weight: { Kg: 80, Lb: 175 },
  IsWarmups: false,
  IsNext: true,
  IsFinished: false,
}

const defaultProps = {
  exercise: mockExercise,
  currentSet: mockCurrentSet,
  setData: { reps: 10, weight: 80, duration: 0 },
  onSetDataChange: vi.fn(),
  onComplete: vi.fn(),
  onSkip: vi.fn(),
  isSaving: false,
  unit: 'kg' as const,
}

describe('CurrentSetCard - Swipe Animation Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Right swipe (complete) animation', () => {
    it('should exit right and re-enter from left', async () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User swipes right to complete
      const mockDragEnd = (global as any).__mockOnDragEnd
      await mockDragEnd(
        {},
        {
          offset: { x: 150 }, // Beyond threshold of 100
          velocity: { x: 0 },
        }
      )

      // Then: Card should animate exit to the right
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: 300,
        opacity: 0,
      })

      // And: Card should reset from the left side (opposite of exit)
      // This test will FAIL with current implementation because it resets to x: 0
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: -300,
        opacity: 1,
      })

      // And: Finally animate to center
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: 0,
        opacity: 1,
        transition: { duration: 0.3 },
      })
    })
  })

  describe('Left swipe (skip) animation', () => {
    it('should exit left and re-enter from right', async () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User swipes left to skip
      const mockDragEnd = (global as any).__mockOnDragEnd
      await mockDragEnd(
        {},
        {
          offset: { x: -150 }, // Beyond threshold of -100
          velocity: { x: 0 },
        }
      )

      // Then: Card should animate exit to the left
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: -300,
        opacity: 0,
      })

      // And: Card should reset from the right side (opposite of exit)
      // This test will FAIL with current implementation because it resets to x: 0
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: 300,
        opacity: 1,
      })

      // And: Finally animate to center
      expect(mockAnimationControls.start).toHaveBeenCalledWith({
        x: 0,
        opacity: 1,
        transition: { duration: 0.3 },
      })
    })
  })

  describe('Animation sequence timing', () => {
    it('should properly sequence exit and entry animations', async () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User completes a swipe
      const mockDragEnd = (global as any).__mockOnDragEnd
      await mockDragEnd(
        {},
        {
          offset: { x: 150 },
          velocity: { x: 0 },
        }
      )

      // Then: Animations should be called in correct order
      const { calls } = mockAnimationControls.start.mock

      // First call: exit animation
      expect(calls[0][0]).toMatchObject({ x: 300, opacity: 0 })

      // Second call: reset position (should be from opposite side)
      // This will FAIL - expecting x: -300 but getting x: 0
      expect(calls[1][0]).toMatchObject({ x: -300, opacity: 1 })

      // Third call: animate to center (if implemented correctly)
      // Currently this might not exist in the implementation
    })
  })

  describe('Edge cases', () => {
    it('should handle swipe below threshold (snap back)', async () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User swipes but doesn't reach threshold
      const mockDragEnd = (global as any).__mockOnDragEnd
      await mockDragEnd(
        {},
        {
          offset: { x: 50 }, // Below threshold of 100
          velocity: { x: 0 },
        }
      )

      // Then: Card should only snap back to center
      expect(mockAnimationControls.start).toHaveBeenCalledTimes(1)
      expect(mockAnimationControls.start).toHaveBeenCalledWith({ x: 0 })

      // And: Complete/Skip handlers should not be called
      expect(defaultProps.onComplete).not.toHaveBeenCalled()
      expect(defaultProps.onSkip).not.toHaveBeenCalled()
    })

    it('should handle rapid swipes gracefully', async () => {
      // Given: CurrentSetCard is rendered
      render(<CurrentSetCard {...defaultProps} />)

      // When: User performs multiple rapid swipes
      const mockDragEnd = (global as any).__mockOnDragEnd

      // First swipe right
      await mockDragEnd({}, { offset: { x: 150 }, velocity: { x: 0 } })

      // Clear previous calls
      mockAnimationControls.start.mockClear()

      // Second swipe left before animation completes
      await mockDragEnd({}, { offset: { x: -150 }, velocity: { x: 0 } })

      // Then: New animation sequence should start
      expect(mockAnimationControls.start).toHaveBeenCalled()
    })
  })
})
