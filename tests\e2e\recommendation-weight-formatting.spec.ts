import { test, expect } from '@playwright/test'
import { login } from './helpers/auth'

test.describe('Recommendation Weight Formatting', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await login(page, process.env.TEST_EMAIL!, process.env.TEST_PASSWORD!)
  })

  test('should display recommendation weights rounded to increment', async ({
    page,
  }) => {
    // Mock API response with precise decimal weights
    await page.route(
      '**/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Series: 3,
            Reps: 10,
            Weight: {
              Lb: 102.7654321,
              Kg: 46.58372,
            },
            Increments: {
              Lb: 5,
              Kg: 2.5,
            },
            WarmupsCount: 2,
            WarmUpsList: [],
          }),
        })
      }
    )

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Look for the recommendation display
    const recommendationText = page.getByTestId('recommendations')
    await expect(recommendationText).toBeVisible()

    // Verify weight is rounded to nearest 5 lbs (105 lbs)
    await expect(recommendationText).toContainText('@ 105 lbs')

    // Verify it doesn't show many decimal places
    await expect(recommendationText).not.toContainText('102.7654321')
    await expect(recommendationText).not.toContainText('102.77')
  })

  test('should handle weights without increment', async ({ page }) => {
    // Mock API response without Increments property
    await page.route(
      '**/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Series: 3,
            Reps: 10,
            Weight: {
              Lb: 135.7654321,
              Kg: 61.58372,
            },
            WarmupsCount: 2,
            WarmUpsList: [],
          }),
        })
      }
    )

    // Navigate to workout
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', { name: /start workout/i })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Look for the recommendation display
    const recommendationText = page.getByTestId('recommendations')
    await expect(recommendationText).toBeVisible()

    // Verify weight shows with 2 decimal precision when no increment
    await expect(recommendationText).toContainText('@ 135.77 lbs')
  })
})
