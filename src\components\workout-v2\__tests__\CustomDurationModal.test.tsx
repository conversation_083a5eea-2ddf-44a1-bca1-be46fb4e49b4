import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { CustomDurationModal } from '../CustomDurationModal'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, onClick, ...props }: any) => (
      <div className={className} onClick={onClick} {...props}>
        {children}
      </div>
    ),
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('CustomDurationModal', () => {
  const mockOnSelect = vi.fn()
  const mockOnClose = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render with current duration as default value', () => {
    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText(
      'Enter duration in seconds'
    ) as HTMLInputElement
    expect(input.value).toBe('90')
  })

  it('should accept valid duration and call onSelect', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, '150')

    fireEvent.click(screen.getByText('Confirm'))

    expect(mockOnSelect).toHaveBeenCalledWith(150)
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should show error for duration less than 5 seconds', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, '3')

    fireEvent.click(screen.getByText('Confirm'))

    await waitFor(() => {
      expect(
        screen.getByText('Please enter a value between 5 and 600 seconds')
      ).toBeInTheDocument()
    })
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should show error for duration greater than 600 seconds', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, '700')

    fireEvent.click(screen.getByText('Confirm'))

    await waitFor(() => {
      expect(
        screen.getByText('Please enter a value between 5 and 600 seconds')
      ).toBeInTheDocument()
    })
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should show error for non-numeric input', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, 'abc')

    fireEvent.click(screen.getByText('Confirm'))

    // Wait for error to appear
    await waitFor(() => {
      expect(
        screen.getByText('Please enter a valid number')
      ).toBeInTheDocument()
    })
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should handle decimal values by rounding', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, '90.7')

    fireEvent.click(screen.getByText('Confirm'))

    // Wait for the form submission
    await waitFor(() => {
      expect(mockOnSelect).toHaveBeenCalledWith(91) // Math.round(90.7) = 91
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('should close when cancel button is clicked', () => {
    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    fireEvent.click(screen.getByText('Cancel'))

    expect(mockOnClose).toHaveBeenCalled()
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should close when backdrop is clicked', () => {
    const { container } = render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const backdrop = container.querySelector('.fixed.inset-0.bg-black\\/60')
    fireEvent.click(backdrop!)

    expect(mockOnClose).toHaveBeenCalled()
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should close when escape key is pressed', () => {
    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    fireEvent.keyDown(document, { key: 'Escape' })

    expect(mockOnClose).toHaveBeenCalled()
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should not close when clicking on modal content', () => {
    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const modal = screen.getByTestId('custom-duration-modal')
    fireEvent.click(modal)

    expect(mockOnClose).not.toHaveBeenCalled()
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('should have proper focus management', () => {
    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    expect(document.activeElement).toBe(input)
  })

  it('should submit form on enter key', async () => {
    const user = userEvent.setup()

    render(
      <CustomDurationModal
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const input = screen.getByLabelText('Enter duration in seconds')
    await user.clear(input)
    await user.type(input, '120{enter}')

    expect(mockOnSelect).toHaveBeenCalledWith(120)
    expect(mockOnClose).toHaveBeenCalled()
  })

  describe('Mobile positioning', () => {
    it('should have higher z-index than DurationPicker backdrop', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')
      expect(modal).toHaveClass('z-[80]') // Should be higher than DurationPicker's z-50
    })

    it('should have mobile-friendly width constraints', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')

      // Should have responsive width for mobile
      expect(modal).toHaveClass('w-auto') // Auto width with inset constraints
      expect(modal).toHaveClass('max-w-sm') // But capped at small screens
    })

    it('should have safe area padding for mobile devices', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')

      // Should have safe area margin to prevent off-screen positioning
      expect(modal).toHaveClass('inset-x-4') // Horizontal inset (16px margins)
      expect(modal).toHaveClass('top-[10vh]') // Fixed top position for keyboard visibility
    })

    it('should have viewport height constraints', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')

      // Should not exceed mobile viewport height
      expect(modal).toHaveClass('max-h-[50vh]') // Max 50% of viewport height for keyboard
    })

    it('should use inset positioning for better mobile compatibility', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')

      // Modal should use inset for mobile-friendly positioning
      expect(modal).toHaveClass('inset-x-4') // Horizontal inset
      expect(modal).toHaveClass('top-[10vh]') // Fixed top position
      expect(modal).not.toHaveClass('top-1/2') // Should not use vertical centering
    })

    it('should have opaque background for better visibility', () => {
      render(
        <CustomDurationModal
          currentDuration={90}
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      )

      const modal = screen.getByTestId('custom-duration-modal')

      // Modal should have solid background, not transparent
      expect(modal).toHaveClass('bg-surface-primary')
      expect(modal).not.toHaveClass('bg-surface-primary/90') // Should not be transparent
    })
  })
})
