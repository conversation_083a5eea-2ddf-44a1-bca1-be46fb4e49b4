/**
 * Singleton coordinator for managing recommendation loading states
 * Prevents duplicate API calls and provides centralized loading state management
 */
export class RecommendationLoadingCoordinator {
  private static instance: RecommendationLoadingCoordinator | null = null

  private loadingStates: Map<number, boolean> = new Map()

  private loadTimestamps: Map<number, number> = new Map()

  private completedLoads: Set<number> = new Set()

  private stats = {
    totalRequests: 0,
    successfulLoads: 0,
    failedLoads: 0,
  }

  private timeouts: Map<number, NodeJS.Timeout> = new Map()

  private constructor() {
    // Private constructor for singleton pattern
  }

  public static getInstance(): RecommendationLoadingCoordinator {
    if (!RecommendationLoadingCoordinator.instance) {
      RecommendationLoadingCoordinator.instance =
        new RecommendationLoadingCoordinator()
    }
    return RecommendationLoadingCoordinator.instance
  }

  /**
   * Check if an exercise can start loading
   */
  public canStartLoading(exerciseId: number): boolean {
    return !this.loadingStates.get(exerciseId)
  }

  /**
   * Start loading for an exercise
   */
  public startLoading(
    exerciseId: number,
    options?: { timeout?: number }
  ): void {
    if (this.loadingStates.get(exerciseId)) {
      return // Already loading
    }

    this.loadingStates.set(exerciseId, true)
    this.loadTimestamps.set(exerciseId, Date.now())
    this.stats.totalRequests++

    // Set timeout if specified
    if (options?.timeout) {
      const timeoutId = setTimeout(() => {
        this.failLoading(exerciseId)
      }, options.timeout)
      this.timeouts.set(exerciseId, timeoutId)
    }
  }

  /**
   * Mark exercise loading as complete
   */
  public completeLoading(exerciseId: number): void {
    if (!this.loadingStates.get(exerciseId)) {
      return // Not loading
    }

    this.loadingStates.delete(exerciseId)
    this.loadTimestamps.delete(exerciseId)
    this.completedLoads.add(exerciseId)
    this.stats.successfulLoads++

    // Clear timeout if exists
    const timeoutId = this.timeouts.get(exerciseId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.timeouts.delete(exerciseId)
    }
  }

  /**
   * Mark exercise loading as failed
   */
  public failLoading(exerciseId: number): void {
    if (!this.loadingStates.get(exerciseId)) {
      return // Not loading
    }

    this.loadingStates.delete(exerciseId)
    this.loadTimestamps.delete(exerciseId)
    this.stats.failedLoads++

    // Clear timeout if exists
    const timeoutId = this.timeouts.get(exerciseId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.timeouts.delete(exerciseId)
    }
  }

  /**
   * Check if an exercise is currently loading
   */
  public isLoading(exerciseId: number): boolean {
    return this.loadingStates.get(exerciseId) || false
  }

  /**
   * Check if any of the given exercises are loading
   */
  public isAnyLoading(exerciseIds: number[]): boolean {
    return exerciseIds.some((id) => this.isLoading(id))
  }

  /**
   * Check if all of the given exercises are loading
   */
  public areAllLoading(exerciseIds: number[]): boolean {
    return (
      exerciseIds.length > 0 && exerciseIds.every((id) => this.isLoading(id))
    )
  }

  /**
   * Start loading for multiple exercises
   */
  public startBatchLoading(exerciseIds: number[]): void {
    exerciseIds.forEach((id) => this.startLoading(id))
  }

  /**
   * Get loading statistics
   */
  public getStats(): {
    totalRequests: number
    successfulLoads: number
    failedLoads: number
    currentlyLoading: number
  } {
    return {
      ...this.stats,
      currentlyLoading: this.loadingStates.size,
    }
  }

  /**
   * Reset all loading states (useful for testing)
   */
  public reset(): void {
    // Clear all timeouts
    this.timeouts.forEach((timeout) => clearTimeout(timeout))

    this.loadingStates.clear()
    this.loadTimestamps.clear()
    this.completedLoads.clear()
    this.timeouts.clear()
    this.stats = {
      totalRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
    }
  }

  /**
   * Get all currently loading exercise IDs
   */
  public getLoadingExerciseIds(): number[] {
    return Array.from(this.loadingStates.keys())
  }

  /**
   * Check how long an exercise has been loading
   */
  public getLoadingDuration(exerciseId: number): number | null {
    const timestamp = this.loadTimestamps.get(exerciseId)
    if (!timestamp) {
      return null
    }
    return Date.now() - timestamp
  }

  /**
   * Check if a specific exercise has completed loading
   */
  public isCompleted(exerciseId: number): boolean {
    return this.completedLoads.has(exerciseId)
  }
}
