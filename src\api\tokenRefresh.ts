import axios from 'axios'

// API base URL - use environment variable or default to production
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL ||
  (process.env.NODE_ENV === 'development'
    ? ''
    : 'https://drmuscle.azurewebsites.net')

/**
 * Refresh access token using refresh token
 * Separated from auth.ts to avoid circular dependency with client.ts
 * Uses a separate axios instance to avoid circular dependency
 */
export async function refreshToken(refreshTokenValue: string): Promise<{
  access_token: string
  refresh_token: string
  expires_in: number
}> {
  const response = await axios.post(
    `${API_BASE_URL}/api/Account/Refresh`,
    {
      refreshToken: refreshTokenValue,
    },
    {
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    }
  )
  return response.data
}
