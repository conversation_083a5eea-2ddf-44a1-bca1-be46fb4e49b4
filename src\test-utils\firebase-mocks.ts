/**
 * Firebase mocks for testing
 */

import { vi } from 'vitest'

// Mock Firebase Auth
export const mockFirebaseAuth = {
  currentUser: null,
  signInWithPopup: vi.fn(),
  signInWithRedirect: vi.fn(),
  signOut: vi.fn(),
  onAuthStateChanged: vi.fn(),
}

// Mock Firebase User
export const mockFirebaseUser = {
  uid: 'test-uid',
  email: '<EMAIL>',
  displayName: 'Test User',
  photoURL: 'https://example.com/photo.jpg',
  getIdToken: vi.fn().mockResolvedValue('mock-id-token'),
}

// Mock Firebase UserCredential
export const mockUserCredential = {
  user: mockFirebaseUser,
  credential: {
    providerId: 'google.com',
    signInMethod: 'google.com',
  },
  operationType: 'signIn',
}

// Mock Google Auth Provider
export const mockGoogleAuthProvider = {
  providerId: 'google.com',
  addScope: vi.fn(),
  setCustomParameters: vi.fn(),
}

// Mock Apple Auth Provider
export const mockAppleAuthProvider = {
  providerId: 'apple.com',
  addScope: vi.fn(),
  setCustomParameters: vi.fn(),
}

// Mock Firebase OAuth Helper
export const mockFirebaseOAuthHelper = {
  initialize: vi.fn().mockResolvedValue(undefined),
  isReady: vi.fn().mockReturnValue(true),
  signInWithGoogle: vi.fn(),
  signInWithApple: vi.fn(),
}

/**
 * Setup Firebase mocks
 */
export function setupFirebaseMocks() {
  // Mock Firebase modules
  vi.mock('firebase/auth', () => ({
    getAuth: vi.fn(() => mockFirebaseAuth),
    signInWithPopup: vi.fn(),
    signInWithRedirect: vi.fn(),
    GoogleAuthProvider: vi.fn(() => mockGoogleAuthProvider),
    OAuthProvider: vi.fn(() => mockAppleAuthProvider),
  }))

  vi.mock('firebase/app', () => ({
    initializeApp: vi.fn(),
    getApps: vi.fn(() => []),
    getApp: vi.fn(),
  }))

  // Mock our Firebase OAuth helper
  vi.mock('@/utils/oauth/firebaseOAuth-refactored', () => ({
    FirebaseOAuthHelper: mockFirebaseOAuthHelper,
  }))

  // Mock Firebase config
  vi.mock('@/config/firebase', () => ({
    firebaseConfig: {
      apiKey: 'test-api-key',
      authDomain: 'test-domain.firebaseapp.com',
      projectId: 'test-project',
    },
    firebaseOAuthConfig: {
      googleClientId: 'test-google-client-id',
      apple: {
        teamId: 'test-team-id',
        bundleId: 'test-bundle-id',
      },
    },
    getFirebaseApp: vi.fn(),
    getFirebaseAuth: vi.fn(() => mockFirebaseAuth),
    isFirebaseInitialized: vi.fn(() => true),
  }))
}

/**
 * Mock successful OAuth sign-in
 */
export function mockSuccessfulOAuthSignIn() {
  mockFirebaseOAuthHelper.signInWithGoogle.mockImplementation((onSuccess) => {
    onSuccess({
      token: 'mock-oauth-token',
      user: {
        id: 'test-uid',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'https://example.com/photo.jpg',
      },
      provider: 'google',
    })
    return Promise.resolve()
  })

  mockFirebaseOAuthHelper.signInWithApple.mockImplementation((onSuccess) => {
    onSuccess({
      token: 'mock-oauth-token',
      user: {
        id: 'test-uid',
        email: '<EMAIL>',
        name: 'Test User',
      },
      provider: 'apple',
    })
    return Promise.resolve()
  })
}

/**
 * Mock OAuth sign-in failure
 */
export function mockFailedOAuthSignIn(errorCode = 'auth/popup-closed-by-user') {
  const error = {
    code: errorCode,
    message: 'OAuth sign-in failed',
    provider: 'google',
  }

  mockFirebaseOAuthHelper.signInWithGoogle.mockImplementation(
    (_onSuccess, onError) => {
      onError(error)
      return Promise.resolve()
    }
  )

  mockFirebaseOAuthHelper.signInWithApple.mockImplementation(
    (_onSuccess, onError) => {
      onError({ ...error, provider: 'apple' })
      return Promise.resolve()
    }
  )
}

/**
 * Mock Firebase initialization failure
 */
export function mockFirebaseInitializationFailure() {
  mockFirebaseOAuthHelper.initialize.mockRejectedValue(new Error('Init failed'))
  mockFirebaseOAuthHelper.isReady.mockReturnValue(false)
}

/**
 * Reset Firebase mocks
 */
export function resetFirebaseMocks() {
  Object.values(mockFirebaseOAuthHelper).forEach((mock) => {
    if (vi.isMockFunction(mock)) {
      mock.mockClear()
    }
  })

  mockFirebaseAuth.signInWithPopup.mockClear()
  mockFirebaseAuth.signInWithRedirect.mockClear()
  mockFirebaseAuth.signOut.mockClear()
  mockFirebaseAuth.onAuthStateChanged.mockClear()

  mockFirebaseUser.getIdToken.mockClear()
}
