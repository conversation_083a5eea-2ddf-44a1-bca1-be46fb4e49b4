import { test, expect } from '@playwright/test'

test.describe('Mobile Custom Duration Modal Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE

    // Go to exercise page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForLoadState('networkidle')
  })

  test('custom duration modal should be fully visible on mobile @critical', async ({
    page,
  }) => {
    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()

    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Verify modal is visible and properly positioned
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Check modal is within viewport
    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBox && viewport) {
      // Modal should be fully within viewport
      expect(modalBox.x).toBeGreaterThanOrEqual(0)
      expect(modalBox.y).toBeGreaterThanOrEqual(0)
      expect(modalBox.x + modalBox.width).toBeLessThanOrEqual(viewport.width)
      expect(modalBox.y + modalBox.height).toBeLessThanOrEqual(viewport.height)

      // Modal should have proper margins (at least 16px)
      expect(modalBox.x).toBeGreaterThanOrEqual(16)
      expect(
        viewport.width - (modalBox.x + modalBox.width)
      ).toBeGreaterThanOrEqual(16)
    }

    // Modal should be centered horizontally
    if (modalBox && viewport) {
      const centerX = modalBox.x + modalBox.width / 2
      const viewportCenterX = viewport.width / 2
      expect(Math.abs(centerX - viewportCenterX)).toBeLessThan(20) // 20px tolerance
    }

    // Modal content should be visible and not transparent
    const modalBackground = await modal.evaluate(
      (el) => window.getComputedStyle(el).backgroundColor
    )
    expect(modalBackground).not.toContain('rgba') // Should not be transparent

    // Input should be functional
    const input = page.locator('#duration-input')
    await expect(input).toBeVisible()
    await input.fill('180')
    expect(await input.inputValue()).toBe('180')

    // Buttons should be clickable
    await page.click('button:has-text("Confirm")')
    await expect(modal).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('3:00')
  })

  test('modal should work on very narrow viewport (320px) @critical', async ({
    page,
  }) => {
    // Set very narrow viewport
    await page.setViewportSize({ width: 320, height: 568 })

    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Open custom duration modal
    await page.click('[data-testid="duration-setting-button"]')
    await page.click('[data-testid="duration-option-custom"]')

    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Check modal fits in narrow viewport
    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBox && viewport) {
      expect(modalBox.x + modalBox.width).toBeLessThanOrEqual(viewport.width)
      expect(modalBox.x).toBeGreaterThanOrEqual(16) // Minimum margin
    }
  })
})
