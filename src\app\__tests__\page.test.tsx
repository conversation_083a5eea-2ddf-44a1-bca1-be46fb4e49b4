import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import Home from '../page'

// Mock dependencies
const mockPush = vi.fn()
const mockReplace = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
  }),
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: false,
  })),
}))

describe('Home Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should display title and subtitle on separate lines', () => {
    render(<Home />)

    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toBeDefined()

    // Title should only contain "Dr. Muscle X"
    expect(heading.textContent).toBe('Dr. Muscle X')

    // Subtitle should be in a separate paragraph
    const subtitle = screen.getByText("World's Fastest AI Personal Trainer")
    expect(subtitle).toBeDefined()
    expect(subtitle.tagName).toBe('P')
  })

  it('should have gold gradient styling on title', () => {
    render(<Home />)

    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading.className).toContain('text-gradient-gold')
  })

  it('should have whitespace-nowrap to prevent wrapping on both title and subtitle', () => {
    render(<Home />)

    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading.className).toContain('whitespace-nowrap')

    const subtitle = screen.getByText("World's Fastest AI Personal Trainer")
    expect(subtitle.className).toContain('whitespace-nowrap')
  })

  it('should redirect to login after delay when not authenticated', () => {
    render(<Home />)

    // Should not redirect immediately
    expect(mockReplace).not.toHaveBeenCalled()

    // Fast forward past delay
    vi.advanceTimersByTime(400)

    // Should redirect to login
    expect(mockReplace).toHaveBeenCalledWith('/login')
  })
})
