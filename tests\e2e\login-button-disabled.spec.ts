import { test, expect } from '@playwright/test'

test.describe('<PERSON>gin <PERSON> Disabled State', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    // Wait for login form to be visible
    await page.waitForSelector('[data-testid="login-form"]')
  })

  test('should show disabled button when fields are empty', async ({
    page,
  }) => {
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Button should be disabled initially
    await expect(loginButton).toBeDisabled()

    // Button should have reduced opacity
    const opacity = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).opacity
    })
    expect(parseFloat(opacity)).toBeLessThan(1)

    // But<PERSON> should have not-allowed cursor
    const cursor = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).cursor
    })
    expect(cursor).toBe('not-allowed')
  })

  test('should not animate on hover when disabled', async ({ page }) => {
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Get initial styles
    const initialTransform = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).transform
    })

    // Hover over disabled button
    await loginButton.hover()

    // Transform should not change (no scale animation)
    const hoverTransform = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).transform
    })
    expect(hoverTransform).toBe(initialTransform)
  })

  test('should not animate on tap in mobile viewport', async ({ page }) => {
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Get initial styles
    const initialTransform = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).transform
    })

    // Try to tap on disabled button (should not work since button is disabled)
    await loginButton.tap({ force: true }) // Force tap on disabled element

    // Transform should not change (no scale animation)
    const tapTransform = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).transform
    })
    expect(tapTransform).toBe(initialTransform)
  })

  test('should enable button when email and password are filled', async ({
    page,
  }) => {
    const emailInput = page.getByLabel('Email')
    const passwordInput = page.locator('#password')
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Fill in email and password
    await emailInput.fill('<EMAIL>')
    await passwordInput.fill('password123')

    // Button should be enabled
    await expect(loginButton).toBeEnabled()

    // Button should have full opacity
    const opacity = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).opacity
    })
    expect(parseFloat(opacity)).toBe(1)

    // Button should have pointer cursor
    const cursor = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).cursor
    })
    expect(cursor).toBe('pointer')
  })

  test('should animate on hover when enabled', async ({ page }) => {
    const emailInput = page.getByLabel('Email')
    const passwordInput = page.locator('#password')
    const loginButton = page.getByRole('button', { name: 'Login' })

    // Fill in email and password to enable button
    await emailInput.fill('<EMAIL>')
    await passwordInput.fill('password123')

    // Hover should now cause visual changes
    await loginButton.hover()

    // Verify hover styles are applied (e.g., shadow changes)
    const boxShadow = await loginButton.evaluate((el) => {
      return window.getComputedStyle(el).boxShadow
    })
    expect(boxShadow).not.toBe('none')
  })
})
