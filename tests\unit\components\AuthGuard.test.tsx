import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { AuthGuard } from '@/components/AuthGuard'
import { useAuthStore } from '@/stores/authStore'
import { useRouter, usePathname } from 'next/navigation'

// Mock dependencies
vi.mock('@/stores/authStore')
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  usePathname: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  })),
}))

describe('AuthGuard', () => {
  const mockPush = vi.fn()
  const mockReplace = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
    } as ReturnType<typeof useRouter>)

    vi.mocked(usePathname).mockReturnValue('/workout')
  })

  describe('Authentication Protection', () => {
    it('should render children when user is authenticated', () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        hasHydrated: true,
        user: { id: '1', email: '<EMAIL>' },
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      expect(screen.getByText('Protected Content')).toBeInTheDocument()
    })

    it('should redirect to login when user is not authenticated', async () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('/login?from=%2Fworkout')
      })
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    })

    it('should show loading state while checking authentication', () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      expect(screen.getByTestId('auth-loading')).toBeInTheDocument()
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    })

    it('should show loading state while hydrating', () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        hasHydrated: false,
        user: { id: '1', email: '<EMAIL>' },
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      expect(screen.getByTestId('auth-loading')).toBeInTheDocument()
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    })
  })

  describe('Return URL Handling', () => {
    it('should preserve current URL for post-login redirect', async () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/workout/exercise/123')
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith(
          '/login?from=%2Fworkout%2Fexercise%2F123'
        )
      })
    })

    it('should not redirect if on login page', () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/login')
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Login Page</div>
        </AuthGuard>
      )

      // Then
      expect(mockReplace).not.toHaveBeenCalled()
      expect(screen.getByText('Login Page')).toBeInTheDocument()
    })

    it('should not redirect until hydration is complete', () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/workout')
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: false,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      expect(mockReplace).not.toHaveBeenCalled()
      expect(screen.getByTestId('auth-loading')).toBeInTheDocument()
    })
  })

  describe('Token Expiration', () => {
    it('should handle token expiration gracefully', async () => {
      // Given - initially authenticated
      const { rerender } = render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        hasHydrated: true,
        user: { id: '1', email: '<EMAIL>' },
      } as ReturnType<typeof useAuthStore>)

      // When - token expires
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      rerender(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('/login?from=%2Fworkout')
      })
    })
  })

  describe('Custom Configuration', () => {
    it('should allow custom loading component', () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      function CustomLoader() {
        return <div>Custom Loading...</div>
      }

      // When
      render(
        <AuthGuard loadingComponent={<CustomLoader />}>
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      expect(screen.getByText('Custom Loading...')).toBeInTheDocument()
    })

    it('should allow custom redirect path', async () => {
      // Given
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard redirectTo="/custom-login">
          <div>Protected Content</div>
        </AuthGuard>
      )

      // Then
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith(
          '/custom-login?from=%2Fworkout'
        )
      })
    })

    it('should exclude certain paths from protection', () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/about')
      vi.mocked(useAuthStore).mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        hasHydrated: true,
        user: null,
      } as ReturnType<typeof useAuthStore>)

      // When
      render(
        <AuthGuard excludePaths={['/about', '/privacy']}>
          <div>Public Content</div>
        </AuthGuard>
      )

      // Then
      expect(mockReplace).not.toHaveBeenCalled()
      expect(screen.getByText('Public Content')).toBeInTheDocument()
    })
  })
})
