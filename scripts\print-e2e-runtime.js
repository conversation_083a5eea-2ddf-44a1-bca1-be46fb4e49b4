#!/usr/bin/env node

/**
 * E2E Runtime Monitor
 * Reads Playwright JSON report, prints total duration, and enforces performance targets
 * Part of the CI optimization plan for DrMuscleWebApp
 */

const fs = require('fs')
const path = require('path')

// Configuration
const REPORT_PATH = path.join(process.cwd(), 'test-results', 'results.json')
const MAX_RUNTIME_MINUTES = 30
const MAX_RUNTIME_MS = MAX_RUNTIME_MINUTES * 60 * 1000

function formatDuration(ms) {
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}m ${seconds}s`
}

function printRuntimeReport() {
  console.log('📊 E2E Runtime Report')
  console.log('=' .repeat(50))

  // Check if report file exists
  if (!fs.existsSync(REPORT_PATH)) {
    console.error('❌ Playwright JSON report not found at:', REPORT_PATH)
    console.error('Make sure <PERSON><PERSON> is configured to generate JSON reports')
    process.exit(1)
  }

  let report
  try {
    const reportContent = fs.readFileSync(REPORT_PATH, 'utf-8')
    report = JSON.parse(reportContent)
  } catch (error) {
    console.error('❌ Failed to parse Playwright report:', error.message)
    process.exit(1)
  }

  // Extract timing information
  const config = report.config || {}
  const suites = report.suites || []
  const stats = report.stats || {}

  console.log(`📋 Test Configuration:`)
  console.log(`   Workers: ${config.workers || 'unknown'}`)
  console.log(`   Projects: ${config.projects?.length || 'unknown'}`)
  console.log(`   Timeout: ${config.timeout ? formatDuration(config.timeout) : 'unknown'}`)
  console.log(`   Retries: ${config.retries || 'unknown'}`)
  console.log('')

  console.log(`📈 Test Statistics:`)
  console.log(`   Total Tests: ${stats.total || 0}`)
  console.log(`   Passed: ${stats.passed || 0}`)
  console.log(`   Failed: ${stats.failed || 0}`)
  console.log(`   Skipped: ${stats.skipped || 0}`)
  console.log(`   Flaky: ${stats.flaky || 0}`)
  console.log('')

  // Calculate total runtime
  let totalDuration = 0
  let testCount = 0

  function processSuite(suite) {
    if (suite.specs) {
      suite.specs.forEach(spec => {
        if (spec.tests) {
          spec.tests.forEach(test => {
            if (test.results) {
              test.results.forEach(result => {
                if (result.duration) {
                  totalDuration += result.duration
                  testCount++
                }
              })
            }
          })
        }
      })
    }
    
    if (suite.suites) {
      suite.suites.forEach(processSuite)
    }
  }

  suites.forEach(processSuite)

  console.log(`⏱️  Runtime Analysis:`)
  console.log(`   Total Duration: ${formatDuration(totalDuration)}`)
  console.log(`   Average Test Duration: ${testCount > 0 ? formatDuration(totalDuration / testCount) : 'N/A'}`)
  console.log(`   Test Count: ${testCount}`)
  console.log('')

  // Environment-specific checks
  const isFullSuite = process.env.CI_FULL_SUITE === '1'
  const isApiMocked = process.env.USE_API_MOCK === '1'

  console.log(`🔧 Environment:`)
  console.log(`   CI_FULL_SUITE: ${isFullSuite ? 'ENABLED' : 'DISABLED'}`)
  console.log(`   USE_API_MOCK: ${isApiMocked ? 'ENABLED' : 'DISABLED'}`)
  console.log('')

  // Performance validation
  console.log(`🎯 Performance Validation:`)
  console.log(`   Target: ≤ ${MAX_RUNTIME_MINUTES} minutes`)
  console.log(`   Actual: ${formatDuration(totalDuration)}`)

  if (isFullSuite && totalDuration > MAX_RUNTIME_MS) {
    console.log(`❌ PERFORMANCE TARGET EXCEEDED!`)
    console.log(`   The full E2E suite took ${formatDuration(totalDuration)}, which exceeds the ${MAX_RUNTIME_MINUTES}-minute target.`)
    console.log(`   Consider:`)
    console.log(`   - Increasing shard count`)
    console.log(`   - Optimizing slow tests`)
    console.log(`   - Reducing test timeouts`)
    console.log(`   - Adding more API mocks`)
    process.exit(1)
  } else if (isFullSuite) {
    console.log(`✅ Performance target met!`)
  } else {
    console.log(`ℹ️  Performance validation skipped (not full suite)`)
  }

  // Success summary
  console.log('')
  console.log('=' .repeat(50))
  console.log(`✅ E2E Runtime Report Complete`)
  console.log(`   Duration: ${formatDuration(totalDuration)}`)
  console.log(`   Tests: ${testCount}`)
  console.log(`   Status: ${isFullSuite && totalDuration > MAX_RUNTIME_MS ? 'FAILED' : 'PASSED'}`)
}

// Run the report
if (require.main === module) {
  printRuntimeReport()
}

module.exports = { printRuntimeReport, formatDuration }
