import React from 'react'

interface ToggleSwitchProps {
  enabled: boolean
}

export function ToggleSwitch({ enabled }: ToggleSwitchProps) {
  return (
    <div
      className={`w-12 h-6 rounded-full p-1 transition-colors ${
        enabled ? 'bg-blue-500' : 'bg-gray-300'
      }`}
    >
      <div
        className={`w-4 h-4 bg-white rounded-full transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-0'
        }`}
      />
    </div>
  )
}
