import { test, expect } from '@playwright/test'

test.describe('Custom Duration Modal Keyboard Visibility', () => {
  test('modal remains visible when keyboard opens on mobile @critical', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Navigate to exercise page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('input[placeholder*="Reps"]', { timeout: 10000 })

    // Fill in and save a set to trigger rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer-container"]', {
      timeout: 5000,
    })

    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    await page.waitForSelector('[data-testid="duration-picker"]', {
      timeout: 5000,
    })

    // Click custom duration
    await page.click('[data-testid="duration-option-custom"]')

    // Wait for modal to appear
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Get the modal's position
    const modalBox = await modal.boundingBox()

    // Focus the input to trigger keyboard
    const input = page.locator('#duration-input')
    await input.focus()

    // Check that modal is positioned at top of viewport
    if (modalBox) {
      // Modal should be positioned at 10vh from top
      const expectedTop = 667 * 0.1 // 10% of viewport height
      expect(modalBox.y).toBeGreaterThanOrEqual(expectedTop - 10) // Allow small variance
      expect(modalBox.y).toBeLessThanOrEqual(expectedTop + 10)

      // Modal should not be vertically centered
      const viewportCenter = 667 / 2
      expect(modalBox.y).toBeLessThan(viewportCenter - 100) // Well above center
    }

    // Verify we can interact with the modal
    await input.fill('120')
    await page.click('button:has-text("Confirm")')

    // Modal should close and timer should update
    await expect(modal).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('2:00')
  })
})
