'use client'

import { useState, useEffect, useMemo } from 'react'
import { motion, useAnimation } from 'framer-motion'
import { useHaptic } from '@/utils/haptic'
import { useStatCounterAnimation } from '@/hooks/useStatCounterAnimation'
import { getStatsConfig } from './swipeableStatConfig'
import type { UserStats } from '@/types'

interface SwipeableStatCardProps {
  stats: UserStats | null
  isLoading: boolean
}

export function SwipeableStatCard({
  stats,
  isLoading,
}: SwipeableStatCardProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const controls = useAnimation()
  const haptic = useHaptic('light')

  // Track if we should show initial animation
  const [showInitialAnimation, setShowInitialAnimation] = useState(true)
  const [animationComplete, setAnimationComplete] = useState(false)

  // Estimated max values for loading animation - memoized to prevent re-renders
  const estimatedMaxValues = useMemo(() => [50, 10, 15000], []) // workouts, week streak, lbs lifted

  // Check if reduced motion is preferred
  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia &&
    window.matchMedia('(prefers-reduced-motion: reduce)')?.matches

  // Counter animation hook - show animation if loading OR during initial mount period
  const { animatedValues, resetAnimation } = useStatCounterAnimation({
    isLoading: isLoading || (showInitialAnimation && !animationComplete),
    hasRealData: !!stats && animationComplete,
    estimatedMaxValues,
    duration: 1500, // Shorter duration for initial animation
  })

  // Handle initial animation timing
  useEffect(() => {
    if (showInitialAnimation && !prefersReducedMotion) {
      // Show animation for 1.5 seconds on mount
      const timer = setTimeout(() => {
        setAnimationComplete(true)
        setShowInitialAnimation(false)
      }, 1500)

      return () => clearTimeout(timer)
    } else if (prefersReducedMotion) {
      // Skip animation if reduced motion is preferred
      setAnimationComplete(true)
      setShowInitialAnimation(false)
    }
    return undefined
  }, [showInitialAnimation, prefersReducedMotion])

  // Define stats configuration
  // Use animated values during initial animation, then transition to real values
  const useAnimatedValues = showInitialAnimation && !animationComplete

  const statsConfig = getStatsConfig(stats, animatedValues, useAnimatedValues)

  const currentStat = statsConfig[currentIndex]

  // Reset animation when index changes during loading
  useEffect(() => {
    if (isLoading && !stats) {
      resetAnimation()
    }
  }, [currentIndex, isLoading, stats, resetAnimation])

  const handleNext = () => {
    haptic.trigger()
    setCurrentIndex((prev) => (prev + 1) % statsConfig.length)
  }

  const handlePrevious = () => {
    haptic.trigger()
    setCurrentIndex(
      (prev) => (prev - 1 + statsConfig.length) % statsConfig.length
    )
  }

  const handleDragEnd = (_: unknown, info: { offset: { x: number } }) => {
    const swipeThreshold = 50

    if (info.offset.x < -swipeThreshold) {
      // Swiped left - go to next
      handleNext()
    } else if (info.offset.x > swipeThreshold) {
      // Swiped right - go to previous
      handlePrevious()
    }

    // Reset position
    controls.start({ x: 0 })
  }

  const handleIndicatorClick = (index: number) => {
    haptic.trigger()
    setCurrentIndex(index)
  }

  // Don't show separate loading state - let the main render handle it
  // This allows the counter animation to display

  if (!currentStat) {
    return null
  }

  const displayValue = currentStat.formatter
    ? currentStat.formatter(currentStat.value)
    : currentStat.value.toString()

  return (
    <div className="relative">
      <motion.div
        data-testid="swipeable-stat-card"
        drag="x"
        dragConstraints={{ left: -150, right: 150 }}
        onDragEnd={handleDragEnd}
        animate={controls}
        className="bg-surface-secondary rounded-2xl p-6 shadow-lg cursor-grab active:cursor-grabbing"
      >
        <div className="flex flex-col items-center justify-center h-[200px]">
          <div className="text-brand-primary mb-4">{currentStat.icon}</div>
          <div
            className="text-4xl font-bold text-text-primary mb-2"
            data-testid="stat-value"
          >
            {displayValue}
          </div>
          <div className="text-lg text-text-secondary">{currentStat.label}</div>
        </div>

        {/* Swipe hint */}
        <div className="text-center mt-4">
          <p className="text-sm text-text-tertiary">Swipe to view more</p>
        </div>
      </motion.div>

      {/* Stat indicators */}
      <div className="flex justify-center gap-2 mt-4">
        {statsConfig.map((stat, index) => (
          <button
            key={stat.label}
            data-testid="stat-indicator"
            onClick={() => handleIndicatorClick(index)}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === currentIndex ? 'bg-brand-primary' : 'bg-bg-tertiary'
            }`}
            aria-label={`Go to stat ${index + 1}`}
          />
        ))}
      </div>

      {/* Loading message */}
      {(isLoading || (showInitialAnimation && !animationComplete)) && (
        <div className="text-center mt-4">
          <p className="text-sm text-text-secondary animate-pulse">
            Loading stats and workout...
          </p>
        </div>
      )}
    </div>
  )
}
