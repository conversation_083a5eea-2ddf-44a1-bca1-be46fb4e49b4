import { test, expect } from '@playwright/test'
import { LoginHelper } from './helpers/login-helper'

test.describe('Kebab Menu Visibility', () => {
  test.beforeEach(async ({ page }) => {
    const loginHelper = new LoginHelper(page)
    await loginHelper.login()
  })

  test('should have visible kebab menu dots in navigation', async ({
    page,
  }) => {
    // Navigate to workout page which has the navigation
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Find the kebab menu icon
    const kebabMenuSvg = page
      .locator('svg')
      .filter({ has: page.locator('circle[r="2"]') })
      .first()

    // Verify the SVG is visible
    await expect(kebabMenuSvg).toBeVisible()

    // Verify it has 3 dots with proper radius
    const circles = kebabMenuSvg.locator('circle')
    await expect(circles).toHaveCount(3)

    // Check each circle has the correct radius
    const circleChecks = []
    for (let i = 0; i < 3; i++) {
      const circle = circles.nth(i)
      circleChecks.push(expect(circle).toHaveAttribute('r', '2'))
    }
    await Promise.all(circleChecks)

    // Verify the button containing the icon is clickable
    const kebabButton = page.locator('button').filter({ has: kebabMenuSvg })
    await expect(kebabButton).toBeVisible()
    await expect(kebabButton).toBeEnabled()

    // Take a screenshot to visually verify
    await page.screenshot({
      path: 'kebab-menu-visibility.png',
      clip: { x: 300, y: 0, width: 100, height: 100 },
    })
  })
})
