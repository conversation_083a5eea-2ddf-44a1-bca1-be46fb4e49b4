import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser, login } from './helpers/auth-helper'
import { WorkoutTemplateGroupModel } from '@/types'

test.describe('V2 Exercise Preloading Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport to mobile
    await page.setViewportSize({ width: 390, height: 844 })
  })

  test('should preload exercise data during quick success screen and show no loading when opening exercise', async ({
    page,
  }) => {
    // Mock API responses
    await page.route('**/api/Account/Login*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Token: 'mock-jwt-token',
          RefreshToken: 'mock-refresh-token',
          User: {
            Email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
          },
        }),
      })
    })

    // Mock user info
    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'lbs',
        }),
      })
    })

    // Mock program info
    await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: {
              Id: 1,
              Label: 'Test Program',
              RemainingToLevelUp: 3,
            },
            NextWorkoutTemplate: {
              Id: 101,
              Label: 'Workout A',
              IsSystemExercise: false,
            },
          },
          TotalWorkoutCompleted: 10,
          ConsecutiveWeeks: 3,
        }),
      })
    })

    // Track if preloading requests are made
    let workoutTemplateRequested = false
    let recommendationRequested = false

    // Mock workout template groups
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        workoutTemplateRequested = true
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercices: [
                    {
                      Id: 1,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                    {
                      Id: 2,
                      Label: 'Squat',
                      IsNextExercise: false,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                  ],
                },
              ],
            },
          ] as WorkoutTemplateGroupModel[]),
        })
      }
    )

    // Mock exercise sets
    await page.route('**/api/Exercise/GetUserWorkoutSets*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Sets: [
            {
              Id: 1,
              Reps: 10,
              Weight: { Lb: 135, Kg: 61.2 },
              IsWarmups: false,
              IsComplete: false,
            },
          ],
        }),
      })
    })

    // Mock recommendations with delay to simulate real API
    await page.route(
      '**/api/Recommendation/GetRecommendation*',
      async (route) => {
        recommendationRequested = true
        // Add a small delay to simulate network latency
        await new Promise((resolve) => setTimeout(resolve, 100))

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Reps: 10,
            Weight: { Lb: 140, Kg: 63.5 },
            RIR: 2,
            WeightIncrement: 5,
          }),
        })
      }
    )

    // Navigate to login page
    await page.goto('/login')

    // Perform login
    await login(page)

    // Wait for quick success screen to appear
    await expect(
      page.locator('[data-testid="quick-success-screen"]')
    ).toBeVisible()

    // During quick success screen, workout data should start preloading
    await page.waitForTimeout(200) // Give time for preloading to start

    // Verify preloading requests were made during quick success screen
    expect(workoutTemplateRequested).toBe(true)

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 3000 })

    // Click on "Start Today's Workout" button
    await page.getByRole('button', { name: /start today's workout/i }).click()

    // Should navigate directly to V2 exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Verify we're on the V2 exercise page for exercise 1
    expect(page.url()).toContain('/workout/exercise-v2/1')

    // Verify recommendation was preloaded
    expect(recommendationRequested).toBe(true)

    // Exercise data should be visible immediately without loading state
    // Check that the recommendation is already displayed
    await expect(page.getByText('10 reps')).toBeVisible({ timeout: 1000 })
    await expect(page.getByText(/140.*lbs/)).toBeVisible({ timeout: 1000 })

    // Verify no loading skeleton is shown
    await expect(page.locator('.skeleton')).not.toBeVisible()
    await expect(page.getByText(/loading/i)).not.toBeVisible()
  })

  test('should preload exercise when clicking from exercise list', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Track preloading
    let recommendationRequested = false

    // Mock recommendations
    await page.route(
      '**/api/Recommendation/GetRecommendation*',
      async (route) => {
        recommendationRequested = true
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Reps: 12,
            Weight: { Lb: 150, Kg: 68 },
            RIR: 2,
            WeightIncrement: 5,
          }),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Click on the first exercise in the list
    await page
      .getByRole('button', { name: /bench press/i })
      .first()
      .click()

    // Should start preloading immediately
    await page.waitForTimeout(100) // Small delay to ensure preload starts

    // Should navigate to V2 exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Verify recommendation was preloaded before navigation completed
    expect(recommendationRequested).toBe(true)

    // Exercise data should be visible immediately
    await expect(page.getByText('12 reps')).toBeVisible({ timeout: 1000 })
    await expect(page.getByText(/150.*lbs/)).toBeVisible({ timeout: 1000 })

    // No loading state should be visible
    await expect(page.locator('.skeleton')).not.toBeVisible()
  })

  test('should preload exercise when accessing through notification banner', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Navigate to workout page
    await page.goto('/workout')

    // Look for the "Try our new UI" banner
    const newUIBanner = page.getByText(/try our new ui/i)

    // If banner exists, click it
    if (await newUIBanner.isVisible({ timeout: 2000 })) {
      await newUIBanner.click()

      // Should navigate to V2 exercise page
      await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

      // Verify exercise data is loaded immediately
      await expect(page.getByText(/\d+ reps/)).toBeVisible({ timeout: 1000 })

      // No loading state should be visible
      await expect(page.locator('.skeleton')).not.toBeVisible()
    }
  })

  test('should handle slow network gracefully', async ({ page }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Mock recommendations with significant delay
    await page.route(
      '**/api/Recommendation/GetRecommendation*',
      async (route) => {
        // Simulate slow network
        await new Promise((resolve) => setTimeout(resolve, 2000))

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Reps: 8,
            Weight: { Lb: 185, Kg: 84 },
            RIR: 1,
            WeightIncrement: 5,
          }),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Click start workout
    await page.getByRole('button', { name: /start today's workout/i }).click()

    // Should navigate to V2 exercise page even with slow network
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Should show loading state initially since recommendation is slow
    await expect(page.locator('.skeleton')).toBeVisible()

    // Eventually data should load
    await expect(page.getByText('8 reps')).toBeVisible({ timeout: 3000 })
    await expect(page.getByText(/185.*lbs/)).toBeVisible({ timeout: 3000 })
  })

  test('should handle recommendation loading failure gracefully', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Mock recommendations to fail
    await page.route(
      '**/api/Recommendation/GetRecommendation*',
      async (route) => {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Not found' }),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Click start workout
    await page.getByRole('button', { name: /start today's workout/i }).click()

    // Should still navigate to V2 exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Page should load without crashing
    await expect(page.getByText(/bench press/i)).toBeVisible()

    // Should show some indication that recommendation couldn't be loaded
    // But still allow user to enter data manually
    await expect(page.locator('input[type="number"]')).toBeVisible()
  })

  test('should handle "Try our new UI" click without race condition', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Override the workout template with correct spelling
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 1,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                    {
                      Id: 2,
                      Label: 'Squat',
                      IsNextExercise: false,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for page to load and exercises to be visible
    await page.waitForLoadState('networkidle')
    await expect(page.getByText(/bench press/i)).toBeVisible()

    // Find and click "Try our new UI" button
    const tryNewUIButton = page
      .getByText(/start with new exercise view/i)
      .first()
    await tryNewUIButton.click()

    // Should navigate to V2 exercise page without error
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Should NOT see the error message about exercise not being part of workout
    await expect(
      page.getByText(/this exercise is not part of my workouts/i)
    ).not.toBeVisible()

    // Exercise data should be loaded
    await expect(page.getByText(/bench press/i)).toBeVisible()
  })

  test('should prevent multiple clicks on "Try our new UI" button', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Override the workout template with correct spelling
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 1,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                    {
                      Id: 2,
                      Label: 'Squat',
                      IsNextExercise: false,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for page to load and exercises to be visible
    await page.waitForLoadState('networkidle')
    await expect(page.getByText(/bench press/i)).toBeVisible()

    // Track navigation count
    let navigationCount = 0
    page.on('framenavigated', () => {
      if (page.url().includes('/workout/exercise-v2/')) {
        navigationCount++
      }
    })

    // Find the button
    const tryNewUIButton = page
      .getByText(/start with new exercise view/i)
      .first()

    // Click multiple times rapidly
    await Promise.all([
      tryNewUIButton.click(),
      tryNewUIButton.click(),
      tryNewUIButton.click(),
    ])

    // Wait a bit for any additional navigations
    await page.waitForTimeout(1000)

    // Should only navigate once
    expect(navigationCount).toBe(1)

    // Should be on the V2 exercise page
    await expect(page).toHaveURL(/\/workout\/exercise-v2\/\d+/)
  })

  test('should handle workout start failure gracefully', async ({ page }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)

    // Override the workout template with correct spelling
    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 101,
                  Label: 'Day 1',
                  Exercises: [
                    {
                      Id: 1,
                      Label: 'Bench Press',
                      IsNextExercise: true,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                    {
                      Id: 2,
                      Label: 'Squat',
                      IsNextExercise: false,
                      IsFinished: false,
                      IsUsingBodyWeightOnly: false,
                    },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    // Mock workout start to fail
    await page.route('**/api/Workout/StartWorkout*', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' }),
      })
    })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for page to load and exercises to be visible
    await page.waitForLoadState('networkidle')
    await expect(page.getByText(/bench press/i)).toBeVisible()

    // Click "Try our new UI" button
    const tryNewUIButton = page
      .getByText(/start with new exercise view/i)
      .first()
    await tryNewUIButton.click()

    // Should NOT navigate when workout start fails
    await expect(page).toHaveURL('/workout')

    // Should not show exercise not found error
    await expect(
      page.getByText(/this exercise is not part of my workouts/i)
    ).not.toBeVisible()
  })
})
