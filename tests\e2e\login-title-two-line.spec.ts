import { test, expect } from '@playwright/test'

test.describe('Login Page Title Layout', () => {
  test('should display title and subtitle on separate lines', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Navigate to login page
    await page.goto('/login')

    // Wait for the header to be visible
    await page.waitForSelector('h1', { state: 'visible' })

    // Get the header and subtitle elements
    const header = await page.locator('h1')
    const subtitle = await page
      .locator('p')
      .filter({ hasText: "World's Fastest AI Personal Trainer" })

    // Verify title is separate from subtitle
    const headerText = await header.textContent()
    expect(headerText).toBe('Dr. Muscle X')
    expect(headerText).not.toContain("World's Fastest AI Personal Trainer")

    // Verify subtitle exists as separate element
    await expect(subtitle).toBeVisible()
    const subtitleText = await subtitle.textContent()
    expect(subtitleText).toBe("World's Fastest AI Personal Trainer")

    // Verify they are on different lines by checking positions
    const headerBox = await header.boundingBox()
    const subtitleBox = await subtitle.boundingBox()

    if (headerBox && subtitleBox) {
      // Subtitle should be below header
      expect(subtitleBox.y).toBeGreaterThan(headerBox.y + headerBox.height)
    }
  })

  test('should maintain two-line layout on desktop', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 })

    // Navigate to login page
    await page.goto('/login')

    // Wait for the header to be visible
    await page.waitForSelector('h1', { state: 'visible' })

    // Get the header and subtitle elements
    const header = await page.locator('h1')
    const subtitle = await page
      .locator('p')
      .filter({ hasText: "World's Fastest AI Personal Trainer" })

    // Verify title and subtitle are separate
    const headerText = await header.textContent()
    expect(headerText).toBe('Dr. Muscle X')

    await expect(subtitle).toBeVisible()
    const subtitleText = await subtitle.textContent()
    expect(subtitleText).toBe("World's Fastest AI Personal Trainer")
  })
})
