import { render, screen, waitFor } from '@testing-library/react'
import { ExercisePageClient } from '../ExercisePageClient'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  })),
}))

const mockUseWorkout = useWorkout as vi.MockedFunction<typeof useWorkout>
const mockUseWorkoutStore = useWorkoutStore as vi.MockedFunction<
  typeof useWorkoutStore
>
const mockUseRouter = useRouter as vi.MockedFunction<typeof useRouter>

describe('ExercisePageClient - Direct Navigation', () => {
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
  }

  const mockWorkoutData = {
    todaysWorkout: [
      {
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [{ Id: 123, Label: 'Test Exercise' }],
          },
        ],
      },
    ],
    isLoadingWorkout: false,
    workoutError: null,
    startWorkout: vi.fn(),
    exercises: [{ Id: 123, Label: 'Test Exercise' }],
    workoutSession: { id: 'session-123' },
    loadRecommendation: vi.fn(),
  }

  const mockWorkoutStore = {
    setCurrentExerciseById: vi.fn(),
    getCachedExerciseRecommendation: vi.fn(),
    loadingStates: new Map(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseRouter.mockReturnValue(mockRouter as any)
    mockUseWorkout.mockReturnValue(mockWorkoutData as any)
    mockUseWorkoutStore.mockReturnValue(mockWorkoutStore as any)
  })

  it('should trigger recommendation loading when accessing exercise directly without cached data', async () => {
    // No cached recommendation
    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue(null)

    render(<ExercisePageClient exerciseId={123} />)

    // Should show loading state initially
    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()

    // Verify that the component attempts to load the recommendation
    await waitFor(() => {
      expect(mockWorkoutData.loadRecommendation).toHaveBeenCalledWith(
        123,
        'Test Exercise'
      )
    })
  })

  it('should not show infinite loading when recommendation is not available and not loading', async () => {
    // No cached recommendation and not loading
    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue(null)
    mockWorkoutStore.loadingStates.get = vi.fn().mockReturnValue(false)

    render(<ExercisePageClient exerciseId={123} />)

    // Should trigger loading instead of staying stuck
    await waitFor(() => {
      expect(mockWorkoutData.loadRecommendation).toHaveBeenCalledWith(
        123,
        'Test Exercise'
      )
    })
  })

  it('should handle recommendation loading completion', async () => {
    // Start with no recommendation
    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue(null)
    mockWorkoutStore.loadingStates.get = vi.fn().mockReturnValue(true)

    const { rerender } = render(<ExercisePageClient exerciseId={123} />)

    // Initially should show loading
    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()

    // Simulate recommendation loaded
    mockWorkoutStore.getCachedExerciseRecommendation.mockReturnValue({
      series: 3,
      reps: 10,
      weight: { kg: 50, lb: 110, weightUnit: 'kg' },
    })
    mockWorkoutStore.loadingStates.get = vi.fn().mockReturnValue(false)

    rerender(<ExercisePageClient exerciseId={123} />)

    // Should no longer show loading
    await waitFor(() => {
      expect(
        screen.queryByText('Loading exercise data...')
      ).not.toBeInTheDocument()
    })
  })
})
