import { render, screen, waitFor } from '@testing-library/react'
import { ExercisePageV2Client } from '../ExercisePageV2Client'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useAuthStore } from '@/stores/authStore'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { useExerciseV2Actions } from '@/hooks/useExerciseV2Actions'
import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'

// Mock all the dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  })),
}))
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useExercisePageInitialization')
vi.mock('@/hooks/useExerciseV2Actions')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('ExercisePageV2Client - Completed Sets Display', () => {
  const mockRecommendation: RecommendationModel = {
    WarmupsCount: 2,
    Series: 3,
    Reps: 10,
    Weight: { Kg: 40, Lb: 88 },
    IsNormalSets: true,
    IsPyramid: false,
    IsReversePyramid: false,
    NbPauses: 0,
    Increments: { Kg: 2.5, Lb: 5 },
  }

  const mockExercise = {
    Id: 123,
    Label: 'Bench Press',
    IsFinished: false,
  }

  const mockWorkoutSession = {
    Id: 1,
    exercises: [mockExercise],
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mocks
    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'kg' }),
    } as any)

    vi.mocked(useWorkout).mockReturnValue({
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: mockWorkoutSession,
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      loadingStates: new Map(),
      restTimerState: { isActive: false },
      setRestTimerState: vi.fn(),
      updateActivity: vi.fn(),
    } as any)

    vi.mocked(useExercisePageInitialization).mockReturnValue({
      isInitializing: false,
      loadingError: null,
      retryInitialization: vi.fn(),
    })

    vi.mocked(useExerciseV2Actions).mockReturnValue({
      handleCompleteSet: vi.fn(),
      handleSkipSet: vi.fn(),
    })
  })

  it('should not show completed sets section when no sets are completed', () => {
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: true,
      isFirstWorkSet: false,
      completedSets: [],
      setData: { reps: 8, weight: 20 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
      showRIRPicker: false,
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
    } as any)

    render(
      <NavigationProvider>
        <ExercisePageV2Client exerciseId={123} />
      </NavigationProvider>
    )

    // Should not find completed sets section
    expect(screen.queryByText('Completed Sets')).not.toBeInTheDocument()
  })

  it('should show completed sets section after first warmup set is completed', () => {
    const completedWarmupSet: WorkoutLogSerieModel = {
      Id: 1,
      SetNo: '1',
      IsWarmups: true,
      IsFinished: true,
      WarmUpReps: 8,
      WarmUpWeightSet: { Kg: 20, Lb: 44 },
      Reps: 0,
      Weight: { Kg: 0, Lb: 0 },
    }

    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 1,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: true,
      isFirstWorkSet: false,
      completedSets: [completedWarmupSet],
      setData: { reps: 6, weight: 30 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
      showRIRPicker: false,
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
    } as any)

    render(
      <NavigationProvider>
        <ExercisePageV2Client exerciseId={123} />
      </NavigationProvider>
    )

    // Should show completed sets section
    expect(screen.getByText('Completed Sets')).toBeInTheDocument()
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.getByText('8 reps')).toBeInTheDocument()
    expect(screen.getByText('20 kg')).toBeInTheDocument()
  })

  it('should show completed sets above the current set card', () => {
    const completedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 20, Lb: 44 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
      {
        Id: 2,
        SetNo: '2',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 6,
        WarmUpWeightSet: { Kg: 30, Lb: 66 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
    ]

    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 2,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: false,
      isFirstWorkSet: true,
      completedSets,
      setData: { reps: 10, weight: 40 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
      showRIRPicker: false,
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
    } as any)

    render(
      <NavigationProvider>
        <ExercisePageV2Client exerciseId={123} />
      </NavigationProvider>
    )

    // Get the positions of elements
    const completedSetsElement = screen.getByText('Completed Sets')
    const currentSetElement = screen.getByTestId('current-set-card')

    // Verify completed sets appear before current set in the DOM
    const completedSetsPosition =
      completedSetsElement.compareDocumentPosition(currentSetElement)
    expect(
      // eslint-disable-next-line no-bitwise
      (completedSetsPosition & Node.DOCUMENT_POSITION_FOLLOWING) !== 0
    ).toBe(true)
  })

  it('should update completed sets list when a new set is saved', async () => {
    const initialCompletedSets: WorkoutLogSerieModel[] = [
      {
        Id: 1,
        SetNo: '1',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 20, Lb: 44 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
    ]

    const mockSetScreenLogic = {
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentSetIndex: 1,
      isSaving: false,
      saveError: null,
      showComplete: false,
      showExerciseComplete: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      isLastSet: false,
      isWarmup: true,
      isFirstWorkSet: false,
      completedSets: initialCompletedSets,
      setData: { reps: 6, weight: 30 },
      setSetData: vi.fn(),
      setSaveError: vi.fn(),
      handleSaveSet: vi.fn(),
      refetchRecommendation: vi.fn(),
      showRIRPicker: false,
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
    }

    vi.mocked(useSetScreenLogic).mockReturnValue(mockSetScreenLogic as any)

    const { rerender } = render(
      <NavigationProvider>
        <ExercisePageV2Client exerciseId={123} />
      </NavigationProvider>
    )

    // Initially shows 1 completed set
    expect(screen.getByText('W1')).toBeInTheDocument()
    expect(screen.queryByText('W2')).not.toBeInTheDocument()

    // Update with a new completed set
    const updatedCompletedSets = [
      ...initialCompletedSets,
      {
        Id: 2,
        SetNo: '2',
        IsWarmups: true,
        IsFinished: true,
        WarmUpReps: 6,
        WarmUpWeightSet: { Kg: 30, Lb: 66 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      },
    ]

    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentSetIndex: 2,
      completedSets: updatedCompletedSets,
    } as any)

    rerender(
      <NavigationProvider>
        <ExercisePageV2Client exerciseId={123} />
      </NavigationProvider>
    )

    // Should now show both completed sets
    await waitFor(() => {
      expect(screen.getByText('W1')).toBeInTheDocument()
      expect(screen.getByText('W2')).toBeInTheDocument()
    })
  })
})
