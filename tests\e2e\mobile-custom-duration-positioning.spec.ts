import { test, expect } from '@playwright/test'
import { ExercisePage } from './pages/ExercisePage'

test.describe('Mobile Custom Duration Modal Positioning', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE

    // Go to exercise page
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()
  })

  test('should position custom duration modal correctly on mobile @critical', async ({
    page,
  }) => {
    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Open duration picker
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
    await page.click('[data-testid="duration-setting-button"]')

    // Click custom duration
    await expect(page.getByTestId('duration-picker')).toBeVisible()
    await page.click('[data-testid="duration-option-custom"]')

    // Verify modal positioning
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Check modal is within viewport bounds
    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBox && viewport) {
      // Modal should be fully visible within viewport
      expect(modalBox.x).toBeGreaterThanOrEqual(0)
      expect(modalBox.y).toBeGreaterThanOrEqual(0)
      expect(modalBox.x + modalBox.width).toBeLessThanOrEqual(viewport.width)
      expect(modalBox.y + modalBox.height).toBeLessThanOrEqual(viewport.height)

      // Modal should have proper margins (16px on each side)
      expect(modalBox.x).toBeGreaterThanOrEqual(16)
      expect(
        viewport.width - (modalBox.x + modalBox.width)
      ).toBeGreaterThanOrEqual(16)
    }

    // Modal should be centered horizontally
    if (modalBox && viewport) {
      const centerX = modalBox.x + modalBox.width / 2
      const viewportCenterX = viewport.width / 2
      expect(Math.abs(centerX - viewportCenterX)).toBeLessThan(20) // Allow 20px tolerance
    }

    // Verify modal functionality works
    await page.fill('#duration-input', '180')
    await page.click('button:has-text("Confirm")')

    // Modal should close and timer should update
    await expect(modal).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('3:00')
  })

  test('should handle very narrow mobile viewport (320px) @critical', async ({
    page,
  }) => {
    // Set narrow mobile viewport
    await page.setViewportSize({ width: 320, height: 568 }) // iPhone 5/SE

    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Open custom duration modal
    await page.click('[data-testid="duration-setting-button"]')
    await page.click('[data-testid="duration-option-custom"]')

    // Verify modal is still fully visible
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    const modalBox = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBox && viewport) {
      // Modal should not overflow narrow viewport
      expect(modalBox.x).toBeGreaterThanOrEqual(0)
      expect(modalBox.x + modalBox.width).toBeLessThanOrEqual(viewport.width)

      // Should have proper minimum margins
      expect(modalBox.x).toBeGreaterThanOrEqual(16)
      expect(
        viewport.width - (modalBox.x + modalBox.width)
      ).toBeGreaterThanOrEqual(16)
    }
  })

  test('should appear above duration picker with proper z-index @critical', async ({
    page,
  }) => {
    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Open duration picker
    await page.click('[data-testid="duration-setting-button"]')
    const durationPicker = page.getByTestId('duration-picker')
    await expect(durationPicker).toBeVisible()

    // Click custom duration to open modal
    await page.click('[data-testid="duration-option-custom"]')

    // Both should be visible but modal should be on top
    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()
    await expect(durationPicker).toBeVisible()

    // Test z-index by trying to interact with modal input (should work)
    const input = page.locator('#duration-input')
    await expect(input).toBeVisible()
    await input.fill('240')
    expect(await input.inputValue()).toBe('240')

    // Modal should be interactive (not blocked by duration picker)
    await page.click('button:has-text("Confirm")')
    await expect(modal).not.toBeVisible()
  })

  test('should remain visible when keyboard opens on mobile @critical', async ({
    page,
  }) => {
    // Set mobile viewport with typical keyboard height
    await page.setViewportSize({ width: 375, height: 667 })

    // Start rest timer
    await page.fill('input[placeholder*="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '100')
    await page.click('button:has-text("Save set")')

    // Open custom duration modal
    await page.click('[data-testid="duration-setting-button"]')
    await page.click('[data-testid="duration-option-custom"]')

    const modal = page.getByTestId('custom-duration-modal')
    await expect(modal).toBeVisible()

    // Focus input to trigger keyboard
    const input = page.locator('#duration-input')
    await input.focus()

    // Get modal position before simulating keyboard
    await modal.boundingBox()

    // Simulate keyboard by reducing viewport height (typical keyboard takes ~260px)
    await page.setViewportSize({ width: 375, height: 407 })

    // Wait for any animations
    await page.waitForTimeout(300)

    // Modal should still be visible
    await expect(modal).toBeVisible()

    // Get modal position after keyboard simulation
    const modalBoxAfter = await modal.boundingBox()
    const viewport = page.viewportSize()

    if (modalBoxAfter && viewport) {
      // Modal should be fully visible within reduced viewport
      expect(modalBoxAfter.y).toBeGreaterThanOrEqual(0)
      expect(modalBoxAfter.y + modalBoxAfter.height).toBeLessThanOrEqual(
        viewport.height
      )

      // Modal header should be visible (not cut off at top)
      const modalHeader = modal.locator('h3:has-text("Custom Duration")')
      await expect(modalHeader).toBeVisible()

      // Input field should be visible and accessible
      await expect(input).toBeVisible()
      await input.fill('120')
      expect(await input.inputValue()).toBe('120')

      // Buttons should be visible
      await expect(modal.locator('button:has-text("Cancel")')).toBeVisible()
      await expect(modal.locator('button:has-text("Confirm")')).toBeVisible()
    }

    // Should be able to complete the action
    await modal.locator('button:has-text("Confirm")').click()
    await expect(modal).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('2:00')
  })
})
