import { describe, it, expect, beforeEach, vi } from 'vitest'
import { RecommendationLoadingCoordinator } from '../RecommendationLoadingCoordinator'

describe('RecommendationLoadingCoordinator', () => {
  let coordinator: RecommendationLoadingCoordinator

  beforeEach(() => {
    // Reset singleton instance before each test
    coordinator = RecommendationLoadingCoordinator.getInstance()
    coordinator.reset()
  })

  describe('singleton pattern', () => {
    it('should return the same instance', () => {
      const instance1 = RecommendationLoadingCoordinator.getInstance()
      const instance2 = RecommendationLoadingCoordinator.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('loading state management', () => {
    it('should mark exercise as loading when startLoading is called', () => {
      // GIVEN no exercises are loading
      expect(coordinator.isLoading(123)).toBe(false)

      // WHEN startLoading called
      coordinator.startLoading(123)

      // THEN marks exercise as loading
      expect(coordinator.isLoading(123)).toBe(true)
    })

    it('should return true when exercise is loading', () => {
      // GIVEN exercise is loading
      coordinator.startLoading(456)

      // WHEN isLoading checked
      const result = coordinator.isLoading(456)

      // THEN returns true
      expect(result).toBe(true)
    })

    it('should prevent duplicate loading when exercise already loading', () => {
      // GIVEN exercise is loading
      coordinator.startLoading(789)
      expect(coordinator.isLoading(789)).toBe(true)

      // WHEN another request comes
      const canLoad = coordinator.canStartLoading(789)

      // THEN prevents duplicate
      expect(canLoad).toBe(false)
    })

    it('should allow loading after previous load completes', () => {
      // GIVEN exercise finished loading
      coordinator.startLoading(111)
      coordinator.completeLoading(111)

      // WHEN another request comes
      const canLoad = coordinator.canStartLoading(111)

      // THEN allows new load
      expect(canLoad).toBe(true)
    })

    it('should track multiple exercises loading states independently', () => {
      // GIVEN multiple exercises
      const exerciseIds = [1, 2, 3, 4, 5]

      // WHEN batch load requested
      exerciseIds.forEach((id) => coordinator.startLoading(id))

      // THEN tracks all states
      exerciseIds.forEach((id) => {
        expect(coordinator.isLoading(id)).toBe(true)
      })

      // AND when some complete
      coordinator.completeLoading(2)
      coordinator.completeLoading(4)

      // THEN tracks correctly
      expect(coordinator.isLoading(1)).toBe(true)
      expect(coordinator.isLoading(2)).toBe(false)
      expect(coordinator.isLoading(3)).toBe(true)
      expect(coordinator.isLoading(4)).toBe(false)
      expect(coordinator.isLoading(5)).toBe(true)
    })
  })

  describe('edge cases', () => {
    it('should handle concurrent requests for same exercise', () => {
      // GIVEN concurrent requests for same exercise
      const exerciseId = 999
      let firstCallAllowed = false
      let secondCallAllowed = false

      // WHEN both try to start loading
      if (coordinator.canStartLoading(exerciseId)) {
        firstCallAllowed = true
        coordinator.startLoading(exerciseId)
      }

      if (coordinator.canStartLoading(exerciseId)) {
        secondCallAllowed = true
        coordinator.startLoading(exerciseId)
      }

      // THEN only first is allowed
      expect(firstCallAllowed).toBe(true)
      expect(secondCallAllowed).toBe(false)
    })

    it('should handle loading failures', () => {
      // GIVEN exercise loading fails
      coordinator.startLoading(222)

      // WHEN failure occurs
      coordinator.failLoading(222)

      // THEN clears loading state
      expect(coordinator.isLoading(222)).toBe(false)

      // AND allows retry
      expect(coordinator.canStartLoading(222)).toBe(true)
    })

    it('should provide batch operations', () => {
      // GIVEN list of exercises
      const exerciseIds = [10, 20, 30]

      // WHEN checking if any are loading
      expect(coordinator.isAnyLoading(exerciseIds)).toBe(false)

      // AND starting batch load
      coordinator.startBatchLoading(exerciseIds)

      // THEN all marked as loading
      expect(coordinator.isAnyLoading(exerciseIds)).toBe(true)
      expect(coordinator.areAllLoading(exerciseIds)).toBe(true)

      // AND when one completes
      coordinator.completeLoading(20)

      // THEN batch checks work correctly
      expect(coordinator.isAnyLoading(exerciseIds)).toBe(true)
      expect(coordinator.areAllLoading(exerciseIds)).toBe(false)
    })

    it('should clean up memory after completion', () => {
      // GIVEN many exercises loaded
      const manyIds = Array.from({ length: 100 }, (_, i) => i)
      manyIds.forEach((id) => {
        coordinator.startLoading(id)
        coordinator.completeLoading(id)
      })

      // WHEN reset called
      coordinator.reset()

      // THEN all states cleared
      manyIds.forEach((id) => {
        expect(coordinator.isLoading(id)).toBe(false)
      })
    })

    it('should track loading statistics', () => {
      // GIVEN some successful and failed loads
      coordinator.startLoading(1)
      coordinator.completeLoading(1)

      coordinator.startLoading(2)
      coordinator.failLoading(2)

      coordinator.startLoading(3)
      coordinator.completeLoading(3)

      // WHEN getting stats
      const stats = coordinator.getStats()

      // THEN provides accurate counts
      expect(stats.totalRequests).toBe(3)
      expect(stats.successfulLoads).toBe(2)
      expect(stats.failedLoads).toBe(1)
      expect(stats.currentlyLoading).toBe(0)
    })
  })

  describe('timeout handling', () => {
    it('should auto-fail loads that timeout', async () => {
      vi.useFakeTimers()

      // GIVEN exercise starts loading with timeout
      coordinator.startLoading(333, { timeout: 5000 })

      // WHEN timeout expires
      vi.advanceTimersByTime(5001)

      // THEN marks as failed
      expect(coordinator.isLoading(333)).toBe(false)
      expect(coordinator.canStartLoading(333)).toBe(true)

      vi.useRealTimers()
    })
  })
})
