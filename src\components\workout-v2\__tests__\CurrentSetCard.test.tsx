import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: 'div',
  },
}))

// Mock hooks
vi.mock('@/hooks/useSwipeAnimation', () => ({
  useSwipeAnimation: () => ({
    animationProps: {},
    triggerAnimation: vi.fn(),
  }),
}))

describe('CurrentSetCard', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Test Exercise',
    BodyPart: 'Chest',
    Equipment: 'Barbell',
  } as ExerciseModel

  const mockCurrentSet: WorkoutLogSerieModel = {
    Id: 1,
    Reps: 10,
    Weight: { Kg: 100, Lb: 220 },
    IsWarmups: false,
  } as WorkoutLogSerieModel

  const defaultProps = {
    exercise: mockExercise,
    currentSet: mockCurrentSet,
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    onComplete: vi.fn(),
    onSkip: vi.fn(),
    isSaving: false,
    unit: 'kg' as const,
  }

  it('should NOT display warm-up set labels like "Warm-up 1", "Warm-up 2"', () => {
    // Test rationale: Set labels removed to avoid redundancy with header progress display
    const warmupSet: WorkoutLogSerieModel = {
      Id: 1,
      Reps: 5,
      Weight: { Kg: 50, Lb: 110 },
      IsWarmups: true,
      WarmUpReps: 5,
      WarmUpWeightSet: { Kg: 50, Lb: 110 },
    } as WorkoutLogSerieModel

    // First warmup set
    const { rerender } = render(
      <CurrentSetCard
        {...defaultProps}
        currentSet={warmupSet}
        currentSetIndex={0}
        isWarmup
      />
    )

    expect(screen.queryByText('Warm-up 1')).not.toBeInTheDocument()
    expect(screen.queryByText('W1')).not.toBeInTheDocument()

    // Second warmup set
    rerender(
      <CurrentSetCard
        {...defaultProps}
        currentSet={warmupSet}
        currentSetIndex={1}
        isWarmup
      />
    )

    expect(screen.queryByText('Warm-up 2')).not.toBeInTheDocument()
    expect(screen.queryByText('W2')).not.toBeInTheDocument()
  })

  it('should not display "Set X of Y" information', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Should not find "Set 3 of 5" text
    expect(screen.queryByText('Set 3 of 5')).not.toBeInTheDocument()
    expect(screen.queryByText(/Set \d+ of \d+/)).not.toBeInTheDocument()
  })

  it('should NOT display set labels like "Set 1", "Set 2"', () => {
    // Test rationale: Set labels removed to avoid redundancy with header progress display
    render(<CurrentSetCard {...defaultProps} />)

    // Should not display "Set X of Y" format
    expect(screen.queryByText(/Set \d+ of \d+/)).not.toBeInTheDocument()

    // Should not display individual set labels like "Set 3"
    expect(screen.queryByText('Set 3')).not.toBeInTheDocument()
    expect(screen.queryByText(/^Set \d+$/)).not.toBeInTheDocument()
  })

  it('should not display chevron up/down buttons', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Should not find chevron buttons
    expect(screen.queryByTestId('chevron-up')).not.toBeInTheDocument()
    expect(screen.queryByTestId('chevron-down')).not.toBeInTheDocument()
  })

  it('should not display asterisk separator', () => {
    render(<CurrentSetCard {...defaultProps} />)

    // Should not find asterisk
    expect(screen.queryByText('*')).not.toBeInTheDocument()
  })

  describe('Save set button styling', () => {
    it('should have correct gradient and styling classes', () => {
      render(<CurrentSetCard {...defaultProps} />)

      const saveButton = screen.getByRole('button', { name: /save set/i })

      // Test for correct gradient class
      expect(saveButton).toHaveClass('bg-gradient-metallic-gold')

      // Test for shadow classes
      expect(saveButton).toHaveClass('shadow-theme-xl')
      expect(saveButton).toHaveClass('hover:shadow-theme-2xl')

      // Test for shimmer effect
      expect(saveButton).toHaveClass('shimmer-hover')

      // Test for text shadow
      expect(saveButton).toHaveClass('text-shadow-sm')

      // Test for correct padding
      expect(saveButton).toHaveClass('py-4')

      // Test for other required classes
      expect(saveButton).toHaveClass('min-h-[56px]')
      expect(saveButton).toHaveClass('text-text-inverse')
    })

    it('should display "Save set" with lowercase "s"', () => {
      render(<CurrentSetCard {...defaultProps} />)

      const saveButton = screen.getByRole('button', { name: 'Save set' })
      expect(saveButton).toHaveTextContent('Save set')
    })

    it('should maintain opacity styling when disabled', () => {
      render(<CurrentSetCard {...defaultProps} isSaving />)

      const saveButton = screen.getByRole('button', { name: 'Saving...' })
      expect(saveButton).toBeDisabled()
      expect(saveButton).toHaveClass('opacity-60')
      expect(saveButton).toHaveClass('cursor-not-allowed')
      expect(saveButton).toHaveClass('bg-bg-tertiary')
      expect(saveButton).toHaveClass('text-text-tertiary')
    })
  })

  describe('Warmup set labeling', () => {
    it('should NOT display "Warm-up" label for warmup sets', () => {
      // Test rationale: Set labels removed to avoid redundancy with header progress display
      const warmupProps = {
        ...defaultProps,
        isWarmup: true,
      }

      render(<CurrentSetCard {...warmupProps} />)

      expect(screen.queryByText('Warm-up')).not.toBeInTheDocument()
      expect(screen.queryByText('Warmup')).not.toBeInTheDocument()
    })
  })
})
