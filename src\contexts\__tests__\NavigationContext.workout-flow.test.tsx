import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { NavigationProvider, useNavigation } from '../NavigationContext'
import React from 'react'

// Mock Next.js router
const mockPush = vi.fn()
const mockBack = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
    replace: vi.fn(),
    refresh: vi.fn(),
    forward: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    has: vi.fn(),
    getAll: vi.fn(),
  }),
  usePathname: () => '/test-path',
  redirect: vi.fn(),
  notFound: vi.fn(),
}))

describe('NavigationContext - Workout Flow', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <NavigationProvider>{children}</NavigationProvider>
  )

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Workout Navigation Flow', () => {
    it('should track navigation from workout overview to exercise', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Navigate to exercise with history tracking
      act(() => {
        result.current.navigateWithHistory('/workout/exercise/123')
      })

      // Since we're in test environment, window.location isn't set properly
      // The navigation should still work
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should handle back navigation from rest timer to exercise', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Simulate navigation: workout -> exercise -> rest timer
      act(() => {
        result.current.pushRoute('/workout')
        result.current.pushRoute('/workout/exercise/123')
        result.current.pushRoute('/workout/rest-timer')
      })

      expect(result.current.navigationStack).toHaveLength(3)

      // Go back from rest timer
      act(() => {
        result.current.goBack()
      })

      // Should return to exercise page
      expect(mockPush).toHaveBeenCalledWith('/workout/rest-timer')
      expect(result.current.navigationStack).toEqual([
        '/workout',
        '/workout/exercise/123',
      ])
    })

    it('should clear navigation stack on workout complete', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Build up navigation history
      act(() => {
        result.current.pushRoute('/workout')
        result.current.pushRoute('/workout/exercise/123')
        result.current.pushRoute('/workout/rest-timer')
        result.current.pushRoute('/workout/exercise/123')
      })

      expect(result.current.navigationStack).toHaveLength(4)

      // Complete workout - should clear stack
      act(() => {
        result.current.clearNavigationStack()
      })

      expect(result.current.navigationStack).toEqual([])
      expect(result.current.canGoBack).toBe(false)
    })

    it('should handle direct URL access without navigation history', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Direct access to exercise page
      act(() => {
        result.current.setCurrentRoute()
      })

      expect(result.current.navigationStack).toEqual([])
      expect(result.current.canGoBack).toBe(false)

      // Attempting to go back should not navigate
      act(() => {
        result.current.goBack()
      })

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should prevent duplicate consecutive routes in stack', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Push same route multiple times
      act(() => {
        result.current.pushRoute('/workout/exercise/123')
        result.current.pushRoute('/workout/exercise/123')
        result.current.pushRoute('/workout/exercise/123')
      })

      // Should only have one entry
      expect(result.current.navigationStack).toEqual(['/workout/exercise/123'])
    })

    it('should handle exercise navigation with invalid IDs', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Try to navigate to invalid exercise
      act(() => {
        result.current.navigateToExercise(0) // Invalid ID
      })

      // Should not navigate
      expect(mockPush).not.toHaveBeenCalled()

      // Try with valid ID
      act(() => {
        result.current.navigateToExercise(123)
      })

      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/123')
    })

    it('should track workout progress through navigation', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Start workout
      act(() => {
        result.current.startWorkoutNavigation([123, 456, 789])
      })

      expect(result.current.workoutProgress).toEqual({
        totalExercises: 3,
        currentExerciseIndex: 0,
        visitedExercises: [],
      })

      // Visit first exercise
      act(() => {
        result.current.markExerciseVisited(123)
      })

      expect(result.current.workoutProgress.visitedExercises).toEqual([123])
      expect(result.current.workoutProgress.currentExerciseIndex).toBe(0)

      // Move to next exercise
      act(() => {
        result.current.navigateToExercise(456)
        result.current.markExerciseVisited(456)
      })

      expect(result.current.workoutProgress.visitedExercises).toEqual([
        123, 456,
      ])
      expect(result.current.workoutProgress.currentExerciseIndex).toBe(1)
    })

    it('should handle rapid navigation calls gracefully', async () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Simulate rapid navigation
      act(() => {
        result.current.navigateWithHistory('/workout/exercise/1')
        result.current.navigateWithHistory('/workout/exercise/2')
        result.current.navigateWithHistory('/workout/exercise/3')
      })

      // All navigations should be called
      expect(mockPush).toHaveBeenCalledTimes(3)

      // But navigation stack should be deduplicated
      expect(result.current.navigationStack.length).toBeLessThanOrEqual(3)
    })

    it('should provide workout context navigation helpers', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Test navigation helpers exist
      expect(result.current.navigateToWorkout).toBeDefined()
      expect(result.current.navigateToExercise).toBeDefined()
      expect(result.current.navigateToRestTimer).toBeDefined()
      expect(result.current.navigateToComplete).toBeDefined()

      // Test they work correctly
      act(() => {
        result.current.navigateToWorkout()
      })
      expect(mockPush).toHaveBeenCalledWith('/workout')

      act(() => {
        result.current.navigateToRestTimer({ betweenSets: true })
      })
      expect(mockPush).toHaveBeenCalledWith(
        '/workout/rest-timer?between-sets=true'
      )
    })
  })

  describe('Browser Back Button Handling', () => {
    it('should sync with browser history', () => {
      const { result } = renderHook(() => useNavigation(), { wrapper })

      // Simulate browser back button behavior
      act(() => {
        result.current.pushRoute('/workout')
        result.current.pushRoute('/workout/exercise/123')
      })

      // Simulate browser back
      act(() => {
        result.current.handleBrowserBack()
      })

      expect(result.current.navigationStack).toEqual(['/workout'])
    })
  })
})
