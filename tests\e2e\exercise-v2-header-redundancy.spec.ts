import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Exercise V2 - Header Redundancy Fix', () => {
  test.use({
    viewport: { width: 375, height: 667 }, // Mobile viewport
  })

  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should show set progress in header but NOT in CurrentSetCard', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Verify header shows "Set 1 of 6" progress
    await expect(page.getByText('Set 1 of 6')).toBeVisible()

    // Verify CurrentSetCard does NOT show redundant set labels
    const currentSetCard = page.getByTestId('current-set-card')
    await expect(currentSetCard).toBeVisible()

    // Should NOT find "Warm-up 1" or "Set 1" text within CurrentSetCard
    await expect(currentSetCard.getByText(/^Warm-up \d+$/)).not.toBeVisible()
    await expect(currentSetCard.getByText(/^Set \d+$/)).not.toBeVisible()

    // Should still see the input controls
    await expect(currentSetCard.getByRole('spinbutton')).toHaveCount(2) // Reps and Weight inputs
    await expect(
      currentSetCard.getByRole('button', { name: 'Save set' })
    ).toBeVisible()
  })

  test('should maintain set progress visibility after completing sets', async ({
    page,
  }) => {
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete first warmup set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Header should update to "Set 2 of 6"
    await expect(page.getByText('Set 2 of 6')).toBeVisible()

    // CurrentSetCard still should NOT show set labels
    const currentSetCard = page.getByTestId('current-set-card')
    await expect(currentSetCard.getByText(/^Warm-up \d+$/)).not.toBeVisible()
    await expect(currentSetCard.getByText(/^Set \d+$/)).not.toBeVisible()
  })

  test('should properly show set type badges if present', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Skip to a work set (complete warmups)
    const completeSet = async () => {
      await page.getByRole('button', { name: 'Save set' }).click()
      await page.waitForSelector('[data-testid="rest-timer"]')
      await page.getByRole('button', { name: 'Skip' }).click()
    }

    await completeSet() // First warmup
    await completeSet() // Second warmup

    // If set type badges exist, they should still be visible
    // But NOT the redundant "Set 1" label
    const currentSetCard = page.getByTestId('current-set-card')
    await expect(currentSetCard.getByText(/^Set \d+$/)).not.toBeVisible()
  })
})
