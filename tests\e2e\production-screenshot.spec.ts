import { test, expect } from '@playwright/test'

test.describe('Production Site Verification', () => {
  test('should navigate to production site and take screenshot', async ({
    page,
  }) => {
    // Navigate to production URL
    await page.goto('https://x.dr-muscle.com/', {
      waitUntil: 'domcontentloaded',
      timeout: 30000,
    })

    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle')

    // Take a full page screenshot
    await page.screenshot({
      path: 'production-site-screenshot.png',
      fullPage: true,
    })

    // Verify the page title to ensure we're on the right site
    const title = await page.title()
    expect(title).toBeTruthy()

    // Log success
    console.log(
      'Successfully navigated to https://x.dr-muscle.com/ and captured screenshot'
    )
  })

  test('should take mobile viewport screenshot', async ({ page }) => {
    // Set mobile viewport (iPhone 13 dimensions)
    await page.setViewportSize({ width: 390, height: 844 })

    // Navigate to production URL
    await page.goto('https://x.dr-muscle.com/', {
      waitUntil: 'domcontentloaded',
      timeout: 30000,
    })

    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle')

    // Take a mobile viewport screenshot
    await page.screenshot({
      path: 'production-site-mobile-screenshot.png',
      fullPage: false, // Just viewport for mobile
    })

    // Log success
    console.log('Successfully captured mobile viewport screenshot')
  })
})
