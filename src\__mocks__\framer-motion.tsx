import React from 'react'
import { vi } from 'vitest'

// List of framer-motion specific props to filter out
const motionProps = [
  'initial',
  'animate',
  'exit',
  'transition',
  'variants',
  'whileHover',
  'whileTap',
  'whileFocus',
  'whileDrag',
  'whileInView',
  'drag',
  'dragConstraints',
  'dragElastic',
  'dragMomentum',
  'dragTransition',
  'dragPropagation',
  'dragDirectionLock',
  'onDragStart',
  'onDragEnd',
  'onDrag',
  'layout',
  'layoutId',
  'onViewportEnter',
  'onViewportLeave',
  'viewport',
  'custom',
]

// Helper to filter out motion props
const filterMotionProps = (props: Record<string, unknown>) => {
  const filteredProps: Record<string, unknown> = {}
  Object.keys(props).forEach((key) => {
    if (!motionProps.includes(key)) {
      filteredProps[key] = props[key]
    }
  })
  return filteredProps
}

// Create mock motion components
const createMotionComponent = (Component: string) => {
  // eslint-disable-next-line react/display-name
  return React.forwardRef(
    (props: Record<string, unknown>, ref: React.Ref<unknown>) => {
      const filteredProps = filterMotionProps(props)
      return React.createElement(Component, { ...filteredProps, ref })
    }
  )
}

export const motion = {
  div: createMotionComponent('div'),
  span: createMotionComponent('span'),
  button: createMotionComponent('button'),
  a: createMotionComponent('a'),
  img: createMotionComponent('img'),
  section: createMotionComponent('section'),
  article: createMotionComponent('article'),
  header: createMotionComponent('header'),
  footer: createMotionComponent('footer'),
  main: createMotionComponent('main'),
  nav: createMotionComponent('nav'),
  aside: createMotionComponent('aside'),
  form: createMotionComponent('form'),
  input: createMotionComponent('input'),
  textarea: createMotionComponent('textarea'),
  select: createMotionComponent('select'),
  h1: createMotionComponent('h1'),
  h2: createMotionComponent('h2'),
  h3: createMotionComponent('h3'),
  h4: createMotionComponent('h4'),
  h5: createMotionComponent('h5'),
  h6: createMotionComponent('h6'),
  p: createMotionComponent('p'),
  ul: createMotionComponent('ul'),
  ol: createMotionComponent('ol'),
  li: createMotionComponent('li'),
}

export function AnimatePresence({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>
}

export const useAnimation = () => ({
  start: vi.fn(),
  stop: vi.fn(),
  set: vi.fn(),
})

export const useMotionValue = (initial: unknown) => ({
  get: () => initial,
  set: () => {},
  subscribe: () => () => {},
})

export const useTransform = () => ({
  get: () => 0,
  set: () => {},
  subscribe: () => () => {},
})

export const useSpring = useMotionValue
export const useVelocity = useMotionValue
