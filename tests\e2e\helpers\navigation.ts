import { Page } from '@playwright/test'

/**
 * Navigate to today's workout page
 */
export async function goToTodaysWorkout(page: Page) {
  await page.goto('/workout')
  await page.waitForLoadState('networkidle')
}

/**
 * Navigate to program page
 */
export async function goToProgram(page: Page) {
  await page.goto('/program')
  await page.waitForLoadState('networkidle')
}

/**
 * Navigate to login page
 */
export async function goToLogin(page: Page) {
  await page.goto('/login')
  await page.waitForLoadState('networkidle')
}

/**
 * Wait for navigation to complete
 */
export async function waitForNavigation(
  page: Page,
  path: string,
  timeout = 10000
) {
  await page.waitForURL(path, { timeout })
}
