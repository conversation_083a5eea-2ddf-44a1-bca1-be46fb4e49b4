import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useExerciseV2Actions } from '@/hooks/useExerciseV2Actions'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { useRestTimer } from '@/components/workout-v2/RestTimer'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('@/components/workout-v2/RestTimer')
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}))

describe('useExerciseV2Actions - Duration from localStorage', () => {
  const mockSaveSet = vi.fn()
  const mockNextSet = vi.fn()
  const mockStartRestTimer = vi.fn()
  const mockSetSaveError = vi.fn()

  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 0,
    IsFinished: false,
  }

  const mockWorkoutSession = {
    id: 'session-123',
    exercises: [],
  }

  const mockSetData = {
    reps: 10,
    weight: 50,
    duration: 0,
  }

  const mockWorkSet: WorkoutLogSerieModel = {
    Id: 1,
    ExerciseId: 1,
    Reps: 10,
    Weight: { Kg: 50, Lb: 110 },
    IsWarmups: false,
    State: 0,
    IsNext: true,
  }

  const mockWarmupSet: WorkoutLogSerieModel = {
    Id: 1,
    ExerciseId: 1,
    IsWarmups: true,
    State: 0,
    Weight: { Kg: 0, Lb: 0 },
    Reps: 0,
    WarmUpReps: 8,
    WarmUpWeightSet: { Kg: 25, Lb: 55 },
    IsNext: true,
  }

  const defaultProps = {
    currentExercise: mockExercise,
    workoutSession: mockWorkoutSession,
    setData: mockSetData,
    currentSetIndex: 0,
    allSets: [mockWorkSet],
    isWarmup: false,
    isLastSet: false,
    isFirstWorkSet: true,
    currentSet: mockWorkSet,
    setSaveError: mockSetSaveError,
    isLastExercise: false,
    exercises: [mockExercise],
  }

  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()

    vi.mocked(useWorkout).mockReturnValue({
      saveSet: mockSaveSet,
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      nextSet: mockNextSet,
    } as any)

    vi.mocked(useAuthStore).mockReturnValue({
      getCachedUserInfo: () => ({ MassUnit: 'kg' }),
    } as any)

    vi.mocked(useRestTimer).mockReturnValue({
      startRestTimer: mockStartRestTimer,
    })

    mockSaveSet.mockResolvedValue({ success: true })
  })

  it('should use default durations when localStorage is empty', async () => {
    // Add another set so it's not the last set
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [mockWorkSet, { ...mockWorkSet, Id: 2, IsNext: false }],
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    // Default: 90s for work sets
    expect(mockStartRestTimer).toHaveBeenCalledWith(90, expect.any(Object))
  })

  it('should use custom duration from localStorage for work sets', async () => {
    localStorage.setItem('restDuration', '120')

    // Add another set so it's not the last set
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [mockWorkSet, { ...mockWorkSet, Id: 2, IsNext: false }],
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    expect(mockStartRestTimer).toHaveBeenCalledWith(120, expect.any(Object))
  })

  it('should use shorter duration for warmup sets', async () => {
    localStorage.setItem('restDuration', '120')

    const warmupProps = {
      ...defaultProps,
      allSets: [mockWarmupSet, { ...mockWorkSet, Id: 2, IsNext: false }],
      isWarmup: true,
      currentSet: mockWarmupSet,
      setData: { reps: 8, weight: 25, duration: 0 },
    }

    const { result } = renderHook(() => useExerciseV2Actions(warmupProps))

    await act(async () => {
      await result.current.handleCompleteSet({
        reps: 8,
        weight: '25',
        rir: null,
      })
    })

    // Should use 1/3 of the duration for warmups
    expect(mockStartRestTimer).toHaveBeenCalledWith(40, expect.any(Object))
  })

  it('should handle invalid localStorage values', async () => {
    localStorage.setItem('restDuration', 'invalid')

    // Add another set so it's not the last set
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [mockWorkSet, { ...mockWorkSet, Id: 2, IsNext: false }],
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    // Should fallback to default
    expect(mockStartRestTimer).toHaveBeenCalledWith(90, expect.any(Object))
  })

  it('should clamp duration to valid range', async () => {
    // Test minimum
    localStorage.setItem('restDuration', '2')

    // Add another set so it's not the last set
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [mockWorkSet, { ...mockWorkSet, Id: 2, IsNext: false }],
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    expect(mockStartRestTimer).toHaveBeenCalledWith(
      5, // Minimum clamped to 5 seconds
      expect.any(Object)
    )

    // Test maximum
    localStorage.setItem('restDuration', '700')

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    expect(mockStartRestTimer).toHaveBeenCalledWith(
      600, // Maximum clamped to 600 seconds
      expect.any(Object)
    )
  })

  it('should use shorter duration for skipped sets', async () => {
    localStorage.setItem('restDuration', '120')

    // Add another set so it's not the last set
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [mockWorkSet, { ...mockWorkSet, Id: 2, IsNext: false }],
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleSkipSet()
    })

    // Skipped sets use 1/4 of the duration
    expect(mockStartRestTimer).toHaveBeenCalledWith(30, expect.any(Object))
  })

  it('should pass next set info to rest timer', async () => {
    localStorage.setItem('restDuration', '90')

    const nextSet: WorkoutLogSerieModel = {
      Id: 2,
      ExerciseId: 1,
      Reps: 12,
      Weight: { Kg: 55, Lb: 121 },
      IsWarmups: false,
      State: 0,
    }

    const currentSet = { ...mockWorkSet, IsNext: true }
    const propsWithNextSet = {
      ...defaultProps,
      allSets: [currentSet, nextSet],
      currentSet,
    }

    const { result } = renderHook(() => useExerciseV2Actions(propsWithNextSet))

    await act(async () => {
      await result.current.handleCompleteSet({ reps: 10, weight: '50', rir: 2 })
    })

    expect(mockStartRestTimer).toHaveBeenCalledWith(90, {
      reps: 12,
      weight: 55,
      unit: 'kg',
    })
  })
})
