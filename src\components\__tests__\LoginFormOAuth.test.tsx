import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { LoginFormOAuth } from '../LoginFormOAuth'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/useOAuth', () => ({
  useOAuth: vi.fn(() => ({
    hasAnyProvider: true,
    google: { isConfigured: true },
    apple: { isConfigured: true },
    signInWithGoogle: vi.fn(),
    signInWithApple: vi.fn(),
    isLoading: null,
    error: null,
  })),
}))

vi.mock('@/hooks/useHapticFeedback', () => ({
  useHapticFeedback: () => ({
    light: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  }),
}))

describe('LoginFormOAuth', () => {
  const mockPush = vi.fn()
  const mockOnSuccess = vi.fn()
  const mockOnError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({ push: mockPush })
    // Reset window.location.href tracking
    delete (window as any).location
    ;(window as any).location = { href: '' }
  })

  it('should handle OAuth success with onSuccess callback pattern', () => {
    // This test documents that LoginFormOAuth should always use the onSuccess
    // callback pattern to enable proper success screen flow like regular login
    render(
      <LoginFormOAuth
        isLoading={false}
        returnUrl="/program"
        onError={mockOnError}
        onSuccess={mockOnSuccess} // Should always be used when provided
      />
    )

    // Verify component renders correctly
    expect(
      screen.getByRole('button', { name: /sign in with google/i })
    ).toBeInTheDocument()
  })

  it('should use router.push when onSuccess is not provided', async () => {
    // Import the module to get access to mocked functions
    const { useOAuth } = await import('@/hooks/useOAuth')
    const mockSignInWithGoogle = vi.fn()

    // Mock the hook to capture the success callback
    ;(useOAuth as any).mockReturnValue({
      hasAnyProvider: true,
      google: { isConfigured: true },
      apple: { isConfigured: true },
      signInWithGoogle: mockSignInWithGoogle,
      signInWithApple: vi.fn(),
      isLoading: null,
      error: null,
    })

    render(
      <LoginFormOAuth
        isLoading={false}
        returnUrl="/program"
        onError={mockOnError}
        // Note: onSuccess is NOT provided, component uses direct navigation
      />
    )

    const googleButton = screen.getByRole('button', {
      name: /sign in with google/i,
    })
    fireEvent.click(googleButton)

    // Verify signInWithGoogle was called
    expect(mockSignInWithGoogle).toHaveBeenCalled()

    // Get the success callback that was passed to signInWithGoogle
    const [successCallback] = mockSignInWithGoogle.mock.calls[0]

    // Simulate successful OAuth
    await successCallback()

    // Should NOT set window.location.href
    expect(window.location.href).toBe('')

    // Should use router.push for direct navigation
    expect(mockPush).toHaveBeenCalledWith('/program')
  })

  it('should call onSuccess callback when provided instead of navigation', async () => {
    const { useOAuth } = await import('@/hooks/useOAuth')
    const mockSignInWithGoogle = vi.fn()

    ;(useOAuth as any).mockReturnValue({
      hasAnyProvider: true,
      google: { isConfigured: true },
      apple: { isConfigured: true },
      signInWithGoogle: mockSignInWithGoogle,
      signInWithApple: vi.fn(),
      isLoading: null,
      error: null,
    })

    render(
      <LoginFormOAuth
        isLoading={false}
        returnUrl="/program"
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    )

    const googleButton = screen.getByRole('button', {
      name: /sign in with google/i,
    })
    fireEvent.click(googleButton)

    const [successCallback] = mockSignInWithGoogle.mock.calls[0]
    await successCallback()

    // Should call onSuccess
    expect(mockOnSuccess).toHaveBeenCalled()

    // Should NOT navigate
    expect(mockPush).not.toHaveBeenCalled()
    expect(window.location.href).toBe('')
  })

  it('should use router.push for Apple sign-in when onSuccess not provided', async () => {
    const { useOAuth } = await import('@/hooks/useOAuth')
    const mockSignInWithApple = vi.fn()

    ;(useOAuth as any).mockReturnValue({
      hasAnyProvider: true,
      google: { isConfigured: true },
      apple: { isConfigured: true },
      signInWithGoogle: vi.fn(),
      signInWithApple: mockSignInWithApple,
      isLoading: null,
      error: null,
    })

    render(
      <LoginFormOAuth
        isLoading={false}
        returnUrl="/workout"
        onError={mockOnError}
        // Note: onSuccess is NOT provided
      />
    )

    const appleButton = screen.getByRole('button', {
      name: /sign in with apple/i,
    })
    fireEvent.click(appleButton)

    const [successCallback] = mockSignInWithApple.mock.calls[0]
    await successCallback()

    // Should use router.push for direct navigation
    expect(mockPush).toHaveBeenCalledWith('/workout')

    // Should NOT use window.location.href
    expect(window.location.href).toBe('')
  })
})
