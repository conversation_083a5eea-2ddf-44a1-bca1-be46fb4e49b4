import React from 'react'

interface WeightInputProps {
  weight: number
  unit: 'lbs' | 'kg'
  onChange: (value: string) => void
  onIncrement: () => void
  onDecrement: () => void
  disabled?: boolean
  error?: string
}

export function WeightInput({
  weight,
  unit,
  onChange,
  onIncrement,
  onDecrement,
  disabled = false,
  error,
}: WeightInputProps) {
  // Dynamic text sizing based on weight value
  const getTextSizeClass = (value: number): string => {
    // Check if the number is an integer
    const isInteger = value % 1 === 0

    // Get the integer part for digit counting
    const integerPart = Math.floor(Math.abs(value))
    const integerDigits = integerPart.toString().length

    // For non-integers, we need to consider the full display length
    // Assuming 2 decimal places max for weights (common in fitness apps)
    if (!isInteger) {
      // Total characters: integer digits + 1 (dot) + 2 (decimal places)
      const totalLength = integerDigits + 3
      if (totalLength >= 6) {
        return 'text-4xl' // e.g., "999.99"
      }
      if (totalLength >= 5) {
        return 'text-5xl' // e.g., "99.99" or "17.50"
      }
      return 'text-5xl' // Default for decimals
    }

    // For integers, base size on digit count
    if (integerDigits >= 4) {
      return 'text-5xl' // 1000+
    }
    if (integerDigits === 3) {
      return 'text-6xl' // 100-999
    }
    // For 1-2 digit integers
    return 'text-7xl'
  }

  const textSizeClass = getTextSizeClass(weight)

  return (
    <div>
      <div className="flex items-center justify-center gap-4">
        <button
          type="button"
          onClick={onDecrement}
          disabled={disabled}
          className={`p-3 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Decrease weight"
        >
          <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>

        <div className="relative">
          <input
            id="weight-input"
            type="number"
            value={weight}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            min={0}
            max={1000}
            step={0.5}
            inputMode="decimal"
            className={`w-36 ${textSizeClass} font-bold text-white text-center bg-transparent focus:outline-none transition-opacity ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            aria-label="Weight"
            aria-invalid={!!error}
            aria-describedby={error ? 'weight-error' : undefined}
          />
          <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-lg text-text-secondary">
            {unit.toUpperCase()}
          </span>
        </div>

        <button
          type="button"
          onClick={onIncrement}
          disabled={disabled}
          className={`p-3 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Increase weight"
        >
          <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5M12 5v14"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
      {error && (
        <p
          id="weight-error"
          className="mt-2 text-sm text-error text-center"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  )
}
