import { test, expect } from '@playwright/test'

test.describe('Exercise V2 Navigation Title', () => {
  test('should display exercise name in navigation bar @critical', async ({
    page,
  }) => {
    // Mock the API responses
    await page.route('**/login', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Email: '<EMAIL>',
          AccessToken: 'mock-token',
          RefreshToken: 'mock-refresh-token',
        }),
      })
    })

    // Mock workout data
    await page.route('**/workout**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Exercises: [
            {
              Id: 1,
              Label: 'Bench Press',
              IsFinished: false,
            },
          ],
        }),
      })
    })

    // Mock recommendation
    await page.route('**/exercise/1/recommendation**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          WeightIncrement: 5,
          Reps: 10,
          Weight: { Kg: 60, Lb: 135 },
        }),
      })
    })

    // Mock sets data
    await page.route('**/exercise/1/sets**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([]),
      })
    })

    // Start from login
    await page.goto('/login')

    // Login
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')

    // Navigate to exercise v2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for the page to load and check the navigation title
    await page.waitForSelector('[data-testid="exercise-info-header"]', {
      state: 'visible',
    })

    // The exercise name should appear in the navigation bar title
    // Since the IOSNavigationBar shows the exercise name when isExercisePage is true
    const navBar = page.locator('header[role="banner"]')
    await expect(navBar).toBeVisible()

    // Check that the exercise name appears in the navigation
    await expect(navBar).toContainText('Bench Press')
  })

  test('should clear navigation title when leaving exercise page @critical', async ({
    page,
  }) => {
    // Setup mocks (same as above)
    await page.route('**/login', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Email: '<EMAIL>',
          AccessToken: 'mock-token',
          RefreshToken: 'mock-refresh-token',
        }),
      })
    })

    await page.route('**/workout**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          Exercises: [{ Id: 1, Label: 'Bench Press', IsFinished: false }],
        }),
      })
    })

    await page.route('**/exercise/1/recommendation**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 1,
          WeightIncrement: 5,
          Reps: 10,
          Weight: { Kg: 60, Lb: 135 },
        }),
      })
    })

    await page.route('**/exercise/1/sets**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([]),
      })
    })

    // Login and navigate to exercise
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')

    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-info-header"]')

    // Navigate away by clicking back button
    const backButton = page.locator('header[role="banner"] button').first()
    await backButton.click()

    // Should navigate to workout page
    await page.waitForURL('**/workout')

    // Navigation title should no longer show "Bench Press"
    const navBar = page.locator('header[role="banner"]')
    await expect(navBar).not.toContainText('Bench Press')
  })
})
