import { describe, it, expect } from 'vitest'
import { computeOneRM } from '../oneRmCalculator'

describe('oneRmCalculator - Mobile App Coefficient', () => {
  it('should match mobile app coefficient for user scenario', () => {
    /**
     * Test rationale: This test verifies we match the mobile app coefficient
     * Mobile app uses coefficient 0.03921568 (not 1/30 = 0.0333)
     * From mobile app investigation: AppThemeConstants.Coeficent() returns (decimal)0.03921568
     */
    const weight = 60
    const reps = 5

    // What mobile app calculates (exact coefficient from investigation)
    const expected1RM = 0.03921568 * reps * weight + weight // 71.76470...

    // What our current implementation calculates
    const actual1RM = computeOneRM(weight, reps)

    // This should now pass with mobile app coefficient
    expect(actual1RM).toBe(expected1RM)
  })

  it('should fail with current coefficient for high precision scenario', () => {
    /**
     * Test rationale: Verify precision matters for different weight/rep combinations
     * Higher weights amplify the precision difference
     */
    const weight = 100
    const reps = 10

    const expected1RM = 0.03921568 * reps * weight + weight // 139.21568
    const actual1RM = computeOneRM(weight, reps)

    // This should fail, showing ~0.1kg difference
    expect(actual1RM).toBeCloseTo(expected1RM, 6)
  })

  it('should calculate progress correctly when coefficients match', () => {
    /**
     * Test rationale: When both calculations use same coefficient,
     * identical inputs should yield 0% progress
     */
    const weight = 60
    const reps = 5

    // Simulate API's FirstWorkSet1RM (calculated with mobile app coefficient)
    const previous1RM = 0.03921568 * reps * weight + weight

    // Our calculation should match exactly for 0% progress
    const current1RM = computeOneRM(weight, reps)

    const progress = ((current1RM - previous1RM) / previous1RM) * 100

    // This will fail with current 0.0333 coefficient, showing negative progress
    expect(progress).toBe(0)
  })

  it('should handle edge case of single rep without precision issues', () => {
    /**
     * Test rationale: Single rep should always equal weight regardless of coefficient
     */
    const weight = 100
    const reps = 1

    const result = computeOneRM(weight, reps)

    // This should pass - single rep always equals weight
    expect(result).toBe(weight)
  })

  it('should match mobile app calculation for common rep ranges', () => {
    /**
     * Test rationale: Verify precision consistency across typical rep ranges
     */
    const testCases = [
      { weight: 50, reps: 3 },
      { weight: 80, reps: 8 },
      { weight: 100, reps: 12 },
      { weight: 60, reps: 15 },
    ]

    testCases.forEach(({ weight, reps }) => {
      const expected = 0.03921568 * reps * weight + weight
      const actual = computeOneRM(weight, reps)

      // These will fail with current coefficient, showing precision gaps
      expect(actual).toBeCloseTo(expected, 6)
    })
  })
})
