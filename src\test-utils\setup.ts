/**
 * Global test setup
 * This file is imported by vitest.config.ts to set up the test environment
 */

import { vi, beforeEach, afterEach } from 'vitest'
import { setupGlobalApiMocks, resetApiMocks } from './api-mocks'
import { setupFirebaseMocks, resetFirebaseMocks } from './firebase-mocks'
import { resetWorkoutMocks } from './workout-mocks'

// Mock Next.js navigation hooks globally
export const mockPush = vi.fn()
export const mockReplace = vi.fn()
export const mockBack = vi.fn()
export const mockForward = vi.fn()
export const mockRefresh = vi.fn()
export const mockPrefetch = vi.fn()
export const mockRedirect = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    back: mockBack,
    forward: mockForward,
    refresh: mockRefresh,
    prefetch: mockPrefetch,
  }),
  useSearchParams: () => {
    const mockSearchParams = new URLSearchParams()
    // Add mock methods that return predictable values for tests
    mockSearchParams.get = vi.fn().mockReturnValue(null)
    mockSearchParams.getAll = vi.fn().mockReturnValue([])
    mockSearchParams.has = vi.fn().mockReturnValue(false)
    mockSearchParams.keys = vi.fn().mockReturnValue([][Symbol.iterator]())
    mockSearchParams.values = vi.fn().mockReturnValue([][Symbol.iterator]())
    mockSearchParams.entries = vi.fn().mockReturnValue([][Symbol.iterator]())
    mockSearchParams.forEach = vi.fn()
    mockSearchParams.toString = vi.fn().mockReturnValue('')
    return mockSearchParams
  },
  usePathname: () => '/test-path',
  redirect: mockRedirect,
  notFound: vi.fn(),
  permanentRedirect: vi.fn(),
}))

// Set up test environment variables
// eslint-disable-next-line import/newline-after-import
;(process.env as Record<string, string>).NODE_ENV = 'test'
process.env.NEXT_PUBLIC_DISABLE_OAUTH = 'true'
process.env.NEXT_PUBLIC_API_URL = 'https://drmuscle.azurewebsites.net'

// Mock Firebase OAuth configuration to prevent initialization
process.env.NEXT_PUBLIC_FIREBASE_API_KEY = 'test-api-key'
process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN = 'test-domain.firebaseapp.com'
process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID = 'test-project'
process.env.GOOGLE_CLIENT_ID = 'test-google-client-id'

// Global mocks for browser APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
  writable: true,
})

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

console.error = vi.fn((...args) => {
  // Only show errors that aren't expected test errors
  const message = args[0]?.toString() || ''
  if (
    !message.includes('Failed to initialize Firebase OAuth') &&
    !message.includes("googleClientId: 'NOT SET'") &&
    !message.includes('Warning: ReactDOM.render is deprecated')
  ) {
    originalConsoleError(...args)
  }
})

console.warn = vi.fn((...args) => {
  // Only show warnings that aren't expected test warnings
  const message = args[0]?.toString() || ''
  if (
    !message.includes('componentWillReceiveProps') &&
    !message.includes('componentWillUpdate')
  ) {
    originalConsoleWarn(...args)
  }
})

// Set up global mocks before each test
beforeEach(() => {
  setupGlobalApiMocks()
  setupFirebaseMocks()

  // Reset Next.js navigation mocks
  mockPush.mockClear()
  mockReplace.mockClear()
  mockBack.mockClear()
  mockForward.mockClear()
  mockRefresh.mockClear()
  mockPrefetch.mockClear()
  mockRedirect.mockClear()

  // Reset localStorage and sessionStorage
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()

  sessionStorageMock.getItem.mockClear()
  sessionStorageMock.setItem.mockClear()
  sessionStorageMock.removeItem.mockClear()
  sessionStorageMock.clear.mockClear()
})

// Clean up after each test
afterEach(() => {
  resetApiMocks()
  resetFirebaseMocks()
  resetWorkoutMocks()
  vi.clearAllMocks()
})

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Suppress specific warnings that are expected in test environment
const originalWarn = console.warn
console.warn = (...args) => {
  const message = args[0]?.toString() || ''
  if (
    message.includes('ReactDOM.render is deprecated') ||
    message.includes('componentWillReceiveProps') ||
    message.includes('componentWillUpdate') ||
    message.includes('Failed to initialize Firebase OAuth')
  ) {
    return
  }
  originalWarn(...args)
}
