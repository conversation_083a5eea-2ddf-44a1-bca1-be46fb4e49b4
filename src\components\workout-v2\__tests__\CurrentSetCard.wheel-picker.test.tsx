import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { CurrentSetCard } from '../CurrentSetCard'
import type { ExerciseModel, WorkoutLogSerieModel } from '@/types'

// Mock the haptics utility
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

describe('CurrentSetCard - Wheel Picker Integration', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: false,
    Path: '',
    MediumPath: null,
    Sets: [],
    ExerciseVariations: [],
    Images: [],
  }

  const mockCurrentSet: WorkoutLogSerieModel = {
    Id: 1,
    Reps: 10,
    Weight: { Kg: 45, Lb: 100 },
    IsWarmups: false,
    SetTitle: 'Set 1',
  }

  const defaultProps = {
    exercise: mockExercise,
    currentSet: mockCurrentSet,
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    onComplete: vi.fn(),
    onSkip: vi.fn(),
    isSaving: false,
    unit: 'lbs' as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Layout', () => {
    it('should display reps and weight in vertical arrangement', () => {
      render(<CurrentSetCard {...defaultProps} />)

      const container = screen.getByTestId('input-controls-container')
      expect(container).toHaveClass('flex-col') // Vertical layout

      // Reps should be above weight
      const repsSection = screen.getByTestId('reps-section')
      const weightSection = screen.getByTestId('weight-section')

      const containerChildren = container.children
      expect(containerChildren[0]).toBe(repsSection)
      expect(containerChildren[1]).toBe(weightSection)
    })

    it('should not display asterisk separator', () => {
      render(<CurrentSetCard {...defaultProps} />)

      // Should not find any asterisk in the component
      expect(screen.queryByText('*')).not.toBeInTheDocument()
    })
  })

  describe('Wheel Picker Integration', () => {
    it('should render WheelPicker for reps input', () => {
      render(<CurrentSetCard {...defaultProps} />)

      const repsWheel = screen.getByTestId('reps-wheel-picker')
      expect(repsWheel).toBeInTheDocument()

      // Should show current reps value in the wheel
      const repsValue = screen.getAllByText('10')[0]
      expect(repsValue).toBeInTheDocument()
    })

    it('should render WheelPicker for weight input', () => {
      render(<CurrentSetCard {...defaultProps} />)

      const weightWheel = screen.getByTestId('weight-wheel-picker')
      expect(weightWheel).toBeInTheDocument()

      // Should show current weight value in the wheel
      const weightValue = screen.getAllByText('100')[0]
      expect(weightValue).toBeInTheDocument()
    })

    it('should update reps when interacting with reps wheel picker', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCard {...defaultProps} onSetDataChange={onSetDataChange} />
      )

      // Click on a different reps value
      fireEvent.click(screen.getByText('12'))

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 12,
        weight: 100,
        duration: 0,
      })
    })

    it('should update weight with 2.5 increments for kg', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCard
          {...defaultProps}
          unit="kg"
          setData={{ reps: 10, weight: 45, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Should show 2.5 increments
      expect(screen.getByText('47.5')).toBeInTheDocument()
      expect(screen.getByText('42.5')).toBeInTheDocument()

      fireEvent.click(screen.getByText('47.5'))

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 47.5,
        duration: 0,
      })
    })

    it('should update weight with 5 increments for lbs', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCard {...defaultProps} onSetDataChange={onSetDataChange} />
      )

      // Should show 5 increments for lbs
      expect(screen.getByText('105')).toBeInTheDocument()
      expect(screen.getByText('95')).toBeInTheDocument()

      fireEvent.click(screen.getByText('105'))

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 105,
        duration: 0,
      })
    })
  })

  describe('Mobile Interactions', () => {
    it('should maintain minimum 44px touch targets', () => {
      render(<CurrentSetCard {...defaultProps} />)

      // Check that all clickable wheel values have min-h-[44px] class
      const wheelValues = screen.getAllByRole('button')
      const wheelButtons = wheelValues.filter((btn) =>
        btn.getAttribute('data-testid')?.startsWith('wheel-value-')
      )

      expect(wheelButtons.length).toBeGreaterThan(0)
      wheelButtons.forEach((button) => {
        expect(button).toHaveClass('min-h-[44px]', { exact: false })
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle zero values correctly', () => {
      render(
        <CurrentSetCard
          {...defaultProps}
          setData={{ reps: 0, weight: 0, duration: 0 }}
        />
      )

      // Check for the actual value buttons showing 0
      const zeroButtons = screen.getAllByText('0')
      expect(zeroButtons.length).toBeGreaterThanOrEqual(2)
    })

    it('should disable interaction when saving', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCard
          {...defaultProps}
          isSaving
          onSetDataChange={onSetDataChange}
        />
      )

      // Try to interact with wheel picker
      fireEvent.click(screen.getByText('12'))

      // Should not trigger change when saving
      expect(onSetDataChange).not.toHaveBeenCalled()
    })
  })
})
