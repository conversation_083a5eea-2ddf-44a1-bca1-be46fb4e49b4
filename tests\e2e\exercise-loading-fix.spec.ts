import { test, expect } from '@playwright/test'

test.describe('Exercise Loading Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 })
  })

  test('should not show double loading when using Try New UI', async ({
    page,
  }) => {
    // Track loading indicators
    let hasDoubleLoaded = false

    // Mock auth APIs
    await page.route('**/api/Account/Login*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Token: 'test-token',
          RefreshToken: 'refresh-token',
          User: { Email: '<EMAIL>' },
        }),
      })
    })

    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          MassUnit: 'lbs',
        }),
      })
    })

    // Mock workout data
    await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
          },
        }),
      })
    })

    await page.route(
      '**/api/Workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              Id: 1,
              Label: 'Test Workout',
              WorkoutTemplates: [
                {
                  Id: 1,
                  Label: 'Day 1',
                  Exercices: [
                    { Id: 123, Label: 'Bench Press' },
                    { Id: 456, Label: 'Squat' },
                  ],
                },
              ],
            },
          ]),
        })
      }
    )

    await page.route('**/api/Workout/StartNewWorkout*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 'workout-123',
          WorkoutTemplateId: 1,
          StartTime: new Date().toISOString(),
        }),
      })
    })

    // Track recommendation calls
    const recommendationCallCount = new Map<number, number>()

    await page.route('**/api/Workout/Getrecommendation', async (route) => {
      const requestBody = route.request().postDataJSON()
      const exerciseId = requestBody?.ExerciseId || 0

      const currentCount = recommendationCallCount.get(exerciseId) || 0
      recommendationCallCount.set(exerciseId, currentCount + 1)

      // If we see more than one call for same exercise, we have double loading
      if (currentCount > 0) {
        hasDoubleLoaded = true
      }

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: Math.random(),
          ExerciseId: exerciseId,
          Reps: 10,
          Weight: { Kg: 50, Lb: 110 },
        }),
      })
    })

    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button:has-text("Log In")')

    // Wait for program page
    await page.waitForURL('/program')

    // Click "Open Workout"
    await page.click('text=Open Workout')
    await page.waitForURL('/workout')

    // Track loading states
    page.on('domcontentloaded', () => {
      const loadingElements = page.locator(
        '.loading, [data-loading="true"], .skeleton'
      )
      loadingElements.count().then((count) => {
        if (count > 0) loadingCount++
      })
    })

    // Click "Try our new UI"
    await page.waitForSelector('text=Try our new UI')
    await page.click('text=Start with new exercise view')

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/)

    // Wait for content to load
    await page.waitForSelector('text=Bench Press')

    // Give time for any duplicate loads
    await page.waitForTimeout(2000)

    // Verify no double loading occurred
    expect(hasDoubleLoaded).toBe(false)

    // Verify each exercise was loaded exactly once
    recommendationCallCount.forEach((count) => {
      expect(count).toBe(1)
    })
  })
})
