import { render } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock the store
vi.mock('@/stores/workoutStore')

describe('ExercisePageV2Client - Timer Bottom Padding', () => {
  it('should detect timer state from store', () => {
    // Given: Timer is inactive
    const mockStore = {
      restTimerState: { isActive: false, duration: 0 },
      setRestTimerState: vi.fn(),
    }
    vi.mocked(useWorkoutStore).mockReturnValue(mockStore as any)

    // When: Accessing timer state
    const store = useWorkoutStore()

    // Then: Timer should be inactive
    expect(store.restTimerState.isActive).toBe(false)
  })

  it('should detect active timer state', () => {
    // Given: Timer is active
    const mockStore = {
      restTimerState: { isActive: true, duration: 60 },
      setRestTimerState: vi.fn(),
    }
    vi.mocked(useWorkoutStore).mockReturnValue(mockStore as any)

    // When: Accessing timer state
    const store = useWorkoutStore()

    // Then: Timer should be active
    expect(store.restTimerState.isActive).toBe(true)
    expect(store.restTimerState.duration).toBe(60)
  })

  it('should apply conditional padding class based on timer state', () => {
    // Given: We have a component that uses conditional classes
    function TestComponent() {
      const { restTimerState } = useWorkoutStore()
      return (
        <div
          data-testid="content-area"
          className={`flex-1 ${restTimerState.isActive ? 'pb-32' : ''}`}
        />
      )
    }

    // When: Timer is inactive
    vi.mocked(useWorkoutStore).mockReturnValue({
      restTimerState: { isActive: false, duration: 0 },
    } as any)

    const { container, rerender } = render(<TestComponent />)
    let contentArea = container.querySelector('[data-testid="content-area"]')

    // Then: Should not have pb-32 class
    expect(contentArea?.className).not.toContain('pb-32')

    // When: Timer becomes active
    vi.mocked(useWorkoutStore).mockReturnValue({
      restTimerState: { isActive: true, duration: 60 },
    } as any)

    rerender(<TestComponent />)
    contentArea = container.querySelector('[data-testid="content-area"]')

    // Then: Should have pb-32 class
    expect(contentArea?.className).toContain('pb-32')
  })
})
