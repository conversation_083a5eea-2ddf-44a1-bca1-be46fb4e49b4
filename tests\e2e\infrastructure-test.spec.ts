import { test, expect } from '@playwright/test'

test.describe('Infrastructure Test', () => {
  test('should be able to load the application', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/')
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
    
    // Check that the page loaded successfully
    expect(page.url()).toContain('localhost:3000')
    
    // Check for basic page structure
    const body = await page.locator('body')
    await expect(body).toBeVisible()
    
    // Check that there are no console errors
    const logs = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text())
      }
    })
    
    // Wait a bit to catch any console errors
    await page.waitForTimeout(2000)
    
    // Filter out known CSP violations and other expected errors
    const criticalErrors = logs.filter(log => 
      !log.includes('CSP') && 
      !log.includes('vercel-scripts') &&
      !log.includes('Content Security Policy')
    )
    
    expect(criticalErrors).toHaveLength(0)
  })

  test('should be able to navigate to login page', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Try to navigate to login page
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    
    // Check that we're on the login page
    expect(page.url()).toContain('/login')
    
    // Check for login form elements
    const emailInput = page.locator('input[type="email"], input[name="email"]')
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    
    // At least one of these should be visible (depending on the login implementation)
    const hasEmailInput = await emailInput.count() > 0
    const hasPasswordInput = await passwordInput.count() > 0
    
    expect(hasEmailInput || hasPasswordInput).toBe(true)
  })

  test('should handle 404 pages gracefully', async ({ page }) => {
    await page.goto('/non-existent-page')
    await page.waitForLoadState('networkidle')
    
    // Should either redirect to a valid page or show a 404 page
    const title = await page.title()
    const bodyText = await page.locator('body').textContent()
    
    // Check that the page loaded something meaningful
    expect(title).toBeTruthy()
    expect(bodyText).toBeTruthy()
    expect(bodyText.length).toBeGreaterThan(10)
  })
})
