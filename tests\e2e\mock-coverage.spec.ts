import { test, expect } from '@playwright/test'
import { mockApi } from './helpers/mock-api'

/**
 * Mock Coverage Smoke Tests
 * Validates that all critical API endpoints return 200 responses when mocked
 * Tagged with @mock for easy filtering
 */

test.describe('Mock API Coverage @mock', () => {
  test.beforeEach(async ({ page }) => {
    // Only setup mocks if USE_API_MOCK is enabled
    if (process.env.USE_API_MOCK === '1') {
      await mockApi(page)
    }

    // Navigate to a simple page to establish context
    await page.goto(
      'data:text/html,<html><body>Mock Coverage Test</body></html>'
    )
  })

  const criticalEndpoints = [
    {
      method: 'POST',
      path: '/api/Account/Login',
      description: 'User authentication',
      expectedFields: ['Token', 'User'],
    },
    {
      method: 'GET',
      path: '/api/Workout/GetUserWorkoutTemplateGroup',
      description: 'User workout templates',
      expectedFields: ['Id', 'Label'],
    },
    {
      method: 'GET',
      path: '/api/workout/exercise/123',
      description: 'Exercise details',
      expectedFields: ['Id', 'Name'],
    },
    {
      method: 'GET',
      path: '/api/recommendations/123',
      description: 'Exercise recommendations',
      expectedFields: ['Weight', 'Reps'],
    },
  ]

  criticalEndpoints.forEach(({ method, path, description, expectedFields }) => {
    test(`should mock ${method} ${path} (${description})`, async ({ page }) => {
      test.skip(process.env.USE_API_MOCK !== '1', 'API mocking not enabled')

      let response

      if (method === 'GET') {
        response = await page.request.get(path)
      } else if (method === 'POST') {
        response = await page.request.post(path, {
          data: { test: 'data' },
        })
      } else if (method === 'PUT') {
        response = await page.request.put(path, {
          data: { test: 'data' },
        })
      } else if (method === 'DELETE') {
        response = await page.request.delete(path)
      }

      // Verify response status
      expect(response.status()).toBe(200)

      // Verify response content type
      expect(response.headers()['content-type']).toContain('application/json')

      // Verify response body structure
      const responseData = await response.json()
      expect(responseData).toBeDefined()

      // Check for expected fields in the response
      if (Array.isArray(responseData)) {
        // For array responses, check the first item
        if (responseData.length > 0) {
          expectedFields.forEach((field) => {
            expect(responseData[0]).toHaveProperty(field)
          })
        }
      } else {
        // For object responses, check directly
        expectedFields.forEach((field) => {
          expect(responseData).toHaveProperty(field)
        })
      }
    })
  })

  test('should handle missing mock fixtures gracefully', async ({ page }) => {
    test.skip(process.env.USE_API_MOCK !== '1', 'API mocking not enabled')

    const response = await page.request.get('/api/nonexistent/endpoint')

    expect(response.status()).toBe(404)

    const errorData = await response.json()
    expect(errorData.error).toBe('Mock not found')
    expect(errorData.fixture).toBe('GET/nonexistent/endpoint.json')
  })

  test('should provide consistent response times', async ({ page }) => {
    test.skip(process.env.USE_API_MOCK !== '1', 'API mocking not enabled')

    const startTime = Date.now()

    // Make multiple requests to test consistency
    const requests = [
      page.request.post('/api/Account/Login'),
      page.request.get('/api/Workout/GetUserWorkoutTemplateGroup'),
      page.request.get('/api/workout/exercise/123'),
      page.request.get('/api/recommendations/123'),
    ]

    const responses = await Promise.all(requests)
    const endTime = Date.now()

    // All requests should succeed
    responses.forEach((response) => {
      expect(response.status()).toBe(200)
    })

    // Total time should be reasonable (mocked responses should be fast)
    const totalTime = endTime - startTime
    expect(totalTime).toBeLessThan(5000) // 5 seconds max for all requests
  })

  test('should maintain mock state across multiple requests', async ({
    page,
  }) => {
    test.skip(process.env.USE_API_MOCK !== '1', 'API mocking not enabled')

    // Make the same request multiple times
    const responses = await Promise.all([
      page.request.post('/api/Account/Login'),
      page.request.post('/api/Account/Login'),
      page.request.post('/api/Account/Login'),
    ])

    // All responses should be identical
    const responseBodies = await Promise.all(responses.map((r) => r.json()))

    expect(responseBodies[0]).toEqual(responseBodies[1])
    expect(responseBodies[1]).toEqual(responseBodies[2])

    // Token should be consistent
    expect(responseBodies[0].Token).toBe('TEST_TOKEN_12345')
    expect(responseBodies[1].Token).toBe('TEST_TOKEN_12345')
    expect(responseBodies[2].Token).toBe('TEST_TOKEN_12345')
  })

  test('should log mock activity for debugging', async ({ page }) => {
    test.skip(process.env.USE_API_MOCK !== '1', 'API mocking not enabled')

    // Capture console logs
    const consoleLogs: string[] = []
    page.on('console', (msg) => {
      if (msg.text().includes('API Mock:')) {
        consoleLogs.push(msg.text())
      }
    })

    // Make a request that should generate logs
    await page.request.get('/api/Workout/GetUserWorkoutTemplateGroup')

    // Should have logged the mock activity
    expect(consoleLogs.length).toBeGreaterThan(0)
    expect(consoleLogs.some((log) => log.includes('GET'))).toBe(true)
    expect(consoleLogs.some((log) => log.includes('Workout'))).toBe(true)
  })
})
