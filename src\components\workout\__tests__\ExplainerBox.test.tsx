import React from 'react'
import { render, screen } from '@testing-library/react'
import { ExplainerBox } from '../ExplainerBox'
import type { RecommendationModel } from '@/types'

describe('ExplainerBox', () => {
  const createRecommendation = (
    overrides?: Partial<RecommendationModel>
  ): RecommendationModel => ({
    ExerciseId: 1,
    Series: 3,
    Reps: 10,
    Weight: { Lb: 135, Kg: 61.2 },
    WarmupsCount: 2,
    HistorySet: [],
    RpRest: 180,
    NbPauses: 0,
    NbRepsPauses: 0,
    ...overrides,
  })

  it('should render nothing when recommendation is null', () => {
    const { container } = render(
      <ExplainerBox
        recommendation={null}
        currentSetIndex={0}
        isWarmup={false}
        unit="lbs"
      />
    )
    expect(container.firstChild).toBeNull()
  })

  it('should display set structure info', () => {
    const recommendation = createRecommendation({
      WarmupsCount: 2,
      Series: 3,
    })

    render(
      <ExplainerBox
        recommendation={recommendation}
        currentSetIndex={0}
        isWarmup
        unit="lbs"
      />
    )

    expect(screen.getByText('2 warm-ups, 3 work sets')).toBeInTheDocument()
  })

  describe('Historical set display', () => {
    it('should show correct historical data for first warmup set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Warmup sets
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // Work sets
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 4,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={0} // First warmup
          isWarmup
          unit="lbs"
        />
      )

      expect(screen.getByText('Last time: 5 × 95 lbs')).toBeInTheDocument()
    })

    it('should show correct historical data for second warmup set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Warmup sets
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // Work sets
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 4,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={1} // Second warmup
          isWarmup
          unit="lbs"
        />
      )

      expect(screen.getByText('Last time: 3 × 115 lbs')).toBeInTheDocument()
    })

    it('should show correct historical data for first work set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Warmup sets
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // Work sets
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 4,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2} // First work set (after 2 warmups)
          isWarmup={false}
          unit="lbs"
        />
      )

      // Should show the first work set from history
      expect(screen.getByText('Last time: 10 × 135 lbs')).toBeInTheDocument()
    })

    it('should show correct historical data for third work set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Warmup sets
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // Work sets
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 4,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={4} // Third work set (index 4 = 2 warmups + 2 previous work sets)
          isWarmup={false}
          unit="lbs"
        />
      )

      // Should show the third work set from history
      expect(screen.getByText('Last time: 8 × 135 lbs')).toBeInTheDocument()
    })

    it('should handle missing historical sets gracefully', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Only has one warmup and one work set in history
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={3} // Second work set (no history for this)
          isWarmup={false}
          unit="lbs"
        />
      )

      // Should not show "Last time" when no historical data exists
      expect(screen.queryByText(/Last time:/)).not.toBeInTheDocument()
    })

    it('should handle HistorySet with only work sets', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // Only work sets in history (common pattern from API)
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 4,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2} // First work set
          isWarmup={false}
          unit="lbs"
        />
      )

      // Should show the first work set from history
      expect(screen.getByText('Last time: 10 × 135 lbs')).toBeInTheDocument()
    })

    it('should follow mobile app logic for warmup sets (direct index)', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // HistorySet[0] - First warmup
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // HistorySet[1] - Second warmup
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // HistorySet[2] - First work set
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          // HistorySet[3] - Second work set
          {
            Id: 4,
            Reps: 9,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          // HistorySet[4] - Third work set
          {
            Id: 5,
            Reps: 8,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      // Test first warmup - should use HistorySet[0]
      const { rerender } = render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={0}
          isWarmup
          unit="lbs"
        />
      )
      expect(screen.getByText('Last time: 5 × 95 lbs')).toBeInTheDocument()

      // Test second warmup - should use HistorySet[1]
      rerender(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={1}
          isWarmup
          unit="lbs"
        />
      )
      expect(screen.getByText('Last time: 3 × 115 lbs')).toBeInTheDocument()
    })

    it('should use FirstWorkSetReps/Weight for first work set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        FirstWorkSetReps: 12,
        FirstWorkSetWeight: { Lb: 150, Kg: 68 },
        HistorySet: [
          // Warmups
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          {
            Id: 2,
            Reps: 3,
            Weight: { Lb: 115, Kg: 52 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
          // Work sets
          {
            Id: 3,
            Reps: 10, // This should be ignored for first work set
            Weight: { Lb: 135, Kg: 61 }, // This should be ignored for first work set
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      // Test first work set - should use FirstWorkSetReps/Weight
      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2} // First work set (after 2 warmups)
          isWarmup={false}
          isFirstWorkSet
          unit="lbs"
        />
      )
      expect(screen.getByText('Last time: 12 × 150 lbs')).toBeInTheDocument()
    })

    it('should handle mixed-order HistorySet for warmups (direct index)', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        Series: 3,
        HistorySet: [
          // HistorySet[0] - Actually a work set, but warmup will use it
          {
            Id: 3,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          // HistorySet[1] - Actually a work set, but warmup will use it
          {
            Id: 4,
            Reps: 9,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
          // Actual warmups further in array
          {
            Id: 1,
            Reps: 5,
            Weight: { Lb: 95, Kg: 43 },
            IsWarmups: true,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      // Test first warmup - should use HistorySet[0] regardless of IsWarmups flag
      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={0}
          isWarmup
          unit="lbs"
        />
      )
      expect(screen.getByText('Last time: 10 × 135 lbs')).toBeInTheDocument()

      // Test second warmup - should use HistorySet[1] regardless of IsWarmups flag
      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={1}
          isWarmup
          unit="lbs"
        />
      )
      expect(screen.getByText('Last time: 9 × 135 lbs')).toBeInTheDocument()
    })
  })

  describe('1RM Progress', () => {
    it('should show 1RM progress for first work set', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2} // First work set
          isWarmup={false}
          unit="lbs"
          currentReps={10}
          currentWeight={135}
          isFirstWorkSet
        />
      )

      // The actual calculation shows +4.41%
      expect(screen.getByText('1RM Progress: +4.41%')).toBeInTheDocument()
    })

    it('should show positive 1RM progress', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2}
          isWarmup={false}
          unit="lbs"
          currentReps={10}
          currentWeight={140} // Increased weight
          isFirstWorkSet
        />
      )

      // The actual calculation shows +8.28%
      expect(screen.getByText('1RM Progress: +8.28%')).toBeInTheDocument()
    })

    it('should not show 1RM progress for non-first work sets', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={3} // Second work set
          isWarmup={false}
          unit="lbs"
          currentReps={10}
          currentWeight={135}
          isFirstWorkSet={false}
        />
      )

      expect(screen.queryByText(/1RM Progress:/)).not.toBeInTheDocument()
    })

    it('should display 1RM progress with exactly 2 decimal places', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 2,
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={2}
          isWarmup={false}
          unit="lbs"
          currentReps={10}
          currentWeight={140} // Increased weight
          isFirstWorkSet
        />
      )

      // Should display with exactly 2 decimal places (+8.28%)
      expect(screen.getByText('1RM Progress: +8.28%')).toBeInTheDocument()
    })
  })

  describe('Unit handling', () => {
    it('should display weights in kg when unit is kg', () => {
      const recommendation = createRecommendation({
        WarmupsCount: 0, // No warmups, so currentSetIndex 0 is the first work set
        Series: 1,
        HistorySet: [
          {
            Id: 1,
            Reps: 10,
            Weight: { Lb: 135, Kg: 61 },
            IsWarmups: false,
            IsNext: false,
            IsFinished: true,
          },
        ],
      })

      render(
        <ExplainerBox
          recommendation={recommendation}
          currentSetIndex={0}
          isWarmup={false}
          unit="kg"
        />
      )

      expect(screen.getByText('Last time: 10 × 61 kg')).toBeInTheDocument()
    })
  })
})
