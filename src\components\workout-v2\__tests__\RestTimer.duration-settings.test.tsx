import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { RestTimer } from '../RestTimer'
import { useWorkoutStore } from '@/stores/workoutStore'

vi.mock('@/stores/workoutStore')
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

const mockSetRestTimerState = vi.fn()

describe('RestTimer Duration Settings', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    vi.mocked(useWorkoutStore).mockReturnValue({
      restTimerState: { isActive: true, duration: 90 },
      setRestTimerState: mockSetRestTimerState,
    } as any)
  })

  describe('Duration Display', () => {
    it('should show current duration setting next to timer', () => {
      render(<RestTimer />)

      expect(screen.getByTestId('duration-setting')).toBeInTheDocument()
      expect(screen.getByTestId('duration-setting')).toHaveTextContent('1:30')
    })

    it('should make duration display tappable with proper touch target', () => {
      render(<RestTimer />)

      const durationButton = screen.getByTestId('duration-setting-button')
      expect(durationButton).toBeInTheDocument()

      // Check that button has min-h-[44px] class
      expect(durationButton.className).toContain('min-h-[44px]')
    })

    it('should show duration picker when tapped', () => {
      render(<RestTimer />)

      const durationButton = screen.getByTestId('duration-setting-button')
      fireEvent.click(durationButton)

      expect(screen.getByTestId('duration-picker')).toBeInTheDocument()
    })
  })

  describe('Duration Picker', () => {
    it('should show preset duration options', () => {
      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))

      // Common preset durations
      expect(screen.getByText('0:30')).toBeInTheDocument()
      expect(screen.getByText('1:00')).toBeInTheDocument()
      expect(screen.getAllByText('1:30').length).toBeGreaterThan(0)
      expect(screen.getByText('2:00')).toBeInTheDocument()
      expect(screen.getByText('3:00')).toBeInTheDocument()
    })

    it('should update timer when new duration is selected', async () => {
      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))
      fireEvent.click(screen.getByText('2:00'))

      await waitFor(() => {
        expect(localStorage.getItem('restDuration')).toBe('120')
      })

      // Timer should restart with new duration
      expect(mockSetRestTimerState).toHaveBeenCalledWith({
        isActive: true,
        duration: 120,
        nextSetInfo: undefined,
      })

      // Duration picker should close
      expect(screen.queryByTestId('duration-picker')).not.toBeInTheDocument()
    })

    it('should close picker when clicking outside', async () => {
      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))
      expect(screen.getByTestId('duration-picker')).toBeInTheDocument()

      // Click outside (on the backdrop)
      const backdrop =
        screen.getByTestId('duration-picker').previousElementSibling
      fireEvent.click(backdrop!)

      // Wait for animation to complete
      await waitFor(() => {
        expect(screen.queryByTestId('duration-picker')).not.toBeInTheDocument()
      })
    })

    it('should highlight current duration in picker', () => {
      localStorage.setItem('restDuration', '60')

      vi.mocked(useWorkoutStore).mockReturnValue({
        restTimerState: { isActive: true, duration: 60 },
        setRestTimerState: mockSetRestTimerState,
      } as any)

      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))

      const oneMinuteOption = screen.getByTestId('duration-option-60')
      expect(oneMinuteOption).toHaveClass('bg-gradient-to-r')
    })
  })

  describe('localStorage Integration', () => {
    it('should use custom duration from localStorage', () => {
      localStorage.setItem('restDuration', '180')

      vi.mocked(useWorkoutStore).mockReturnValue({
        restTimerState: { isActive: true, duration: 180 },
        setRestTimerState: mockSetRestTimerState,
      } as any)

      render(<RestTimer />)

      expect(screen.getByTestId('duration-setting')).toHaveTextContent('3:00')
    })

    it('should handle invalid localStorage values', () => {
      localStorage.setItem('restDuration', 'invalid')

      render(<RestTimer />)

      // Should fallback to default
      expect(screen.getByTestId('duration-setting')).toHaveTextContent('1:30')
    })

    it('should clamp duration to valid range - minimum', () => {
      localStorage.setItem('restDuration', '2')

      vi.mocked(useWorkoutStore).mockReturnValue({
        restTimerState: { isActive: true, duration: 5 }, // Clamped to minimum
        setRestTimerState: mockSetRestTimerState,
      } as any)

      render(<RestTimer />)
      expect(screen.getByTestId('duration-setting')).toHaveTextContent('0:05')
    })

    it('should clamp duration to valid range - maximum', () => {
      localStorage.setItem('restDuration', '700')

      vi.mocked(useWorkoutStore).mockReturnValue({
        restTimerState: { isActive: true, duration: 600 }, // Clamped to maximum
        setRestTimerState: mockSetRestTimerState,
      } as any)

      render(<RestTimer />)
      expect(screen.getByTestId('duration-setting')).toHaveTextContent('10:00')
    })
  })

  describe('Modal Background Styling', () => {
    it('should apply glassmorphism style to duration picker', () => {
      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))

      const picker = screen.getByTestId('duration-picker')
      expect(picker.className).toContain('bg-surface-primary')
      expect(picker.className).toContain('backdrop-blur')
    })

    it('should have consistent backdrop blur on both modals', () => {
      render(<RestTimer />)

      // Open duration picker
      fireEvent.click(screen.getByTestId('duration-setting-button'))

      // Check backdrop has blur
      const backdrop =
        screen.getByTestId('duration-picker').previousElementSibling
      expect(backdrop?.className).toContain('backdrop-blur')

      // Open custom modal
      fireEvent.click(screen.getByTestId('duration-option-custom'))

      // Check custom modal also has blurred background
      expect(screen.getByTestId('custom-duration-modal')).toBeInTheDocument()
      const customModal = screen.getByTestId('custom-duration-modal')
      expect(customModal.className).toContain('bg-surface-primary')
      expect(customModal.className).toContain('backdrop-blur')
    })
  })

  describe('Timer Running Behavior', () => {
    it('should restart timer immediately when duration is changed', async () => {
      render(<RestTimer />)

      // Timer is running
      expect(screen.getByTestId('countdown-text')).toBeInTheDocument()

      // Change duration
      fireEvent.click(screen.getByTestId('duration-setting-button'))
      fireEvent.click(screen.getByText('2:00'))

      // Should update localStorage and restart timer immediately
      await waitFor(() => {
        expect(localStorage.getItem('restDuration')).toBe('120')
      })

      // Timer should restart with new duration
      expect(mockSetRestTimerState).toHaveBeenCalledWith({
        isActive: true,
        duration: 120,
        nextSetInfo: undefined,
      })
    })

    it('should preserve nextSetInfo when restarting timer', async () => {
      const nextSetInfo = { reps: 12, weight: 55, unit: 'kg' as const }

      vi.mocked(useWorkoutStore).mockReturnValue({
        restTimerState: { isActive: true, duration: 90, nextSetInfo },
        setRestTimerState: mockSetRestTimerState,
      } as any)

      render(<RestTimer />)

      fireEvent.click(screen.getByTestId('duration-setting-button'))
      fireEvent.click(screen.getByText('3:00'))

      // Should preserve nextSetInfo when restarting
      expect(mockSetRestTimerState).toHaveBeenCalledWith({
        isActive: true,
        duration: 180,
        nextSetInfo,
      })
    })
  })
})
