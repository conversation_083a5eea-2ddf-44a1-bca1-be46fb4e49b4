import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { KebabMenuIcon } from '../KebabMenuIcon'

describe('KebabMenuIcon', () => {
  it('should render dots with radius of 2 for better visibility', () => {
    // Testing that kebab menu dots are large enough to be visible and tappable
    const { container } = render(<KebabMenuIcon />)

    const circles = container.querySelectorAll('circle')
    expect(circles).toHaveLength(3)

    // Each dot should have radius of 2 for better visibility
    circles.forEach((circle) => {
      expect(circle.getAttribute('r')).toBe('2')
    })
  })

  it('should maintain proper spacing between dots', () => {
    // Ensure dots are evenly spaced vertically
    const { container } = render(<KebabMenuIcon />)

    const circles = container.querySelectorAll('circle')
    expect(circles[0].getAttribute('cy')).toBe('5')
    expect(circles[1].getAttribute('cy')).toBe('12')
    expect(circles[2].getAttribute('cy')).toBe('19')
  })
})
