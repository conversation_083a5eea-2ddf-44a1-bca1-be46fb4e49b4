import { describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import { act, renderHook } from '@testing-library/react'
import type { WorkoutTemplateModel } from '@/types'

// Mock data for tests
const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Test Workout',
  Exercises: [
    {
      Id: 123,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
    {
      Id: 456,
      Label: 'Shoulder Press',
      Path: 'shoulders/press',
      TargetWeight: { Mass: 60, MassUnit: 'lbs' },
      TargetReps: 10,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

describe('workoutStore', () => {
  beforeEach(() => {
    localStorage.clear()
    const { result } = renderHook(() => useWorkoutStore())
    act(() => {
      result.current.resetWorkout()
    })
  })

  describe('getRestDuration', () => {
    it('should return default duration when no preference is set', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(120)
    })

    it('should return stored duration from localStorage', () => {
      localStorage.setItem('restDuration', '180')

      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(180)
    })

    it('should handle invalid localStorage values', () => {
      const testCases = [
        { stored: 'invalid', expected: 120 },
        { stored: '-100', expected: 120 },
        { stored: '0', expected: 120 },
        { stored: 'null', expected: 120 },
        { stored: '', expected: 120 },
      ]

      testCases.forEach(({ stored, expected }) => {
        localStorage.setItem('restDuration', stored)
        const { result } = renderHook(() => useWorkoutStore())

        expect(result.current.getRestDuration()).toBe(expected)
      })
    })

    it('should clamp duration to valid range', () => {
      const testCases = [
        { stored: '3', expected: 5 }, // Below minimum
        { stored: '700', expected: 600 }, // Above maximum
        { stored: '150', expected: 150 }, // Within range
      ]

      testCases.forEach(({ stored, expected }) => {
        localStorage.setItem('restDuration', stored)
        const { result } = renderHook(() => useWorkoutStore())

        expect(result.current.getRestDuration()).toBe(expected)
      })
    })

    it('should update when localStorage changes', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.getRestDuration()).toBe(120)

      // Simulate localStorage change
      localStorage.setItem('restDuration', '90')
      window.dispatchEvent(new Event('storage'))

      // Store should react to storage event and update
      expect(result.current.getRestDuration()).toBe(90)
    })
  })

  describe('skipExercise', () => {
    it('should mark exercise as finished when skipped', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout and start it
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      // Initially, exercises should not be finished
      expect(result.current.exercises[0].IsFinished).toBe(false)
      expect(result.current.exercises[1].IsFinished).toBe(false)

      // Skip the first exercise
      act(() => {
        result.current.skipExercise(123)
      })

      // First exercise should be marked as finished
      expect(result.current.exercises[0].IsFinished).toBe(true)
      expect(result.current.exercises[1].IsFinished).toBe(false)
    })

    it('should handle skipping already completed exercise', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout and skip first exercise
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
        // Mark first exercise as finished by skipping it
        result.current.skipExercise(123)
      })

      // Verify it's finished
      expect(result.current.exercises[0].IsFinished).toBe(true)

      // Skip already finished exercise again
      act(() => {
        result.current.skipExercise(123)
      })

      // Should remain finished (idempotent)
      expect(result.current.exercises[0].IsFinished).toBe(true)
    })

    it('should handle skipping non-existent exercise', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      // Skip non-existent exercise
      act(() => {
        result.current.skipExercise(999)
      })

      // No exercises should be affected
      expect(result.current.exercises[0].IsFinished).toBe(false)
      expect(result.current.exercises[1].IsFinished).toBe(false)
    })

    it('should work without active workout session', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout but don't start it
      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Skip exercise without active session
      act(() => {
        result.current.skipExercise(123)
      })

      // Exercise should still be marked as finished
      expect(result.current.exercises[0].IsFinished).toBe(true)
    })

    it('should handle skipping last exercise in workout', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout
      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.startWorkout()
      })

      // Skip the last exercise
      act(() => {
        result.current.skipExercise(456)
      })

      // Last exercise should be marked as finished
      expect(result.current.exercises[1].IsFinished).toBe(true)
    })
  })

  describe('preview skip exercise functionality', () => {
    it('should add exercise to previewExerciseSkips when workout not started', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Setup workout but don't start it (preview mode)
      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Initially no skipped exercises
      expect(result.current.previewExerciseSkips).toEqual(new Set())

      // Skip exercise in preview mode
      act(() => {
        result.current.skipPreviewExercise(123)
      })

      // Exercise ID should be added to previewExerciseSkips Set
      expect(result.current.previewExerciseSkips.has(123)).toBe(true)
      expect(result.current.previewExerciseSkips.size).toBe(1)
    })

    it('should handle skipping multiple preview exercises', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Skip multiple exercises
      act(() => {
        result.current.skipPreviewExercise(123)
        result.current.skipPreviewExercise(456)
      })

      // Both should be in the Set
      expect(result.current.previewExerciseSkips.has(123)).toBe(true)
      expect(result.current.previewExerciseSkips.has(456)).toBe(true)
      expect(result.current.previewExerciseSkips.size).toBe(2)
    })

    it('should handle skipping same exercise twice (Set behavior)', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
      })

      // Skip same exercise twice
      act(() => {
        result.current.skipPreviewExercise(123)
        result.current.skipPreviewExercise(123)
      })

      // Should only appear once in Set
      expect(result.current.previewExerciseSkips.has(123)).toBe(true)
      expect(result.current.previewExerciseSkips.size).toBe(1)
    })

    it('should clear preview skips when workout starts', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setWorkout(mockWorkout)
        result.current.skipPreviewExercise(123)
      })

      // Verify skip was added
      expect(result.current.previewExerciseSkips.has(123)).toBe(true)

      // Start workout - should clear preview skips
      act(() => {
        result.current.startWorkout()
      })

      // Preview skips should be cleared
      expect(result.current.previewExerciseSkips.size).toBe(0)
    })

    it('should persist previewExerciseSkips across store recreation', () => {
      // First store instance
      const { result: result1 } = renderHook(() => useWorkoutStore())

      act(() => {
        result1.current.setWorkout(mockWorkout)
        result1.current.skipPreviewExercise(123)
      })

      expect(result1.current.previewExerciseSkips.has(123)).toBe(true)

      // Create new store instance (simulates page reload)
      const { result: result2 } = renderHook(() => useWorkoutStore())

      // Should persist due to Zustand persistence
      expect(result2.current.previewExerciseSkips.has(123)).toBe(true)
    })
  })
})
