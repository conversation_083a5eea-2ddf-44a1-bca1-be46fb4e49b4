import { describe, it, expect } from 'vitest'

// Import the setup to ensure mocks are loaded
import '../setup'

describe('Navigation Mock Validation', () => {
  it('should provide useSearchParams mock', async () => {
    // Dynamic import to ensure mocks are applied
    const { useSearchParams } = await import('next/navigation')

    const searchParams = useSearchParams()
    expect(searchParams).toBeDefined()
    expect(typeof searchParams.get).toBe('function')
    expect(typeof searchParams.has).toBe('function')
  })

  it('should provide useRouter mock', async () => {
    const { useRouter } = await import('next/navigation')

    const router = useRouter()
    expect(router).toBeDefined()
    expect(typeof router.push).toBe('function')
    expect(typeof router.back).toBe('function')
  })
})
