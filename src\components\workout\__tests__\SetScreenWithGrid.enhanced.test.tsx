import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useNavigation } from '@/contexts/NavigationContext'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/contexts/NavigationContext')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lb' }),
  }),
}))
vi.mock('../ExerciseSetsGrid', () => ({
  ExerciseSetsGrid: ({ exercise }: any) => (
    <div data-testid="exercise-sets-grid">
      <h2>{exercise.Label}</h2>
    </div>
  ),
}))

describe('SetScreenWithGrid - Enhanced Features', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Montréal',
    IsBodyweight: false,
    IsSystemExercise: true,
    BodyPartId: 1,
  }

  const mockRecommendation: RecommendationModel = {
    Series: 4,
    Reps: 12,
    Weight: { Lb: 20, Kg: 9 },
    WarmupsCount: 1,
    WarmUpsList: [{ WarmUpReps: 12, WarmUpWeightSet: { Lb: 20, Kg: 9 } }],
    HistorySet: [
      {
        Id: 1,
        Reps: 8,
        Weight: { Lb: 20, Kg: 9 },
        IsWarmups: false,
        SetNo: '1',
      },
    ],
    FirstWorkSet1RM: { Lb: 25, Kg: 11.3 },
    LastLogDate: '2024-01-15',
  } as RecommendationModel

  beforeEach(() => {
    vi.clearAllMocks()

    // Default mock for useNavigation
    vi.mocked(useNavigation).mockReturnValue({
      setTitle: vi.fn(),
    } as any)

    // Default mock for useSetScreenLogic
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      recommendation: mockRecommendation,
      currentSetIndex: 0,
      completedSets: [],
      isLoading: false,
      error: null,
      showComplete: false,
      showExerciseComplete: false,
      showRIRPicker: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      isLastExercise: false,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)
  })

  describe('Exercise Name in Navigation', () => {
    it('should set exercise name as navigation title on mount', () => {
      const mockSetTitle = vi.fn()
      vi.mocked(useNavigation).mockReturnValue({
        setTitle: mockSetTitle,
      } as any)

      render(<SetScreenWithGrid exerciseId={123} />)

      expect(mockSetTitle).toHaveBeenCalledWith('Montréal')
    })

    it('should update navigation title when exercise changes', () => {
      const mockSetTitle = vi.fn()
      vi.mocked(useNavigation).mockReturnValue({
        setTitle: mockSetTitle,
      } as any)

      const { rerender } = render(<SetScreenWithGrid exerciseId={123} />)

      // Change to a different exercise
      vi.mocked(useSetScreenLogic).mockReturnValue({
        ...vi.mocked(useSetScreenLogic).mock.results[0].value,
        currentExercise: { ...mockExercise, Label: 'Bench Press' },
      } as any)

      rerender(<SetScreenWithGrid exerciseId={456} />)

      expect(mockSetTitle).toHaveBeenCalledWith('Bench Press')
    })
  })

  describe('Explainer Box Display', () => {
    it('should display explainer box with warmup and work set counts', () => {
      function ExplainerBox({ warmupCount, workSetCount }: any) {
        return (
          <div data-testid="explainer-box" className="explainer-box">
            <p>
              {warmupCount} warm-up{warmupCount !== 1 ? 's' : ''},{' '}
              {workSetCount} work set{workSetCount !== 1 ? 's' : ''}
            </p>
          </div>
        )
      }

      render(<ExplainerBox warmupCount={1} workSetCount={4} />)

      expect(screen.getByText('1 warm-up, 4 work sets')).toBeInTheDocument()
    })

    it('should display last time values from history for current set', () => {
      function ExplainerBox({
        recommendation,
        currentSetIndex,
        isWarmup,
      }: any) {
        // Get the corresponding history set
        const historyIndex = isWarmup
          ? 0
          : currentSetIndex - recommendation.WarmupsCount
        const historySet = recommendation.HistorySet?.[historyIndex]

        if (!historySet) return null

        return (
          <div data-testid="explainer-box" className="explainer-box">
            <p>
              Last time: {historySet.Reps} × {historySet.Weight.Lb} lbs
            </p>
          </div>
        )
      }

      render(
        <ExplainerBox
          recommendation={mockRecommendation}
          currentSetIndex={1} // First work set
          isWarmup={false}
        />
      )

      expect(screen.getByText('Last time: 8 × 20 lbs')).toBeInTheDocument()
    })

    it('should handle metric units in explainer box', () => {
      function ExplainerBox({ recommendation, unit }: any) {
        const historySet = recommendation.HistorySet?.[0]
        if (!historySet) return null

        const weight =
          unit === 'kg' ? historySet.Weight.Kg : historySet.Weight.Lb
        const unitLabel = unit === 'kg' ? 'kg' : 'lbs'

        return (
          <div data-testid="explainer-box">
            <p>
              Last time: {historySet.Reps} × {weight} {unitLabel}
            </p>
          </div>
        )
      }

      render(<ExplainerBox recommendation={mockRecommendation} unit="kg" />)

      expect(screen.getByText('Last time: 8 × 9 kg')).toBeInTheDocument()
    })
  })

  describe('1RM Percentage Display', () => {
    it('should display 1RM progress percentage for first work set', () => {
      function OneRMProgress({
        isFirstWorkSet,
        currentWeight,
        currentReps,
        previousRM,
      }: any) {
        if (!isFirstWorkSet) return null

        // Dr. Muscle formula: 1RM = (0.0333 * reps) * weight + weight
        const calculate1RM = (weight: number, reps: number) => {
          if (reps === 1) return weight
          return 0.0333 * reps * weight + weight
        }

        const current1RM = calculate1RM(currentWeight, currentReps)
        const percentageChange = ((current1RM - previousRM) / previousRM) * 100

        return (
          <div data-testid="onerm-progress" className="onerm-progress">
            <p>
              1RM Progress: {percentageChange > 0 ? '+' : ''}
              {percentageChange.toFixed(2)}%
            </p>
          </div>
        )
      }

      render(
        <OneRMProgress
          isFirstWorkSet
          currentWeight={22}
          currentReps={12}
          previousRM={25}
        />
      )

      // Current 1RM = (0.0333 * 12) * 22 + 22 = 30.7912
      // Change = (30.7912 - 25) / 25 × 100 = 23.16%
      expect(screen.getByText('1RM Progress: +23.16%')).toBeInTheDocument()
    })

    it('should not display 1RM progress for warmup sets', () => {
      function OneRMProgress({ isWarmup }: any) {
        if (isWarmup) return null
        return <div data-testid="onerm-progress">1RM Progress</div>
      }

      render(<OneRMProgress isWarmup />)

      expect(screen.queryByTestId('onerm-progress')).not.toBeInTheDocument()
    })

    it('should handle negative 1RM progress', () => {
      function OneRMProgress({ currentWeight, currentReps, previousRM }: any) {
        const calculate1RM = (weight: number, reps: number) => {
          return 0.0333 * reps * weight + weight
        }

        const current1RM = calculate1RM(currentWeight, currentReps)
        const percentageChange = ((current1RM - previousRM) / previousRM) * 100

        return (
          <div data-testid="onerm-progress">
            <p>
              1RM Progress: {percentageChange > 0 ? '+' : ''}
              {percentageChange.toFixed(2)}%
            </p>
          </div>
        )
      }

      render(
        <OneRMProgress currentWeight={18} currentReps={10} previousRM={30} />
      )

      // Current 1RM = (0.0333 * 10) * 18 + 18 = 23.994
      // Change = (23.994 - 30) / 30 × 100 = -20.02%
      expect(screen.getByText('1RM Progress: -20.02%')).toBeInTheDocument()
    })
  })

  describe('Complete Integration', () => {
    it('should only show explainer box and 1RM for active set', () => {
      function ActiveSetInfo({ warmupCount, isFirstWorkSet }: any) {
        const isActive = true // Only render for active set

        if (!isActive) return null

        return (
          <div data-testid="active-set-info">
            <div className="explainer-box">
              <p>{warmupCount} warm-up, 4 work sets</p>
              <p>Last time: 8 × 20 lbs</p>
            </div>
            {isFirstWorkSet && (
              <div className="onerm-progress">
                <p>1RM Progress: +170.29%</p>
              </div>
            )}
          </div>
        )
      }

      render(
        <ActiveSetInfo currentSetIndex={1} warmupCount={1} isFirstWorkSet />
      )

      expect(screen.getByTestId('active-set-info')).toBeInTheDocument()
      expect(screen.getByText('1 warm-up, 4 work sets')).toBeInTheDocument()
      expect(screen.getByText('1RM Progress: +170.29%')).toBeInTheDocument()
    })
  })
})
