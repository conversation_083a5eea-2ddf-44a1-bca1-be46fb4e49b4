import { Page, Route, Request } from '@playwright/test'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

/**
 * Mock API helper that intercepts API calls and serves static JSON fixtures
 * Based on the CI optimization plan for stabilizing E2E tests
 */
export async function mockApi(page: Page): Promise<void> {
  console.warn('🔧 Setting up API mocking for page...')

  await page.route('**/api/**', async (route: Route, request: Request) => {
    const method = request.method()
    const url = new URL(request.url())
    const { pathname } = url

    // Extract the API path (remove /api prefix)
    const apiPath = pathname.replace(/^\/api\//, '')

    // Build fixture key: METHOD/path.json
    const fixtureKey = `${method}/${apiPath}.json`
    const fixturePath = join(process.cwd(), 'tests', 'fixtures', fixtureKey)

    console.warn(`🔍 API Mock: ${method} ${pathname} -> ${fixtureKey}`)

    try {
      if (existsSync(fixturePath)) {
        const fixtureContent = readFileSync(fixturePath, 'utf-8')
        const jsonData = JSON.parse(fixtureContent)

        console.warn(`✅ Serving fixture: ${fixtureKey}`)
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(jsonData),
        })
      } else {
        console.warn(`❌ Fixture not found: ${fixtureKey}`)
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Mock not found',
            path: apiPath,
            fixture: fixtureKey,
          }),
        })
      }
    } catch (error) {
      console.error(`❌ Error serving fixture ${fixtureKey}:`, error)
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Mock error',
          message: error instanceof Error ? error.message : String(error),
          fixture: fixtureKey,
        }),
      })
    }
  })

  console.warn('✅ API mocking setup complete')
}
