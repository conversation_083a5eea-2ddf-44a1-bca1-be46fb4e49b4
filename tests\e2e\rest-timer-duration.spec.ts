import { test, expect } from '@playwright/test'

test.describe('Rest Timer Duration Settings', () => {
  test('should allow user to change rest timer duration', async ({ page }) => {
    // Navigate to rest timer page
    await page.goto('/workout/rest-timer')

    // Open settings menu from navigation
    await page.click('[aria-label="Timer settings"]')

    // Click on Rest Duration button
    await page.click('button:has-text("Rest Duration")')

    // Select 3:00 (180 seconds)
    await page.click('button:has-text("3:00")')

    // Verify localStorage was updated
    const restDuration = await page.evaluate(() =>
      localStorage.getItem('restDuration')
    )
    expect(restDuration).toBe('180')

    // Reload page to verify persistence
    await page.reload()

    // Open settings menu again
    await page.click('[aria-label="Timer settings"]')

    // Verify the duration shows 3:00
    await expect(page.locator('text=3:00').first()).toBeVisible()
  })

  test('should use custom rest duration in timer', async ({ page }) => {
    // Set custom duration
    await page.evaluate(() => localStorage.setItem('restDuration', '60'))

    // Navigate to actual rest timer page
    await page.goto('/workout/rest-timer')

    // Timer should show 1:00
    await expect(page.locator('[role="timer"]')).toContainText('1:00')
  })

  test('should have all duration options available', async ({ page }) => {
    // Navigate to rest timer page
    await page.goto('/workout/rest-timer')

    // Open settings menu
    await page.click('[aria-label="Timer settings"]')

    // Click on Rest Duration
    await page.click('button:has-text("Rest Duration")')

    // Check all options are visible
    const durations = [
      '0:05',
      '0:20',
      '0:30',
      '1:00',
      '1:30',
      '2:00',
      '3:00',
      '5:00',
      'Custom',
    ]
    await Promise.all(
      durations.map((duration) =>
        expect(page.locator(`button:has-text("${duration}")`)).toBeVisible()
      )
    )
  })

  test('should close duration picker when clicking outside', async ({
    page,
  }) => {
    // Navigate to rest timer page
    await page.goto('/workout/rest-timer')

    // Open settings menu
    await page.click('[aria-label="Timer settings"]')

    // Click on Rest Duration
    await page.click('button:has-text("Rest Duration")')

    // Verify picker is open
    await expect(page.locator('button:has-text("0:30")')).toBeVisible()

    // Click outside
    await page.click('body', { position: { x: 10, y: 10 } })

    // Verify picker is closed
    await expect(page.locator('button:has-text("0:30")')).not.toBeVisible()
  })
})
