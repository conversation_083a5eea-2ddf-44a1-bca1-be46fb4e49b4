import { describe, it, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useSetMetrics } from '../useSetMetrics'
import type { RecommendationModel } from '@/types'
import { computeOneRM } from '@/utils/oneRmCalculator'

describe('useSetMetrics - 1RM Formula Fix', () => {
  it('should return 0% progress when current matches last workout exactly', () => {
    // Test data based on real issue: Last time 5 × 60 kg
    const weight = 60
    const reps = 5

    // Calculate what the API would have sent using Dr. Muscle formula
    const previous1RM = computeOneRM(weight, reps) // 69.99kg

    const mockRecommendation: RecommendationModel = {
      Id: 1,
      WarmupsCount: 2,
      Series: 6,
      FirstWorkSetReps: reps,
      FirstWorkSetWeight: { Kg: weight, Lb: weight * 2.20462 },
      FirstWorkSet1RM: { Kg: previous1RM, Lb: previous1RM * 2.20462 },
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetMetrics({
        recommendation: mockRecommendation,
        currentSetIndex: 2, // First work set
        isWarmup: false,
        unit: 'kg',
        isFirstWorkSet: true,
        currentReps: reps, // Matching exactly
        currentWeight: weight, // Matching exactly
      })
    )

    // This test will fail with current Epley formula showing -2.37%
    // After fix with Dr. Muscle formula, it should show 0%
    expect(result.current.oneRMProgress).toBe(0)
  })

  it('should calculate correct progress with Dr. Muscle formula', () => {
    // Previous: 10 reps × 50 kg
    const prevWeight = 50
    const prevReps = 10
    const previous1RM = computeOneRM(prevWeight, prevReps) // 66.65kg

    // Current: 12 reps × 50 kg (more reps, same weight)
    const currWeight = 50
    const currReps = 12
    const current1RM = computeOneRM(currWeight, currReps) // 69.98kg

    const expectedProgress = ((current1RM - previous1RM) / previous1RM) * 100 // ~5%

    const mockRecommendation: RecommendationModel = {
      Id: 1,
      WarmupsCount: 0,
      Series: 3,
      FirstWorkSetReps: prevReps,
      FirstWorkSetWeight: { Kg: prevWeight, Lb: prevWeight * 2.20462 },
      FirstWorkSet1RM: { Kg: previous1RM, Lb: previous1RM * 2.20462 },
    } as RecommendationModel

    const { result } = renderHook(() =>
      useSetMetrics({
        recommendation: mockRecommendation,
        currentSetIndex: 0,
        isWarmup: false,
        unit: 'kg',
        isFirstWorkSet: true,
        currentReps: currReps,
        currentWeight: currWeight,
      })
    )

    // This will fail with current implementation
    expect(result.current.oneRMProgress).toBeCloseTo(expectedProgress, 2)
  })
})
