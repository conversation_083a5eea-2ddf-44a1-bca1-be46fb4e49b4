import { describe, it, expect } from 'vitest'
import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

describe('API Fixtures', () => {
  const fixturesDir = join(process.cwd(), 'tests', 'fixtures')

  it('should have fixtures directory structure', () => {
    expect(existsSync(join(fixturesDir, 'GET'))).toBe(true)
    expect(existsSync(join(fixturesDir, 'POST'))).toBe(true)
  })

  it('should have required fixture files', () => {
    const requiredFixtures = [
      'POST/login.json',
      'GET/workout.json',
      'GET/workout/exercise/123.json',
      'GET/recommendations/123.json',
    ]

    requiredFixtures.forEach((fixture) => {
      const fixturePath = join(fixturesDir, fixture)
      expect(existsSync(fixturePath)).toBe(true)
    })
  })

  it('should have valid JSON in fixture files', () => {
    const fixtures = [
      'POST/login.json',
      'GET/workout.json',
      'GET/workout/exercise/123.json',
      'GET/recommendations/123.json',
    ]

    fixtures.forEach((fixture) => {
      const fixturePath = join(fixturesDir, fixture)
      const content = JSON.parse(readFileSync(fixturePath, 'utf-8'))
      expect(content).toBeDefined()
      expect(typeof content).toBe('object')
    })
  })
})
