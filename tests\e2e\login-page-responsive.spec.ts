import { test, expect } from '@playwright/test'

test.describe('Login Page - Responsive Subheading', () => {
  test('should display subheading on single line on mobile viewport', async ({
    page,
  }) => {
    // Test on smallest supported viewport (320px)
    await page.setViewportSize({ width: 320, height: 568 })
    await page.goto('/login')

    // Wait for the subheading to be visible
    const subheading = page.getByText("World's Fastest AI Personal Trainer")
    await expect(subheading).toBeVisible()

    // Check that it has the responsive classes
    await expect(subheading).toHaveClass(/text-sm/)
    await expect(subheading).toHaveClass(/whitespace-nowrap/)

    // Verify the text doesn't wrap by checking the element's height
    const boundingBox = await subheading.boundingBox()
    expect(boundingBox).not.toBeNull()

    // A single line of text should be less than 40px tall
    if (boundingBox) {
      expect(boundingBox.height).toBeLessThan(40)
    }
  })

  test.describe('Mobile Viewports', () => {
    const mobileViewports = [
      { width: 320, height: 568, name: 'iPhone SE' },
      { width: 375, height: 667, name: 'iPhone 8' },
      { width: 390, height: 844, name: 'iPhone 12' },
      { width: 414, height: 896, name: 'iPhone 11 Pro Max' },
    ]

    mobileViewports.forEach((viewport) => {
      test(`should maintain single line on ${viewport.name}`, async ({
        page,
      }) => {
        await page.setViewportSize({
          width: viewport.width,
          height: viewport.height,
        })
        await page.goto('/login')

        const subheading = page.getByText("World's Fastest AI Personal Trainer")
        await expect(subheading).toBeVisible()

        // Verify single line by checking height
        const boundingBox = await subheading.boundingBox()
        expect(boundingBox).not.toBeNull()

        if (boundingBox) {
          expect(boundingBox.height).toBeLessThan(
            40,
            `Subheading wraps on ${viewport.name} (${viewport.width}x${viewport.height})`
          )
        }
      })
    })
  })

  test('should scale appropriately on larger viewports', async ({ page }) => {
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.goto('/login')

    const subheading = page.getByText("World's Fastest AI Personal Trainer")
    await expect(subheading).toBeVisible()

    // Should have larger text on tablet
    await expect(subheading).toHaveClass(/sm:text-lg/)

    // Desktop viewport
    await page.setViewportSize({ width: 1280, height: 800 })
    await page.goto('/login')

    // Should have even larger text on desktop
    await expect(subheading).toHaveClass(/lg:text-2xl/)
  })
})
