import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test'
import { mockApi } from '../e2e/helpers/mock-api'

describe('Mock API Helper', () => {
  let browser: Browser
  let context: BrowserContext
  let page: Page

  beforeAll(async () => {
    browser = await chromium.launch({ headless: true })
    context = await browser.newContext()
    page = await context.newPage()
  })

  afterAll(async () => {
    await context.close()
    await browser.close()
  })

  it('should intercept API calls and serve fixture data', async () => {
    // Setup mock API
    await mockApi(page)

    // Navigate to a data URL to avoid external requests
    await page.goto('data:text/html,<html><body>Test</body></html>')

    // Test POST /api/Account/Login
    const loginResponse = await page.request.post('/api/Account/Login', {
      data: { email: '<EMAIL>', password: 'password' },
    })

    expect(loginResponse.status()).toBe(200)
    const loginData = await loginResponse.json()
    expect(loginData.Token).toBe('TEST_TOKEN_12345')
    expect(loginData.User.Email).toBe('<EMAIL>')
  })

  it('should return 404 for missing fixtures', async () => {
    await mockApi(page)
    await page.goto('data:text/html,<html><body>Test</body></html>')

    const response = await page.request.get('/api/nonexistent/endpoint')

    expect(response.status()).toBe(404)
    const errorData = await response.json()
    expect(errorData.error).toBe('Mock not found')
    expect(errorData.fixture).toBe('GET/nonexistent/endpoint.json')
  })

  it('should serve GET fixtures correctly', async () => {
    await mockApi(page)
    await page.goto('data:text/html,<html><body>Test</body></html>')

    const workoutResponse = await page.request.get(
      '/api/Workout/GetUserWorkoutTemplateGroup'
    )

    expect(workoutResponse.status()).toBe(200)
    const workoutData = await workoutResponse.json()
    expect(Array.isArray(workoutData)).toBe(true)
    expect(workoutData[0].Label).toBe('Test Workout A')
  })

  it('should handle nested API paths', async () => {
    await mockApi(page)
    await page.goto('data:text/html,<html><body>Test</body></html>')

    const exerciseResponse = await page.request.get('/api/workout/exercise/123')

    expect(exerciseResponse.status()).toBe(200)
    const exerciseData = await exerciseResponse.json()
    expect(exerciseData.Id).toBe(123)
    expect(exerciseData.Name).toBe('Bench Press')
  })
})
