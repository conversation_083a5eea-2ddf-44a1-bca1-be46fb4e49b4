import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

// Mock APIs helper
async function mockWorkoutAPIs(page: any) {
  // Mock program info
  await page.route('**/api/Workout/GetUserProgramInfo*', async (route: any) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Test Program',
            RemainingToLevelUp: 3,
          },
          NextWorkoutTemplate: {
            Id: 101,
            Label: 'Workout A',
            IsSystemExercise: false,
          },
        },
        TotalWorkoutCompleted: 10,
        ConsecutiveWeeks: 3,
      }),
    })
  })

  // Mock workout template groups with correct exercises
  await page.route(
    '**/api/Workout/GetUserWorkoutTemplateGroup*',
    async (route: any) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Label: 'Test Workout',
            WorkoutTemplates: [
              {
                Id: 101,
                Label: 'Day 1',
                Exercises: [
                  {
                    Id: 3456,
                    Label: 'Bench Press',
                    IsNextExercise: true,
                    IsFinished: false,
                    IsUsingBodyWeightOnly: false,
                  },
                  {
                    Id: 3457,
                    Label: 'Squat',
                    IsNextExercise: false,
                    IsFinished: false,
                    IsUsingBodyWeightOnly: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    }
  )

  // Mock exercise recommendations
  await page.route(
    '**/api/Recommendation/GetRecommendation*',
    async (route: any) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Reps: 10,
          Weight: { Lb: 140, Kg: 63.5 },
          RIR: 2,
          WeightIncrement: 5,
          Series: 3,
        }),
      })
    }
  )
}

test.describe('V2 Exercise Preloading - Race Condition Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport to mobile
    await page.setViewportSize({ width: 390, height: 844 })
  })

  test('should not cause 404 errors when clicking Try new UI button', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)
    await mockWorkoutAPIs(page)

    // Track all API calls to check for 404s
    const apiCalls: { url: string; status: number }[] = []

    page.on('response', (response) => {
      const url = response.url()
      if (url.includes('/api/')) {
        apiCalls.push({ url, status: response.status() })

        // Log 404 errors
        if (response.status() === 404) {
          console.error('404 Error:', url)
        }
      }
    })

    // Mock the API response for exercise sets to prevent 404s
    await page.route('**/api/WorkoutLog/GetExerciseSets/**', (route) => {
      // Extract exercise ID from URL
      const url = route.request().url()
      const match = url.match(/\/GetExerciseSets\/[^/]+\/(\d+)/)
      const exerciseId = match ? parseInt(match[1]) : 0

      // Only return data for valid exercise IDs
      if (exerciseId === 3456 || exerciseId === 3457) {
        route.fulfill({
          status: 200,
          body: JSON.stringify({
            StatusCode: 200,
            Result: [],
          }),
        })
      } else {
        // Return 404 for invalid exercise IDs
        route.fulfill({
          status: 404,
          body: JSON.stringify({
            StatusCode: 404,
            ErrorMessage: 'Exercise not found',
          }),
        })
      }
    })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Wait for exercises to load
    await page.waitForSelector('text=Bench Press', { timeout: 10000 })

    // Click the "Try our new UI" button
    const tryNewUIButton = page.getByText('Start with new exercise view →')
    await tryNewUIButton.waitFor({ state: 'visible' })

    // Ensure button is not disabled
    await expect(tryNewUIButton).not.toBeDisabled()

    // Click the button
    await tryNewUIButton.click()

    // Wait for navigation
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Wait a bit for any additional API calls
    await page.waitForTimeout(1000)

    // Check that no 404 errors occurred for invalid exercise IDs
    const notFoundErrors = apiCalls.filter((call) => call.status === 404)

    // We expect NO 404 errors with the fix
    expect(notFoundErrors).toHaveLength(0)

    if (notFoundErrors.length > 0) {
      console.error('Unexpected 404 errors found:', notFoundErrors)
    }

    // Verify we're on the correct exercise page
    expect(page.url()).toContain('/workout/exercise-v2/3456')

    // Exercise should be loaded properly
    await expect(page.getByText('Bench Press')).toBeVisible()
  })

  test('should handle rapid clicks without causing multiple navigations or 404s', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)
    await mockWorkoutAPIs(page)

    // Track navigation and API calls
    let navigationCount = 0
    const apiCalls: { url: string; status: number }[] = []

    page.on('framenavigated', () => {
      if (page.url().includes('/workout/exercise-v2/')) {
        navigationCount++
      }
    })

    page.on('response', (response) => {
      const url = response.url()
      if (url.includes('/api/')) {
        apiCalls.push({ url, status: response.status() })
      }
    })

    // Mock exercise sets API
    await page.route('**/api/WorkoutLog/GetExerciseSets/**', (route) => {
      route.fulfill({
        status: 200,
        body: JSON.stringify({ StatusCode: 200, Result: [] }),
      })
    })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Wait for exercises to load
    await page.waitForSelector('text=Bench Press', { timeout: 10000 })

    // Find the button
    const tryNewUIButton = page.getByText('Start with new exercise view →')
    await tryNewUIButton.waitFor({ state: 'visible' })

    // Click multiple times rapidly
    await Promise.all([
      tryNewUIButton.click(),
      tryNewUIButton.click(),
      tryNewUIButton.click(),
    ])

    // Wait for navigation and any additional API calls
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })
    await page.waitForTimeout(1000)

    // Should only navigate once
    expect(navigationCount).toBe(1)

    // Check for 404 errors
    const notFoundErrors = apiCalls.filter((call) => call.status === 404)
    expect(notFoundErrors).toHaveLength(0)
  })

  test('should use correct startWorkout function from useWorkoutActions', async ({
    page,
  }) => {
    // Set up authenticated user
    await setupAuthenticatedUser(page)
    await mockWorkoutAPIs(page)

    // Track startWorkout calls
    let startWorkoutCalled = false

    // Mock workout start API to verify it's called
    await page.route('**/api/Workout/StartWorkout*', async (route) => {
      startWorkoutCalled = true
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: { Id: 'workout-123', StartTime: new Date().toISOString() },
        }),
      })
    })

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-container"]', {
      timeout: 10000,
    })

    // Wait for exercises to load
    await page.waitForSelector('text=Bench Press', { timeout: 10000 })

    // Click the "Try our new UI" button
    const tryNewUIButton = page.getByText('Start with new exercise view →')
    await tryNewUIButton.click()

    // Wait for navigation
    await page.waitForURL(/\/workout\/exercise-v2\/\d+/, { timeout: 5000 })

    // Verify startWorkout was called
    expect(startWorkoutCalled).toBe(true)

    // Should navigate to the correct exercise
    expect(page.url()).toContain('/workout/exercise-v2/3456')
  })
})
