import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { DurationPicker } from '../DurationPicker'

// Mock haptics
vi.mock('@/utils/haptics', () => ({
  vibrate: vi.fn(),
}))

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, ...props }: any) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('DurationPicker', () => {
  const mockOnSelect = vi.fn()
  const mockOnClose = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render preset duration options', () => {
    render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    expect(screen.getByTestId('duration-option-30')).toHaveTextContent('0:30')
    expect(screen.getByTestId('duration-option-60')).toHaveTextContent('1:00')
    expect(screen.getByTestId('duration-option-90')).toHaveTextContent('1:30')
    expect(screen.getByTestId('duration-option-120')).toHaveTextContent('2:00')
    expect(screen.getByTestId('duration-option-180')).toHaveTextContent('3:00')
    expect(screen.getByTestId('duration-option-300')).toHaveTextContent('5:00')
  })

  it('should render custom duration button', () => {
    render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const customButton = screen.getByTestId('duration-option-custom')
    expect(customButton).toBeInTheDocument()
    expect(customButton).toHaveTextContent('Custom')
  })

  it('should highlight current duration', () => {
    render(
      <DurationPicker
        currentDuration={120}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const selectedOption = screen.getByTestId('duration-option-120')
    expect(selectedOption).toHaveClass('from-brand-gold-start')
  })

  it('should call onSelect and onClose when preset option is clicked', () => {
    render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    fireEvent.click(screen.getByTestId('duration-option-180'))

    expect(mockOnSelect).toHaveBeenCalledWith(180)
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should open custom duration modal when custom button is clicked', () => {
    render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    fireEvent.click(screen.getByTestId('duration-option-custom'))

    expect(screen.getByTestId('custom-duration-modal')).toBeInTheDocument()
    expect(
      screen.getByLabelText('Enter duration in seconds')
    ).toBeInTheDocument()
  })

  it('should close picker when clicking backdrop', () => {
    const { container } = render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const backdrop = container.querySelector('.fixed.inset-0.bg-black\\/50')
    fireEvent.click(backdrop!)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should have proper touch target sizes', () => {
    render(
      <DurationPicker
        currentDuration={90}
        onSelect={mockOnSelect}
        onClose={mockOnClose}
      />
    )

    const buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).toHaveClass('min-h-[48px]')
    })
  })
})
