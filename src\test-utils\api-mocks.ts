/**
 * Global API mocks for testing
 * Prevents unauthorized API calls during tests
 */

import { vi } from 'vitest'

// Mock API responses
export const mockApiResponses = {
  // Authentication endpoints
  '/api/token': {
    access_token: 'mock-token',
    refresh_token: 'mock-refresh-token',
    expires_in: 3600,
    token_type: 'Bearer',
  },
  '/api/token/refresh': {
    access_token: 'mock-refreshed-token',
    refresh_token: 'mock-new-refresh-token',
    expires_in: 3600,
  },

  // Workout endpoints
  '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo': {
    GetUserProgramInfoResponseModel: {
      RecommendedProgram: {
        Id: 4330,
        Label: 'Test Program',
        RemainingToLevelUp: 0,
      },
      NextWorkoutTemplate: {
        Id: 27792,
        Label: 'Test Workout',
        IsSystemExercise: false,
        Exercises: null,
      },
    },
  },
  '/api/Workout/GetUserCustomizedCurrentWorkout': {
    Id: 12345,
    Label: 'Test Workout',
    Exercises: [
      {
        Id: 1001,
        Label: 'Bench Press',
        BodyPartId: 2,
        EquipmentId: 1,
        IsBodyweight: false,
        VideoUrl: 'https://example.com/video',
        RecoModel: null,
      },
    ],
  },
  '/api/WorkoutLog/GetLogAverageWithSetsV2': {
    weekStreak: 7,
    workoutsCompleted: 79,
    lbsLifted: 848978,
  },

  // Exercise endpoints
  '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew': {
    Id: 1,
    ExerciseId: 1001,
    Weight: 135,
    Reps: 10,
    Sets: 3,
    RestTime: 180,
  },
  '/api/Exercise/GetRecommendationRestPauseForExerciseWithoutWarmupsNew': {
    Id: 2,
    ExerciseId: 1002,
    Weight: 100,
    Reps: 8,
    Sets: 2,
    RestTime: 120,
  },

  // Save endpoints
  '/api/WorkoutLog/SaveSetLog': {
    success: true,
    id: 'mock-set-id',
  },
}

/**
 * Setup global API mocks for fetch
 */
export function setupGlobalApiMocks() {
  // Mock fetch globally
  global.fetch = vi.fn((input: RequestInfo | URL) => {
    let urlString: string
    if (typeof input === 'string') {
      urlString = input
    } else if (input instanceof URL) {
      urlString = input.toString()
    } else {
      urlString = input.url
    }

    // Extract endpoint from URL
    const endpoint = urlString.replace(/^https?:\/\/[^/]+/, '')

    // Check if we have a mock response for this endpoint
    const mockResponse = Object.entries(mockApiResponses).find(([key]) =>
      endpoint.includes(key)
    )?.[1]

    if (mockResponse) {
      return Promise.resolve({
        ok: true,
        status: 200,
        statusText: 'OK',
        json: () => Promise.resolve(mockResponse),
        text: () => Promise.resolve(JSON.stringify(mockResponse)),
        headers: new Headers({
          'content-type': 'application/json',
        }),
      } as Response)
    }

    // For unmocked endpoints, return 404 to prevent real API calls
    console.warn(`Unmocked API call to: ${urlString}`)
    return Promise.resolve({
      ok: false,
      status: 404,
      statusText: 'Not Found',
      json: () => Promise.resolve({ error: 'Endpoint not mocked' }),
      text: () => Promise.resolve('Endpoint not mocked'),
      headers: new Headers(),
    } as Response)
  })
}

/**
 * Mock specific API endpoint
 */
export function mockApiEndpoint(
  endpoint: string,
  response: unknown,
  status = 200
) {
  const originalFetch = global.fetch

  global.fetch = vi.fn((input: RequestInfo | URL) => {
    let urlString: string
    if (typeof input === 'string') {
      urlString = input
    } else if (input instanceof URL) {
      urlString = input.toString()
    } else {
      urlString = input.url
    }

    if (urlString.includes(endpoint)) {
      return Promise.resolve({
        ok: status >= 200 && status < 300,
        status,
        statusText: status === 200 ? 'OK' : 'Error',
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response)),
        headers: new Headers({
          'content-type': 'application/json',
        }),
      } as Response)
    }

    // Fall back to original fetch or global mock
    return originalFetch
      ? originalFetch(input)
      : Promise.reject(new Error('No mock found'))
  })
}

/**
 * Mock 401 Unauthorized response for testing auth flows
 */
export function mockUnauthorizedResponse(endpoint: string) {
  mockApiEndpoint(endpoint, { error: 'Unauthorized' }, 401)
}

/**
 * Reset all API mocks
 */
export function resetApiMocks() {
  vi.restoreAllMocks()
}
