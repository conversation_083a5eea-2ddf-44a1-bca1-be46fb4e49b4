import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useLoginPrefetch } from '../useLoginPrefetch'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'
import * as workoutApi from '@/api/workouts'
import * as apiWorkout from '@/services/api/workout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('@/api/workouts')
vi.mock('@/services/api/workout')
vi.mock('@/stores/workoutStore')
vi.mock('@/hooks/useUserStats', () => ({
  usePrefetchUserStats: () => ({ prefetch: vi.fn() }),
}))

const mockLoadAllExerciseRecommendations = vi.fn().mockResolvedValue(undefined)
const mockSetWorkout = vi.fn()

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
    setWorkout: mockSetWorkout,
    currentWorkout: null,
    workoutSession: null,
  })),
}))

describe('useLoginPrefetch with RecommendationLoadingCoordinator', () => {
  let queryClient: QueryClient
  let coordinator: RecommendationLoadingCoordinator

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    coordinator = RecommendationLoadingCoordinator.getInstance()
    coordinator.reset()

    vi.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should skip duplicate loads when recommendations already loading', async () => {
    // GIVEN recommendations already loading for exercises
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' },
      { Id: 2, Label: 'Squat' },
    ]

    // Mark exercises as already loading
    coordinator.startLoading(1)
    coordinator.startLoading(2)

    // Mock API responses
    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: mockExercises,
      },
    ] as any)

    // WHEN prefetch starts
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // THEN skips duplicate loads
    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
  })

  it('should initiate loading when no active loads', async () => {
    // GIVEN no active loads
    expect(coordinator.isAnyLoading([1, 2])).toBe(false)

    // Mock API responses
    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
        ],
      },
    ] as any)

    // WHEN prefetch starts
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // THEN initiates loading
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalled()
  })

  it('should report accurate progress considering existing loads', async () => {
    // GIVEN partial loads already complete
    coordinator.startLoading(1)
    coordinator.completeLoading(1)

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
          { Id: 3, Label: 'Deadlift' },
        ],
      },
    ] as any)

    // WHEN checked
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // THEN reports accurate progress
    expect(result.current.progress).toBeGreaterThan(0)
    expect(result.current.isComplete).toBe(true)
  })

  it('should handle coordinator integration errors gracefully', async () => {
    // GIVEN coordinator throws error
    const mockError = new Error('Coordinator error')
    vi.spyOn(coordinator, 'canStartLoading').mockImplementation(() => {
      throw mockError
    })

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    // WHEN prefetch runs
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // THEN handles error gracefully and continues
    expect(result.current.error).toBe(null) // Prefetch doesn't surface errors
    expect(result.current.isComplete).toBe(true)
  })

  it('should coordinate with workoutStore loading states', async () => {
    // Mock workout store with loading states
    const mockLoadingStates = new Map([
      [1, true],
      [2, false],
    ])

    vi.mocked(useWorkoutStore as any).mockReturnValue({
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      setWorkout: mockSetWorkout,
      currentWorkout: null,
      workoutSession: null,
      loadingStates: mockLoadingStates,
    })

    vi.mocked(apiWorkout.getUserWorkoutProgramInfo).mockResolvedValue({
      GetUserProgramInfoResponseModel: {
        NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
      },
    } as any)

    vi.mocked(workoutApi.workoutApi.getUserWorkout).mockResolvedValue([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Squat' },
        ],
      },
    ] as any)

    // WHEN prefetch runs
    const { result } = renderHook(() => useLoginPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // THEN respects existing loading states
    // Should still call loadAll but coordinator will filter
    expect(mockLoadAllExerciseRecommendations).toHaveBeenCalled()
  })
})
