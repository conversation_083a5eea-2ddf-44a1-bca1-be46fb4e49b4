import { render, screen } from '@testing-library/react'
import { ExerciseInfoHeader } from '../ExerciseInfoHeader'
import { CurrentSetCard } from '../CurrentSetCard'
import { TodaysSetsPreview } from '../TodaysSetsPreview'
import { SetMetricsDisplay } from '../SetMetricsDisplay'
import { RepsInput } from '@/components/workout/inputs/RepsInput'
import { WeightInput } from '@/components/workout/inputs/WeightInput'
import { vi } from 'vitest'
import type { WorkoutLogSerieModel, RecommendationModel } from '@/types'

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: 'div',
  },
}))

// Mock hooks
vi.mock('@/hooks/useSwipeAnimation', () => ({
  useSwipeAnimation: () => ({
    controls: {},
    isDragging: false,
    handleDragEnd: vi.fn(),
    handleDragStart: vi.fn(),
    triggerFadeAnimation: vi.fn(),
  }),
}))

vi.mock('@/hooks/useSetMetrics', () => ({
  useSetMetrics: () => ({
    lastTimeReps: 12,
    lastTimeWeight: 60,
    oneRMProgress: 5.5,
  }),
}))

describe('Text Size Increase Tests', () => {
  describe('ExerciseInfoHeader', () => {
    it('should display "Set X of Y" text with text-lg class', () => {
      render(
        <ExerciseInfoHeader currentSet={1} totalSets={5} completedSets={0} />
      )

      const setCounterText = screen.getByText('Set 1 of 5')
      expect(setCounterText).toHaveClass('text-lg')
      expect(setCounterText).not.toHaveClass('text-sm')
    })
  })

  describe('CurrentSetCard', () => {
    const mockExercise = {
      Id: 1,
      Label: 'Bench Press',
      IsFinished: false,
    }

    const mockCurrentSet: WorkoutLogSerieModel = {
      Id: 1,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      IsWarmups: true,
      IsNext: true,
    } as WorkoutLogSerieModel

    const mockSetData = { reps: 10, weight: 50, duration: 0 }

    it('should display swipe hints with text-lg class', () => {
      render(
        <CurrentSetCard
          exercise={mockExercise as any}
          currentSet={mockCurrentSet}
          setData={mockSetData}
          onSetDataChange={vi.fn()}
          onComplete={vi.fn()}
          onSkip={vi.fn()}
          isSaving={false}
          completedSets={0}
          unit="kg"
        />
      )

      const swipeHint = screen.getByText(
        'Swipe left to skip · right to complete'
      )
      expect(swipeHint).toHaveClass('text-lg')
      expect(swipeHint).not.toHaveClass('text-sm')
    })
  })

  describe('RepsInput', () => {
    it('should display "REPS" label with text-lg class', () => {
      render(
        <RepsInput
          reps={10}
          onChange={vi.fn()}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
        />
      )

      const repsLabel = screen.getByText('REPS')
      expect(repsLabel).toHaveClass('text-lg')
      expect(repsLabel).not.toHaveClass('text-sm')
    })
  })

  describe('WeightInput', () => {
    it('should display unit label with text-lg class', () => {
      render(
        <WeightInput
          weight={50}
          unit="kg"
          onChange={vi.fn()}
          onIncrement={vi.fn()}
          onDecrement={vi.fn()}
        />
      )

      const unitLabel = screen.getByText('KG')
      expect(unitLabel).toHaveClass('text-lg')
      expect(unitLabel).not.toHaveClass('text-sm')
    })
  })

  describe('TodaysSetsPreview', () => {
    const mockSets = [
      {
        Id: 1,
        IsWarmups: true,
        WarmUpReps: 8,
        WarmUpWeightSet: { Kg: 20, Lb: 44 },
        Reps: 0,
        Weight: { Kg: 0, Lb: 0 },
      } as any,
      {
        Id: 2,
        IsWarmups: false,
        Reps: 10,
        Weight: { Kg: 50, Lb: 110 },
      } as any,
    ]

    it('should display "Today\'s sets" header with text-lg class', () => {
      render(<TodaysSetsPreview allSets={mockSets} unit="kg" />)

      const header = screen.getByText("Today's sets")
      expect(header).toHaveClass('text-lg')
      expect(header).not.toHaveClass('text-sm')
    })

    it('should display set details with text-lg class', () => {
      render(<TodaysSetsPreview allSets={mockSets} unit="kg" />)

      const repsText = screen.getByText('8 reps')
      const repsParent = repsText.parentElement
      expect(repsParent).toHaveClass('text-lg')
      expect(repsParent).not.toHaveClass('text-sm')

      const weightText = screen.getByText('20 kg')
      const weightParent = weightText.parentElement
      expect(weightParent).toHaveClass('text-lg')
      expect(weightParent).not.toHaveClass('text-sm')
    })
  })

  describe('SetMetricsDisplay', () => {
    const mockRecommendation: RecommendationModel = {
      AllLogs: [
        {
          WorkoutLogSerieModel: {
            Reps: 12,
            Weight: { Kg: 60, Lb: 132 },
          } as any,
        },
      ],
      RM1Progress: 5.5,
      Increments: { Kg: 1, Lb: 2.5 },
    } as any

    it('should display "Last workout best" text with text-lg class', () => {
      render(
        <SetMetricsDisplay
          recommendation={mockRecommendation}
          currentSetIndex={0}
          isWarmup={false}
          isFirstWorkSet
          unit="kg"
          currentReps={10}
          currentWeight={50}
        />
      )

      const lastTimeText = screen.getByText(/Last workout best/)
      expect(lastTimeText).toHaveClass('text-lg')
      expect(lastTimeText).not.toHaveClass('text-sm')
    })

    it('should display "Today" progress text with text-lg class', () => {
      render(
        <SetMetricsDisplay
          recommendation={mockRecommendation}
          currentSetIndex={0}
          isWarmup={false}
          isFirstWorkSet
          unit="kg"
          currentReps={10}
          currentWeight={50}
        />
      )

      const todayText = screen.getByText(/Today:/)
      expect(todayText).toHaveClass('text-lg')
      expect(todayText).not.toHaveClass('text-sm')
    })
  })
})
