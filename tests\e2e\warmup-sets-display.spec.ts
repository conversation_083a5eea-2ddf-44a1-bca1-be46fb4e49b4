import { test, expect } from '@playwright/test'

test.describe('Warmup Sets Display', () => {
  test('warmup sets should display correct weight and reps values', async ({
    page,
  }) => {
    // Navigate to the test page
    await page.goto('/test/exercise-sets-grid')

    // Wait for the component to render
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Get all weight and reps inputs
    const weightInputs = await page.$$('input[aria-label*="Weight"]')
    const repsInputs = await page.$$('input[aria-label*="Reps"]')

    // Check warmup sets (first two sets)
    // First warmup set - should show ~50% weight and ~75% reps
    const firstWarmupWeight = await weightInputs[0].inputValue()
    const firstWarmupReps = await repsInputs[0].inputValue()
    expect(firstWarmupWeight).toBe('13.6 kg')
    expect(firstWarmupReps).toBe('9')

    // Second warmup set - should show ~75% weight and ~50% reps
    const secondWarmupWeight = await weightInputs[1].inputValue()
    const secondWarmupReps = await repsInputs[1].inputValue()
    expect(secondWarmupWeight).toBe('20.4 kg')
    expect(secondWarmupReps).toBe('6')

    // First work set - should show full weight and reps
    const firstWorkWeight = await weightInputs[2].inputValue()
    const firstWorkReps = await repsInputs[2].inputValue()
    expect(firstWorkWeight).toBe('27.2 kg')
    expect(firstWorkReps).toBe('12')

    // Verify warmup sets are NOT showing work set values
    expect(firstWarmupWeight).not.toBe('27.2 kg')
    expect(firstWarmupReps).not.toBe('12')
  })

  test('warmup sets should maintain correct values after interaction', async ({
    page,
  }) => {
    await page.goto('/test/exercise-sets-grid')
    await page.waitForSelector('[data-testid="exercise-sets-grid"]')

    // Get the first warmup set inputs
    const firstWarmupWeightInput = await page.$(
      'input[aria-label="Weight for set W"]'
    )
    const firstWarmupRepsInput = await page.$(
      'input[aria-label="Reps for set W"]'
    )

    // Verify initial values
    expect(await firstWarmupWeightInput.inputValue()).toBe('13.6 kg')
    expect(await firstWarmupRepsInput.inputValue()).toBe('9')

    // Click on the reps input to ensure it's focused
    await firstWarmupRepsInput.click()

    // Verify values are still correct after interaction
    expect(await firstWarmupWeightInput.inputValue()).toBe('13.6 kg')
    expect(await firstWarmupRepsInput.inputValue()).toBe('9')
  })
})
