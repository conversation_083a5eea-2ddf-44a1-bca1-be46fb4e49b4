# CI Optimization & Mocked E2E Blueprint

## 1. High-Level Blueprint

Goals  
• Stabilise Playwright E2E suite by mocking backend API.  
• Cut **e2e-full** job runtime to ≤ 30 min while keeping Safari coverage in **e2e-critical**.  
• Maintain deterministic, test-driven development workflow with incremental, reviewable PRs.

Major deliverables

1. Mock API layer (`mock-api.ts` + JSON fixtures).
2. Playwright global setup switchable via `USE_API_MOCK`.
3. Four seed fixtures (login, workout, exercise, recommendations).
4. Workflow edits  
   • `USE_API_MOCK=1` in **e2e-full** job.  
   • Remove WebKit from full-suite config when `CI_FULL_SUITE=1`.  
   • Drop `max-parallel: 1` so shards run concurrently.
5. Documentation & test coverage for new infra.

## 2. Iterative Chunk Breakdown

### Phase 1 – Foundation

- Task 1.1 Create fixtures directory & seed JSON.
- Task 1.2 Implement `mock-api.ts` helper.
- Task 1.3 Wire helper in Playwright `global-setup.ts` behind env flag.
- Task 1.4 Add failing unit tests that assert route interception works.

### Phase 2 – Workflow Wiring

- Task 2.1 Add `env: USE_API_MOCK: '1'` to **e2e-full** job.
- Task 2.2 Inject `CI_FULL_SUITE=1` and remove WebKit projects in config gate.
- Task 2.3 Delete `max-parallel: 1` from matrix strategy.

### Phase 3 – Incremental Expansion

- Task 3.1 Run suite in CI, capture missing-fixture errors, auto-generate stubs.
- Task 3.2 Add seed fixtures for newly surfaced endpoints.
- Task 3.3 Add Playwright smoke test ensuring all `**/api/**` calls are satisfied by mock.

### Phase 4 – Performance Polishing

- Task 4.1 Measure runtime; if > 30 min, profile slow specs.
- Task 4.2 Optional: increase shard count or tweak test timeouts.
- Task 4.3 Add README section on toggling mocks locally.

## 3. Fine-Grained Step List

1. **Create fixture skeleton**  
   a. `tests/fixtures/GET/` & `POST/` dirs.  
   b. Add four JSON files with minimal valid payloads.

2. **Implement `mock-api.ts`**  
   a. Export `async function mockApi(page)` registering `page.route('**/api/**', handler)`.  
   b. Handler builds key `<METHOD>/<PATH>.json`, serves 200 or 404.

3. **Update `global-setup.ts`**  
   a. Check `process.env.USE_API_MOCK`.  
   b. For each context’s page call `mockApi(page)`.

4. **Unit tests for helper** (`vitest`)  
   a. Spawn `chromium.launch()`, call helper, assert intercepted responses.

5. **Workflow edits**  
   a. Insert job-level `env` in **e2e-full**.  
   b. Add `CI_FULL_SUITE=1`.  
   c. Remove WebKit projects conditionally in Playwright config.  
   d. Delete `max-parallel` line.

6. **Smoke fixture test**  
   a. New Playwright spec hits `/api/login` etc.  
   b. Assert 200 & body shape.

7. **Iterate fixtures**  
   a. On CI failure, parse missing mock path, add stub file, commit.

8. **Performance review**  
   a. Record job duration in README.  
   b. Decide on more shards or prune flaky specs.

## 4. LLM Prompt Series

```text
Prompt 1 – Create Fixture Skeleton
You are adding static API fixtures to a TypeScript Playwright repo.

1. Add directory `tests/fixtures/{GET,POST}`.
2. Add four JSON files:
   • `POST/login.json` – `{ "token": "TEST_TOKEN" }`
   • `GET/workout.json` – minimal workout object
   • `GET/workout/exercise/123.json` – minimal exercise object
   • `GET/recommendations/123.json` – `{ "weight": 100 }`

Write code to create these directories & files and add them to git.
Include a small `vitest` test that verifies the files exist.

Return only modified files.
```

```text
Prompt 2 – Implement mock-api.ts
Create `tests/e2e/helpers/mock-api.ts`:

• Export async `mockApi(page)` registering `page.route('**/api/**', async (route, request) => { ... })`.
• Determine fixture key from `request.method()` & `new URL(request.url()).pathname`.
• Read JSON file, default 404 `{ error: 'Mock not found' }`.

Also write `tests/unit/mock-api.test.ts` using Playwright’s request API in headless mode to assert a fixture is returned.

Return new files.
```

```text
Prompt 3 – Integrate in global-setup
Edit `tests/e2e/global-setup.ts`:

• Import `mockApi`.
• If `process.env.USE_API_MOCK === '1'`, for each new page call helper.

Add vitest to ensure env flag toggles behaviour.

Return diff.
```

```text
Prompt 4 – Workflow Update
Edit `.github/workflows/ci-optimized.yml`:

• In `e2e-full` job add:
  env:
    USE_API_MOCK: '1'
    CI_FULL_SUITE: '1'
• Remove `max-parallel: 1` line.
Commit workflow change with descriptive message.

Return updated YAML section only.
```

```text
Prompt 5 – Playwright Config Gate
Edit `playwright.ci.optimized.config.ts`:

• If `process.env.CI_FULL_SUITE`, filter projects to those with `name.includes('Chromium')`.

Add unit test verifying config exports only Chromium projects when flag set.

Return diff.
```

```text
Prompt 6 – Smoke Test
Create `tests/e2e/mock-coverage.spec.ts`:

• List of critical endpoints.
• For each, do `await page.request.get(baseUrl + path)` and expect 200.

Ensure spec runs with tag `@mock`.

Return new spec.
```

```text
Prompt 7 – CI Runtime Review Script
Add `scripts/print-e2e-runtime.js`:

• Reads Playwright JSON report, prints total duration.
• Fails if > 30 min when `CI_FULL_SUITE` is set.

Hook script in `e2e-full` job after tests.

Return script + workflow snippet.
```

Each prompt builds on the previous, adds tests, and keeps code integrated—no orphan files.
