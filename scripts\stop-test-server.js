#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const PID_FILE = path.join(__dirname, '..', '.test-server.pid');

function stopServer() {
  try {
    if (fs.existsSync(PID_FILE)) {
      const pid = parseInt(fs.readFileSync(PID_FILE, 'utf8').trim());
      process.kill(pid, 'SIGTERM');
      fs.unlinkSync(PID_FILE);
      console.log('✅ Dev server stopped');
    } else {
      console.log('ℹ️  No dev server to stop');
    }
  } catch (e) {
    console.error('Error stopping server:', e.message);
  }
}

stopServer();