import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetMetricsDisplay } from '../SetMetricsDisplay'
import type { RecommendationModel } from '@/types'

describe('SetMetricsDisplay - Weight Formatting', () => {
  it('should format last workout weight with floating point errors', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 0,
      HistorySet: [
        {
          Reps: 10,
          Weight: { Lb: 132.00000000000003, Kg: 59.874579 },
          IsWarmups: false,
        },
      ],
      FirstWorkSetReps: 10,
      FirstWorkSetWeight: { Lb: 132.00000000000003, Kg: 59.874579 },
    } as RecommendationModel

    render(
      <SetMetricsDisplay
        recommendation={recommendation}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet
        unit="lbs"
        currentReps={10}
        currentWeight={135}
      />
    )

    // Should show "Last workout best: 10 × 132 lbs" not "10 × 132.00000000000003 lbs"
    expect(
      screen.getByText(/Last workout best: 10 × 132 lbs/)
    ).toBeInTheDocument()

    // Should not show the floating point error
    expect(screen.queryByText(/132\.00000000000003/)).not.toBeInTheDocument()
  })

  it('should format kg weights correctly', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 0,
      HistorySet: [
        {
          Reps: 8,
          Weight: { Lb: 110.23128, Kg: 49.999999999999 },
          IsWarmups: false,
        },
      ],
      FirstWorkSetReps: 8,
      FirstWorkSetWeight: { Lb: 110.23128, Kg: 49.999999999999 },
    } as RecommendationModel

    render(
      <SetMetricsDisplay
        recommendation={recommendation}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet
        unit="kg"
        currentReps={10}
        currentWeight={52.5}
      />
    )

    // Should show "Last workout best: 8 × 50 kg" with proper formatting
    expect(screen.getByText(/Last workout best: 8 × 50 kg/)).toBeInTheDocument()

    // Should not show excessive decimals
    expect(screen.queryByText(/49\.999999999999/)).not.toBeInTheDocument()
  })

  it('should not render when no metrics are available', () => {
    const recommendation: RecommendationModel = {
      ExerciseId: 1001,
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.2 },
      WarmupsCount: 0,
      HistorySet: [],
    } as RecommendationModel

    const { container } = render(
      <SetMetricsDisplay
        recommendation={recommendation}
        currentSetIndex={0}
        isWarmup={false}
        isFirstWorkSet
        unit="lbs"
        currentReps={10}
        currentWeight={135}
      />
    )

    // Should not render anything when no last time data
    expect(container.firstChild).toBeNull()
  })
})
