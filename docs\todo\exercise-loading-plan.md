# Exercise Loading Improvement Plan

## Detailed Blueprint

Based on the analysis of the Dr. Muscle app's current loading issues, particularly flaky loading, infinite skeletons, and authentication expiry after 1-2 hours, the blueprint focuses on enhancing background loading, automatic token refresh, and seamless resuming for sessions up to 12 hours. Key goals:

- Initiate prefetching of workout data post-login using service workers for caching.
- Implement automatic token refresh in API client on 401 errors.
- Enhance error handling to prevent stuck states, with auto-login fallback.
- Ensure visible loading indicators and indefinite sessions unless explicit logout.

The implementation will follow TDD, incremental changes, and integration at each step, aligning with Next.js, Zustand, and existing patterns.

## Iterative Chunks

### Chunk 1: Enhance Authentication Token Refresh

- Step 1.1: Update API client to attempt token refresh on 401 errors.
- Step 1.2: Add retry logic for failed requests post-refresh.

### Chunk 2: Background Prefetching Post-Login

- Step 2.1: Implement prefetch hook in login flow.
- Step 2.2: Integrate service worker caching for workout data.

### Chunk 3: Improve Error Handling in Initialization

- Step 3.1: Add timeout and retry in recommendation coordinator.
- Step 3.2: Implement auto-login fallback on persistent failures.

### Chunk 4: Seamless Resuming and Loading Indicators

- Step 4.1: Persist workout state for extended backgrounding.
- Step 4.2: Ensure visible, non-infinite loading states.

### Chunk 5: Integration and Testing

- Step 5.1: Wire all components together.
- Step 5.2: End-to-end testing for full flow.

## Refined Small Steps

After review, steps are sized for safe implementation with testing: each involves 1-2 files, TDD, and verification.

## LLM Prompts

Each prompt is for a code-generation LLM to implement in a test-driven manner.

```text
Prompt 1: Using TDD, update src/api/client.ts to handle 401 errors by calling refreshToken from auth.ts, retry the original request, and fallback to logout if refresh fails. Write failing tests first in src/api/__tests__/client.test.ts, then implement minimally.
```

```text
Prompt 2: Building on Prompt 1, add unit tests for retry logic in client.ts, ensure it handles multiple 401s without infinite loops, then implement the retry mechanism.
```

```text
Prompt 3: Implement a prefetch hook in src/hooks/useLoginPrefetch.ts to fetch workout data post-login. Start with failing integration tests in src/hooks/__tests__/useLoginPrefetch.test.tsx, then code the hook and integrate it into the login component.
```

```text
Prompt 4: Extend the service worker in next.config.js to cache prefetch data. Write tests for caching behavior, then update the configuration and verify integration with the prefetch hook from Prompt 3.
```

```text
Prompt 5: In src/hooks/useExercisePageInitialization.ts, add timeout and retry for recommendation loading. Create failing tests in useExercisePageInitialization.coordinator.test.tsx, implement, and ensure it prevents infinite skeletons.
```

```text
Prompt 6: Add auto-login fallback in useExercisePageInitialization.ts for persistent failures. Test with mocked auth expiry, implement, and connect to token refresh from Prompt 1.
```

```text
Prompt 7: Enhance workoutStore/index.ts to persist state for resuming after 12 hours. Write persistence tests, implement using Zustand persist middleware, and integrate with initialization hook.
```

```text
Prompt 8: Update UI components like ExercisePageClient.tsx to show progressive loading indicators. Add component tests for loading states, implement, and ensure no blank screens.
```

```text
Prompt 9: Wire all changes together: Update app/layout.tsx or relevant entry points to use the new hooks and stores. Add E2E tests in tests/e2e/exercise-loading.spec.ts to verify the full flow, including background resume and error handling.
```

This plan ensures incremental progress, with each step building on the previous, culminating in a fully integrated solution.
