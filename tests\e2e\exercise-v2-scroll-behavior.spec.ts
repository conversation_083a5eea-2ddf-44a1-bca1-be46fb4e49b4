import { test, expect } from '@playwright/test'

test.describe('Exercise V2 Page Scroll Behavior', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication to bypass login
    await page.addInitScript(() => {
      const authState = {
        state: {
          user: {
            email: '<EMAIL>',
            FirstName: 'Test',
            LastName: 'User',
          },
          isAuthenticated: true,
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token',
          cachedUserInfo: { MassUnit: 'kg' },
          cacheVersion: 1,
        },
        version: 0,
      }
      window.localStorage.setItem('drmuscle-auth', JSON.stringify(authState))
    })

    // Mock API responses
    await page.route('**/api/Account/GetUserInfo*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
          MassUnit: 'kg',
        }),
      })
    })

    await page.route(
      '**/api/workout/GetUserWorkoutTemplateGroup*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Id: 1,
            Label: 'Test Workout',
            ListOfExercises: [
              {
                Id: 1,
                Label: 'Bench Press',
                IsFinished: false,
              },
            ],
          }),
        })
      }
    )

    await page.route(
      '**/api/workout/GetRecommendationMobile*',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Result: {
              Id: 1,
              ExerciseId: 1,
              ExerciseName: 'Bench Press',
              WarmupSets: [],
              RecommendedSets: ['5 x 40 kg'],
              TotalSets: 1,
              RepsRecommended: 5,
              WeightRecommended: { Kg: 40, Lb: 88 },
              NbSets: 1,
              NbWarmupSets: 0,
            },
          }),
        })
      }
    )

    // Default mock for other API calls
    await page.route('**/api/**', async (route) => {
      await route.fulfill({ status: 200, body: JSON.stringify({}) })
    })
  })

  test('should use min-h-screen instead of h-screen for flexible height', async ({
    page,
  }) => {
    // Navigate directly to exercise V2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]', {
      timeout: 10000,
    })

    // Check the page structure
    const container = page.locator('[data-testid="exercise-page-container"]')

    // Container should use min-h-screen for flexible height
    await expect(container).toHaveClass(/min-h-screen/)

    // Should NOT have fixed h-screen class
    const containerClasses = await container.getAttribute('class')
    expect(containerClasses).not.toContain('h-screen')
  })

  test('should not have nested scroll containers', async ({ page }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]', {
      timeout: 10000,
    })

    // Should have NO nested scroll containers
    const scrollContainers = await page
      .locator('.overflow-y-auto, .overflow-auto, .overflow-scroll')
      .count()
    expect(scrollContainers).toBe(0)
  })

  test('should allow scrolling through document body', async ({ page }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]', {
      timeout: 10000,
    })

    // Check that the page allows natural document flow
    const pageHeight = await page.evaluate(() => document.body.scrollHeight)
    const viewportHeight = await page.evaluate(() => window.innerHeight)

    // The page should be scrollable or fit within viewport
    const isScrollable = pageHeight > viewportHeight
    const fitsInViewport = pageHeight <= viewportHeight

    expect(isScrollable || fitsInViewport).toBe(true)

    // If scrollable, verify we can scroll
    if (isScrollable) {
      await page.evaluate(() => window.scrollTo(0, 100))
      const scrollY = await page.evaluate(() => window.scrollY)
      expect(scrollY).toBeGreaterThan(0)

      // Reset scroll
      await page.evaluate(() => window.scrollTo(0, 0))
    }
  })

  test('should maintain scroll behavior with bottom padding', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')
    await page.waitForSelector('[data-testid="exercise-page-container"]', {
      timeout: 10000,
    })

    // Check main content area exists
    const mainContent = page.locator('.flex-1.flex.flex-col.px-4')
    await expect(mainContent).toBeVisible()

    // The main content should not have overflow-y-auto
    const mainContentClasses = await mainContent.getAttribute('class')
    expect(mainContentClasses).not.toContain('overflow-y-auto')
  })
})
