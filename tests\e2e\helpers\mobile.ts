import { Page, expect } from '@playwright/test'

/**
 * Mobile testing helpers for E2E tests
 * Provides utilities for mobile viewport testing and user setup
 */

/**
 * Ensures the page is using a mobile viewport
 */
export async function expectMobileViewport(page: Page) {
  const viewport = page.viewportSize()
  expect(viewport?.width).toBeLessThanOrEqual(768) // Mobile breakpoint
  expect(viewport?.height).toBeGreaterThan(0)
}

/**
 * Sets up an authenticated user session for mobile testing
 */
export async function setupAuthenticatedUser(
  page: Page,
  options: {
    email?: string
    skipOnboarding?: boolean
  } = {}
) {
  const { email = '<EMAIL>', skipOnboarding = true } = options

  // Navigate to login page
  await page.goto('/login')

  // Wait for page to load
  await page.waitForLoadState('networkidle')

  // Fill login form
  const emailInput = page.locator('input[type="email"], input[name="email"]')
  const passwordInput = page.locator(
    'input[type="password"], input[name="password"]'
  )
  const loginButton = page.locator(
    'button[type="submit"], button:has-text("Sign In"), button:has-text("Login")'
  )

  await emailInput.waitFor({ timeout: 10000 })
  await emailInput.fill(email)
  await passwordInput.fill('Dr123456')
  await loginButton.click()

  // Wait for successful login (redirect to program or workout page)
  await page.waitForURL(/\/(program|workout)/, { timeout: 15000 })

  // Skip onboarding if requested
  if (skipOnboarding) {
    try {
      const skipButton = page.locator(
        'button:has-text("Skip"), button:has-text("Continue"), [data-testid="skip-onboarding"]'
      )
      await skipButton.waitFor({ timeout: 3000 })
      await skipButton.click()
    } catch {
      // Onboarding might not be present, continue
    }
  }

  // Ensure we're on a valid authenticated page
  await expect(page).toHaveURL(/\/(program|workout)/)
}

/**
 * Configures mobile-specific settings for the page
 */
export async function configureMobileSettings(page: Page) {
  // Set mobile viewport if not already set
  await page.setViewportSize({ width: 393, height: 851 }) // iPhone 12 Pro size

  // Enable touch events
  await page.addInitScript(() => {
    // Add touch event support
    Object.defineProperty(navigator, 'maxTouchPoints', {
      writable: false,
      value: 5,
    })
  })

  // Set mobile user agent
  await page.setExtraHTTPHeaders({
    'User-Agent':
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  })
}

/**
 * Waits for mobile-specific loading states
 */
export async function waitForMobilePageLoad(page: Page) {
  // Wait for network to be idle
  await page.waitForLoadState('networkidle')

  // Wait for any mobile-specific loading indicators to disappear
  try {
    await page.waitForSelector('[data-testid="loading"], .loading, .spinner', {
      state: 'hidden',
      timeout: 10000,
    })
  } catch {
    // Loading indicators might not be present
  }

  // Ensure main content is visible
  await page.waitForSelector('main, [role="main"], .main-content', {
    timeout: 15000,
  })
}

/**
 * Handles mobile-specific navigation
 */
export async function navigateOnMobile(page: Page, path: string) {
  await page.goto(path)
  await waitForMobilePageLoad(page)
  await expectMobileViewport(page)
}

/**
 * Simulates mobile touch interactions
 */
export async function mobileTouch(page: Page, selector: string) {
  const element = page.locator(selector)
  await element.waitFor({ timeout: 5000 })

  // Use tap instead of click for mobile
  await element.tap()
}

/**
 * Handles mobile keyboard interactions
 */
export async function mobileType(page: Page, selector: string, text: string) {
  const element = page.locator(selector)
  await element.waitFor({ timeout: 5000 })

  // Focus and type
  await element.focus()
  await element.fill(text)

  // Hide mobile keyboard
  await page.keyboard.press('Enter')
}

/**
 * Checks if the page is responsive on mobile
 */
export async function checkMobileResponsiveness(page: Page) {
  const viewport = page.viewportSize()
  expect(viewport?.width).toBeLessThanOrEqual(768)

  // Check that content is not horizontally scrollable
  const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
  const viewportWidth = viewport?.width || 0

  expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 10) // Allow small margin
}

/**
 * Default mobile test setup
 */
export async function setupMobileTest(
  page: Page,
  options: {
    authenticate?: boolean
    email?: string
    skipOnboarding?: boolean
  } = {}
) {
  const {
    authenticate = true,
    email = '<EMAIL>',
    skipOnboarding = true,
  } = options

  // Configure mobile settings
  await configureMobileSettings(page)

  // Setup authentication if requested
  if (authenticate) {
    await setupAuthenticatedUser(page, { email, skipOnboarding })
  }

  // Verify mobile viewport
  await expectMobileViewport(page)
}
