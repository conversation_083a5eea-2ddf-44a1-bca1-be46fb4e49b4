import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import { useWorkout } from '@/hooks/useWorkout'
import { useRouter } from 'next/navigation'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: vi.fn(() => ({ isRefreshing: false, pullDistance: 0 })),
}))

// Mock the workout store
const mockSkipExercise = vi.fn()
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: Object.assign(
    (selector: any) => {
      const state = {
        currentWorkout: null,
        skipExercise: mockSkipExercise,
      }
      return selector ? selector(state) : state
    },
    {
      getState: () => ({
        skipExercise: mockSkipExercise,
      }),
    }
  ),
}))

describe('WorkoutOverview - Skip Exercise', () => {
  const mockWorkout = {
    Id: 1,
    Label: 'Test Workout',
    Exercises: [
      {
        Id: 123,
        Label: 'Bench Press',
        BodyPartId: 2,
      },
      {
        Id: 456,
        Label: 'Squats',
        BodyPartId: 7,
      },
    ],
  }

  const mockTodaysWorkout = [
    {
      WorkoutTemplates: [mockWorkout],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({
      push: vi.fn(),
      replace: vi.fn(),
      prefetch: vi.fn(),
    })
  })

  it('should mark exercise as finished when skipped before workout starts', async () => {
    // Mock the useWorkout hook for pre-workout state
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: mockTodaysWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [],
      exerciseWorkSetsModels: [], // Empty - workout not started
      expectedExerciseCount: 2,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null, // No session - workout not started
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    })

    render(<WorkoutOverview />)

    // Wait for exercises to render
    await waitFor(() => {
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })

    // Find and click the overflow menu for the first exercise
    const overflowButtons = screen.getAllByLabelText('Exercise options')
    fireEvent.click(overflowButtons[0])

    // Wait for menu to appear
    await waitFor(() => {
      expect(screen.getByText('Skip Exercise')).toBeInTheDocument()
    })

    // Click skip exercise
    fireEvent.click(screen.getByText('Skip Exercise'))

    // Verify the exercise card shows finished state (opacity-60)
    await waitFor(() => {
      const exerciseCards = screen.getAllByTestId('exercise-card')
      expect(exerciseCards[0]).toHaveClass(/opacity-60/)
    })

    // Verify store action was NOT called (because workout hasn't started)
    expect(mockSkipExercise).not.toHaveBeenCalled()
  })

  it('should use store action when workout is active', async () => {
    // Mock the useWorkout hook for active workout state
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: mockTodaysWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: mockWorkout.Exercises,
      exerciseWorkSetsModels: [
        {
          Id: 123,
          Label: 'Bench Press',
          BodyPartId: 2,
          IsFinished: false,
          IsNextExercise: true,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: Date.now(),
          sets: [],
        },
      ],
      expectedExerciseCount: 2,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: { id: 'test-session' }, // Active session
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    })

    render(<WorkoutOverview />)

    // Wait for exercises to render
    await waitFor(() => {
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })

    // Find and click the overflow menu
    const overflowButton = screen.getByLabelText('Exercise options')
    fireEvent.click(overflowButton)

    // Wait for menu to appear
    await waitFor(() => {
      expect(screen.getByText('Skip Exercise')).toBeInTheDocument()
    })

    // Click skip exercise
    fireEvent.click(screen.getByText('Skip Exercise'))

    // Verify store action was called
    await waitFor(() => {
      expect(mockSkipExercise).toHaveBeenCalledWith(123)
    })
  })

  it('should use preview skip functionality before workout starts', async () => {
    // Mock the workout store to include preview skip functionality
    const mockSkipPreviewExercise = vi.fn()
    vi.doMock('@/stores/workoutStore', () => ({
      useWorkoutStore: Object.assign(
        (selector: any) => {
          const state = {
            currentWorkout: null,
            skipExercise: mockSkipExercise,
            skipPreviewExercise: mockSkipPreviewExercise,
            previewExerciseSkips: new Set(),
          }
          return selector ? selector(state) : state
        },
        {
          getState: () => ({
            skipExercise: mockSkipExercise,
            skipPreviewExercise: mockSkipPreviewExercise,
            previewExerciseSkips: new Set(),
          }),
        }
      ),
    }))

    // Mock the useWorkout hook for pre-workout state
    ;(useWorkout as any).mockReturnValue({
      todaysWorkout: mockTodaysWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [],
      exerciseWorkSetsModels: [], // Empty - workout not started
      expectedExerciseCount: 2,
      hasInitialData: true,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null, // No session - workout not started
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    })

    render(<WorkoutOverview />)

    // Wait for exercises to render
    await waitFor(() => {
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
    })

    // Find and click the overflow menu for the first exercise
    const overflowButtons = screen.getAllByLabelText('Exercise options')
    fireEvent.click(overflowButtons[0])

    // Wait for menu to appear
    await waitFor(() => {
      expect(screen.getByText('Skip Exercise')).toBeInTheDocument()
    })

    // Click skip exercise
    fireEvent.click(screen.getByText('Skip Exercise'))

    // Verify preview skip function was called (not the regular skip)
    await waitFor(() => {
      expect(mockSkipPreviewExercise).toHaveBeenCalledWith(123)
      expect(mockSkipExercise).not.toHaveBeenCalled()
    })
  })
})
