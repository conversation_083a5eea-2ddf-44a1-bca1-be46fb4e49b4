'use client'

import React from 'react'
import { render, screen } from '@testing-library/react'
import { SetScreenWithGrid } from '../SetScreenWithGrid'
import { vi } from 'vitest'
import type { RecommendationModel, WorkoutExercise } from '@/types'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { generateAllSets } from '@/utils/generateAllSets'

// Mock dependencies
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/utils/generateAllSets')
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({ setTitle: vi.fn() }),
}))
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    getCachedUserInfo: () => ({ MassUnit: 'lbs' }),
  }),
}))

describe('SetScreenWithGrid - Add Set Button Removal', () => {
  const mockExercise: WorkoutExercise = {
    Id: 1,
    ExerciseId: 101,
    Label: 'Bench Press',
    Sets: 3,
    IsBodyweight: false,
    OneRMPercent: 100,
    PlatesText: '45',
    UnilateralSets: false,
    EquipmentId: 3,
    IsUnilateral: false,
    Units: { Kg: 100, Lb: 225 },
  }

  const mockRecommendation: RecommendationModel = {
    ExerciseId: 101,
    Reps: 8,
    Weight: { Kg: 80, Lb: 180 },
    Series: 3,
    WarmupsCount: 2,
    IsDeload: false,
    IsSkipped: false,
    Units: { Kg: 80, Lb: 180 },
    NbRepsTimeUnderTension: null,
    Equipment: 'Barbell',
    ExerciseName: 'Bench Press',
    IsBodyweight: false,
    SetType: 'standard',
    Increments: { Kg: 2.5, Lb: 5 },
    WarmUpsList: [],
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock generateAllSets to return sets
    vi.mocked(generateAllSets).mockReturnValue([
      {
        Id: -1001,
        IsWarmups: true,
        WarmUpReps: 5,
        WarmUpWeightSet: { Kg: 40, Lb: 90 },
        Weight: { Kg: 0, Lb: 0 },
        IsFinished: false,
        IsNext: true,
      },
      {
        Id: 1,
        Reps: 8,
        Weight: { Kg: 80, Lb: 180 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: false,
      },
    ])
  })

  it('should NOT pass onAddSet prop to ExerciseSetsGrid component', () => {
    // Setup mock hook return
    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      isTransitioning: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      completedSets: [],
      setData: { reps: 8, weight: 180 },
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    render(<SetScreenWithGrid exerciseId={1} />)

    // This test should FAIL initially because the Add set button is still being rendered
    // After our implementation, this test should PASS
    const addSetButton = screen.queryByText('Add set')
    expect(addSetButton).not.toBeInTheDocument()
  })

  it('should NOT display Add set button when exercise has no sets', () => {
    // Mock empty sets scenario
    vi.mocked(generateAllSets).mockReturnValue([])

    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      isSaving: false,
      saveError: null,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      isTransitioning: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      completedSets: [],
      setData: { reps: 0, weight: 0 },
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    render(<SetScreenWithGrid exerciseId={1} />)

    // Should show "No sets" message but NOT the Add set button
    expect(screen.getByText('No sets for this exercise')).toBeInTheDocument()

    // This test should FAIL initially, then PASS after implementation
    const addSetButton = screen.queryByText('Add set')
    expect(addSetButton).not.toBeInTheDocument()
  })

  it('should NOT display Add set button on last set', () => {
    // Mock last set scenario
    vi.mocked(generateAllSets).mockReturnValue([
      {
        Id: 1,
        Reps: 8,
        Weight: { Kg: 80, Lb: 180 },
        IsWarmups: false,
        IsFinished: true,
        IsNext: false,
      },
      {
        Id: 2,
        Reps: 8,
        Weight: { Kg: 80, Lb: 180 },
        IsWarmups: false,
        IsFinished: false,
        IsNext: true,
      },
    ])

    vi.mocked(useSetScreenLogic).mockReturnValue({
      currentExercise: mockExercise,
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 1,
      isSaving: false,
      saveError: null,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      isTransitioning: false,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      isLastExercise: false,
      completedSets: [],
      setData: { reps: 8, weight: 180 },
      setSetData: vi.fn(),
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
    } as any)

    render(<SetScreenWithGrid exerciseId={1} />)

    // This test should FAIL initially, then PASS after implementation
    const addSetButton = screen.queryByText('Add set')
    expect(addSetButton).not.toBeInTheDocument()
  })
})
