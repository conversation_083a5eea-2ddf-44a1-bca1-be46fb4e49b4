import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ExerciseTransitionScreen } from '../ExerciseTransitionScreen'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

vi.mock('@/utils/debugLog', () => ({
  debugLog: Object.assign(vi.fn(), {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  }),
}))

describe('ExerciseTransitionScreen - RecommendationLoadingCoordinator Integration', () => {
  let coordinator: RecommendationLoadingCoordinator
  const mockOnComplete = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset coordinator singleton
    RecommendationLoadingCoordinator['instance'] = null
    coordinator = RecommendationLoadingCoordinator.getInstance()
  })

  afterEach(() => {
    // Clean up coordinator
    coordinator.reset()
  })

  it('should skip transition immediately if coordinator indicates recommendation is already loaded', async () => {
    const exerciseId = 123
    const exerciseName = 'Test Exercise'

    // Mark exercise as already completed in coordinator
    coordinator.startLoading(exerciseId)
    coordinator.completeLoading(exerciseId)

    render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should call onComplete immediately without showing transition
    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 100 }
    ) // Should be immediate, not wait 800ms
  })

  it('should show abbreviated transition if coordinator indicates loading is in progress', async () => {
    const exerciseId = 456
    const exerciseName = 'Test Exercise'

    // Mark exercise as currently loading in coordinator
    coordinator.startLoading(exerciseId)

    render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should show only exercise name, skip checkmark animation
    expect(screen.getByText(exerciseName)).toBeInTheDocument()
    expect(screen.queryByTestId('success-icon-wrapper')).not.toBeInTheDocument()

    // Should complete faster than normal (400ms instead of 800ms)
    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 500 }
    )
  })

  it('should show full transition if coordinator indicates no loading activity', async () => {
    const exerciseId = 789
    const exerciseName = 'Test Exercise'

    render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should show checkmark initially
    expect(screen.getByTestId('success-icon-wrapper')).toBeInTheDocument()

    // Checkmark should disappear after 400ms
    await waitFor(
      () => {
        expect(
          screen.queryByTestId('success-icon-wrapper')
        ).not.toBeInTheDocument()
      },
      { timeout: 500 }
    )

    // Exercise name should appear
    expect(screen.getByText(exerciseName)).toBeInTheDocument()

    // Should complete at 800ms
    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 900 }
    )
  })

  it('should handle coordinator errors gracefully', async () => {
    const exerciseId = 999
    const exerciseName = 'Test Exercise'

    // Make coordinator throw an error
    vi.spyOn(coordinator, 'isLoading').mockImplementation(() => {
      throw new Error('Coordinator error')
    })

    render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should show full transition as fallback
    expect(screen.getByTestId('success-icon-wrapper')).toBeInTheDocument()

    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 900 }
    )
  })

  it('should update timing based on coordinator loading progress', async () => {
    const exerciseId = 111
    const exerciseName = 'Test Exercise'

    // Simulate exercise that's been loading for a while
    coordinator.startLoading(exerciseId)

    // Mock that it's been loading for 600ms
    vi.spyOn(coordinator, 'getLoadingDuration').mockReturnValue(600)

    render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should complete very quickly since loading has been in progress
    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 500 }
    ) // Should complete quickly
  })
})

// Integration test with actual exercise page usage
describe('ExerciseTransitionScreen - Exercise Page Integration', () => {
  it('should coordinate with useExercisePageInitialization loading state', async () => {
    const exerciseId = 222
    const exerciseName = 'Bench Press'
    const mockOnComplete = vi.fn()

    // Simulate the flow where initialization starts loading
    const coordinator = RecommendationLoadingCoordinator.getInstance()
    coordinator.startLoading(exerciseId)

    // Render transition screen as it would be in the exercise page
    const { rerender } = render(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Verify abbreviated transition
    expect(screen.queryByTestId('success-icon-wrapper')).not.toBeInTheDocument()
    expect(screen.getByText(exerciseName)).toBeInTheDocument()

    // Simulate recommendation loaded
    coordinator.completeLoading(exerciseId)

    // Rerender to simulate prop updates
    rerender(
      <ExerciseTransitionScreen
        exerciseName={exerciseName}
        exerciseId={exerciseId}
        onComplete={mockOnComplete}
        coordinatorEnabled
      />
    )

    // Should complete quickly after loading completes (abbreviated transition is 400ms)
    await waitFor(
      () => {
        expect(mockOnComplete).toHaveBeenCalled()
      },
      { timeout: 500 }
    )
  })
})
