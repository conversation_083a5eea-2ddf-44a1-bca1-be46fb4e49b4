import { Page } from '@playwright/test'
import type { RecommendationModel } from '@/types'

export async function mockApiResponses(page: Page) {
  // Common API response mocking utility
  await page.route('**/api/**', (route) => {
    const url = route.request().url()

    // Mock common responses based on endpoint
    if (url.includes('/GetLogAverageWithSetsV2')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Result: true,
          Data: [],
        }),
      })
    } else if (url.includes('/GetUserWorkoutProgramTimeZoneInfo')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          GetUserProgramInfoResponseModel: {
            UserId: 'test-user',
            WeeklyStatus: 'Week 1',
            ProgramLabel: 'Test Program',
          },
        }),
      })
    } else {
      route.continue()
    }
  })
}

export async function mockStartWorkout(
  page: Page,
  recommendation: RecommendationModel
) {
  await page.route('**/api/workout/start**', (route) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Result: true,
        Data: {
          ExerciseLogId: 1,
          exercises: [
            {
              Id: 1,
              Name: 'Test Exercise',
              recommendation,
            },
          ],
        },
      }),
    })
  })

  await page.route('**/api/exercise/*/recommendation**', (route) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Result: true,
        Data: recommendation,
      }),
    })
  })
}

export async function mockSaveSet(page: Page) {
  await page.route('**/api/set/save**', (route) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Result: true,
        Data: { success: true },
      }),
    })
  })
}
