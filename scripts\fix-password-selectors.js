#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// Find all test files that might have password selectors
const testFiles = glob.sync('tests/e2e/**/*.spec.ts')

console.log(`Found ${testFiles.length} test files to check`)

let filesUpdated = 0
let totalReplacements = 0

testFiles.forEach((file) => {
  let content = fs.readFileSync(file, 'utf8')
  const originalContent = content
  
  // Check if file needs auth helper import
  const hasPasswordSelector = content.includes("getByLabel('Password')") || content.includes('getByLabel("Password")')
  const hasAuthHelperImport = content.includes("import { fillPasswordField") || content.includes("import { performLogin")
  
  if (hasPasswordSelector && !hasAuthHelperImport) {
    // Add import if not present
    if (!content.includes("from './helpers/auth-helpers'")) {
      // Find the last import statement
      const importMatch = content.match(/^import .* from .*/gm)
      if (importMatch) {
        const lastImport = importMatch[importMatch.length - 1]
        const lastImportIndex = content.lastIndexOf(lastImport)
        content = content.slice(0, lastImportIndex + lastImport.length) + 
          "\nimport { fillPasswordField } from './helpers/auth-helpers'" + 
          content.slice(lastImportIndex + lastImport.length)
      }
    }
  }
  
  // Replace password selectors
  const replacements = [
    // getByLabel('Password').fill(...) -> fillPasswordField(page, ...)
    {
      pattern: /await page\.getByLabel\(['"]Password['"]\)\.fill\(([^)]+)\)/g,
      replacement: 'await fillPasswordField(page, $1)'
    },
    // getByLabel('Password') for other uses -> locator('#password')
    {
      pattern: /page\.getByLabel\(['"]Password['"]\)/g,
      replacement: "page.locator('#password')"
    },
    // In expect statements
    {
      pattern: /expect\(page\.getByLabel\(['"]Password['"]\)\)/g,
      replacement: "expect(page.locator('#password'))"
    }
  ]
  
  replacements.forEach(({ pattern, replacement }) => {
    const matches = content.match(pattern)
    if (matches) {
      totalReplacements += matches.length
      content = content.replace(pattern, replacement)
    }
  })
  
  // Write back if changed
  if (content !== originalContent) {
    fs.writeFileSync(file, content, 'utf8')
    filesUpdated++
    console.log(`Updated: ${file}`)
  }
})

console.log(`\nSummary:`)
console.log(`Files updated: ${filesUpdated}`)
console.log(`Total replacements: ${totalReplacements}`)