import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { CurrentSetCardControls } from '../CurrentSetCardControls'

describe('CurrentSetCardControls', () => {
  const defaultProps = {
    setData: { reps: 10, weight: 100, duration: 0 },
    onSetDataChange: vi.fn(),
    unit: 'kg' as const,
    isSaving: false,
    animationClass: '',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Fade-in animation', () => {
    it('should apply 200ms fade-in animation class when animationClass is provided', () => {
      render(
        <CurrentSetCardControls {...defaultProps} animationClass="fade-in" />
      )

      const container = screen.getByTestId('input-controls-container')
      expect(container).toHaveClass('fade-in')
    })

    it('should render without animation class when not provided', () => {
      render(<CurrentSetCardControls {...defaultProps} />)

      const container = screen.getByTestId('input-controls-container')
      expect(container).not.toHaveClass('fade-in')
    })
  })

  it('renders reps and weight sections', () => {
    render(<CurrentSetCardControls {...defaultProps} />)

    expect(screen.getByTestId('reps-section')).toBeInTheDocument()
    expect(screen.getByTestId('weight-section')).toBeInTheDocument()
  })

  describe('Weight increment behavior', () => {
    describe('Default increments (no recommendation)', () => {
      it('should increment weight by 1 for kg unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should increment by 1 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 51, // 50 + 1
          duration: 0,
        })
      })

      it('should increment weight by 2.5 for lbs unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 100, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should increment by 2.5 for lbs
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 102.5, // 100 + 2.5
          duration: 0,
        })
      })

      it('should decrement weight by 1 for kg unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should decrement by 1 for kg
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 49, // 50 - 1
          duration: 0,
        })
      })

      it('should decrement weight by 2.5 for lbs unit', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 100, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should decrement by 2.5 for lbs
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 97.5, // 100 - 2.5
          duration: 0,
        })
      })

      it('should not allow weight to go below 0', () => {
        const onSetDataChange = vi.fn()
        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 0.5, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should set to 0, not negative
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 0,
          duration: 0,
        })
      })
    })

    describe('Fixed increments (ignoring API recommendations)', () => {
      it('should ALWAYS use 1kg increment for kg units', () => {
        const onSetDataChange = vi.fn()
        // Even if API would recommend different increments, we force 1kg

        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="kg"
            setData={{ reps: 10, weight: 50, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should ALWAYS use 1kg increment (forced)
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 51, // 50 + 1
          duration: 0,
        })
      })

      it('should ALWAYS use 2.5lbs increment for lbs units', () => {
        const onSetDataChange = vi.fn()
        // Test for the bug fix: API might return 5lbs, but we force 2.5lbs

        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 35, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight increment button
        const incrementButton = screen.getByLabelText('Increase weight')
        fireEvent.click(incrementButton)

        // Should ALWAYS use 2.5lbs increment (forced)
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 37.5, // 35 + 2.5
          duration: 0,
        })
      })

      it('should use same fixed increments for decrement', () => {
        const onSetDataChange = vi.fn()

        render(
          <CurrentSetCardControls
            {...defaultProps}
            unit="lbs"
            setData={{ reps: 10, weight: 40, duration: 0 }}
            onSetDataChange={onSetDataChange}
          />
        )

        // Find and click the weight decrement button
        const decrementButton = screen.getByLabelText('Decrease weight')
        fireEvent.click(decrementButton)

        // Should use fixed 2.5lbs decrement
        expect(onSetDataChange).toHaveBeenCalledWith({
          reps: 10,
          weight: 37.5, // 40 - 2.5
          duration: 0,
        })
      })
    })
  })

  describe('Reps increment behavior', () => {
    it('should increment reps by 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 10, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps increment button
      const incrementButton = screen.getByLabelText('Increase reps')
      fireEvent.click(incrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 11,
        weight: 50,
        duration: 0,
      })
    })

    it('should decrement reps by 1 but not below 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 2, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps decrement button
      const decrementButton = screen.getByLabelText('Decrease reps')
      fireEvent.click(decrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 1,
        weight: 50,
        duration: 0,
      })
    })

    it('should not decrement reps below 1', () => {
      const onSetDataChange = vi.fn()
      render(
        <CurrentSetCardControls
          {...defaultProps}
          setData={{ reps: 1, weight: 50, duration: 0 }}
          onSetDataChange={onSetDataChange}
        />
      )

      // Find and click the reps decrement button
      const decrementButton = screen.getByLabelText('Decrease reps')
      fireEvent.click(decrementButton)

      expect(onSetDataChange).toHaveBeenCalledWith({
        reps: 1, // Should stay at 1
        weight: 50,
        duration: 0,
      })
    })
  })
})
