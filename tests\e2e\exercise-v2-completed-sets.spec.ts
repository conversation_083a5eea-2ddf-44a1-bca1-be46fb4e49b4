import { test, expect } from '@playwright/test'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('Exercise V2 - Completed Sets Display', () => {
  test.beforeEach(async ({ page }) => {
    await setupAuthenticatedUser(page)
  })

  test('should not show completed sets section on first warmup set', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1') // Bench Press (from mocked data)

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Verify completed sets section is not visible
    await expect(page.getByText('Completed Sets')).not.toBeVisible()

    // Verify we're on the first set (header shows "Set 1 of 6")
    await expect(page.getByText('Set 1 of 6')).toBeVisible()
  })

  test('should show completed sets after saving first warmup set', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete the first warmup set
    await page.getByRole('button', { name: 'Save set' }).click()

    // Wait for the save to complete and rest timer to appear
    await page.waitForSelector('[data-testid="rest-timer"]', {
      timeout: 5000,
    })

    // Skip rest timer
    await page.getByRole('button', { name: 'Skip' }).click()

    // Now the completed sets section should be visible
    await expect(page.getByText('Completed Sets')).toBeVisible()

    // Verify the first warmup set is shown as completed
    await expect(page.getByText('W1')).toBeVisible()

    // Should show check mark icon
    const completedSetRow = page.locator('text=W1').locator('..')
    await expect(completedSetRow.locator('svg')).toBeVisible()
  })

  test('should update completed sets list when saving multiple sets', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete first warmup set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Verify first warmup set is shown
    await expect(page.getByText('W1')).toBeVisible()

    // Complete second warmup set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Both warmup sets should now be visible in completed sets
    await expect(page.getByText('W1')).toBeVisible()
    await expect(page.getByText('W2')).toBeVisible()

    // Complete first work set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Should now show both warmups and first work set
    await expect(page.getByText('W1')).toBeVisible()
    await expect(page.getByText('W2')).toBeVisible()
    await expect(page.getByText('Set 1')).toBeVisible()
  })

  test('completed sets should appear above current set card', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete first warmup set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Get bounding boxes to verify position
    const completedSetsBox = await page
      .getByText('Completed Sets')
      .boundingBox()
    const currentSetBox = await page
      .getByTestId('current-set-card')
      .boundingBox()

    // Verify completed sets appear above current set card
    expect(completedSetsBox).toBeTruthy()
    expect(currentSetBox).toBeTruthy()
    expect(completedSetsBox!.y).toBeLessThan(currentSetBox!.y)
  })

  test('should show completed sets with correct weight units', async ({
    page,
  }) => {
    // Navigate to exercise V2 page
    await page.goto('/workout/exercise-v2/1')

    // Wait for page to load
    await page.waitForSelector('[data-testid="exercise-page-container"]')

    // Complete a set
    await page.getByRole('button', { name: 'Save set' }).click()
    await page.waitForSelector('[data-testid="rest-timer"]')
    await page.getByRole('button', { name: 'Skip' }).click()

    // Check for weight display - should show kg by default for test user
    const completedSetText = await page
      .locator('text=W1')
      .locator('..')
      .textContent()
    expect(completedSetText).toContain('kg')
  })
})
