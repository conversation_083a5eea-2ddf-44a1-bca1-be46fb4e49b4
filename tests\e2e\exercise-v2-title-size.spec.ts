import { test, expect } from '@playwright/test'
import { login } from './helpers'
import { mockWorkoutAPI } from './helpers/workout-api-mocks'

test.describe('Exercise V2 Header Display', () => {
  test.beforeEach(async ({ page }) => {
    // Setup mock API responses
    await mockWorkoutAPI(page)

    // Login as test user
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')
  })

  test('should NOT display exercise name in header', async ({ page }) => {
    // Navigate to workout page and start workout
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Click start workout button
    await page.click('button:has-text("Start workout")')

    // Wait for navigation to exercise page (it should redirect to first exercise)
    await page.waitForURL(/\/workout\/exercise/)

    // Get the current URL and extract exercise ID
    const url = page.url()
    const match = url.match(/\/workout\/exercise\/(\d+)/)
    const exerciseId = match ? match[1] : '1'

    // Navigate to exercise v2 page with the same ID
    await page.goto(`/workout/exercise-v2/${exerciseId}`)

    // Wait for exercise info header to be visible
    await page.waitForSelector('[data-testid="exercise-info-header"]', {
      state: 'visible',
      timeout: 10000,
    })

    // Then: Exercise name should NOT be in the header (no h2 element)
    const exerciseNameInHeader = page.locator(
      '[data-testid="exercise-info-header"] h2'
    )
    await expect(exerciseNameInHeader).not.toBeVisible()
  })
})
