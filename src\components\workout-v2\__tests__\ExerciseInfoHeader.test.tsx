import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseInfoHeader } from '../ExerciseInfoHeader'

describe('ExerciseInfoHeader', () => {
  it('should NOT display exercise name', () => {
    render(<ExerciseInfoHeader currentSet={1} totalSets={5} />)

    expect(screen.queryByText('Bench Press')).not.toBeInTheDocument()
  })

  it('should have header container without exercise name', () => {
    render(<ExerciseInfoHeader currentSet={1} totalSets={5} />)

    const header = screen.getByTestId('exercise-info-header')
    expect(header).toBeInTheDocument()
    // Should not contain any h2 element for exercise name
    expect(header.querySelector('h2')).not.toBeInTheDocument()
  })

  it('should display set progress', () => {
    render(<ExerciseInfoHeader currentSet={3} totalSets={8} />)

    expect(screen.getByText('Set 3 of 8')).toBeInTheDocument()
  })

  it('should display progress bar with correct width', () => {
    render(
      <ExerciseInfoHeader currentSet={3} totalSets={4} completedSets={2} />
    )

    const progressBar = screen.getByTestId('exercise-progress-bar')
    const progressFill = progressBar.firstChild as HTMLElement

    // 2 completed out of 4 total = 50%
    expect(progressFill).toHaveStyle({ width: '50%' })
  })

  it('should align with page content', () => {
    const { container } = render(
      <ExerciseInfoHeader currentSet={1} totalSets={3} />
    )

    // Check that the container has proper padding for alignment
    const header = container.firstChild as HTMLElement
    expect(header).toHaveClass('px-4')
  })
})
