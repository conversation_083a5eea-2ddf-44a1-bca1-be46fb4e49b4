import { render, screen, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { ExercisePageStates } from '../ExercisePageStates'
import type { ExerciseModel } from '@/types'

// Mock the loading and error components
vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: ({ exerciseName, isLoadingRecommendations }: any) => (
    <div data-testid="loading-state">
      {exerciseName && <div data-testid="exercise-name">{exerciseName}</div>}
      {isLoadingRecommendations && (
        <div data-testid="recommendations-loading">
          Loading recommendations...
        </div>
      )}
    </div>
  ),
}))

vi.mock('@/components/workout/SetScreenErrorState', () => ({
  SetScreenErrorState: ({ onRetry, message }: any) => (
    <div data-testid="error-state">
      {message && <div data-testid="error-message">{message}</div>}
      <button onClick={onRetry} data-testid="retry-button">
        Retry
      </button>
    </div>
  ),
}))

describe('ExercisePageStates - Timeout Handling', () => {
  const mockExercise: ExerciseModel = {
    Id: 123,
    Label: 'Test Exercise',
    sets: [],
  }

  const defaultProps = {
    loadingError: null,
    workoutError: null,
    retryInitialization: vi.fn(),
    isInitializing: false,
    isLoadingWorkout: false,
    isLoadingRecommendation: false,
    isLoading: false,
    recommendation: null,
    currentExercise: mockExercise,
    workoutSession: { id: 'test-session' },
    error: null,
    refetchRecommendation: vi.fn(),
    showComplete: false,
    showExerciseComplete: false,
    currentSet: null,
    isLastExercise: false,
    handleSaveSet: vi.fn(),
  }

  beforeEach(() => {
    vi.useFakeTimers()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should show loading state when loading recommendations', () => {
    render(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation
        recommendation={null}
      />
    )

    expect(screen.getByTestId('loading-state')).toBeInTheDocument()
    expect(screen.getByTestId('recommendations-loading')).toBeInTheDocument()
  })

  it('should show timeout error after 45 seconds of loading', async () => {
    const mockRefetch = vi.fn()

    render(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation
        recommendation={null}
        refetchRecommendation={mockRefetch}
      />
    )

    // Initially should show loading
    expect(screen.getByTestId('loading-state')).toBeInTheDocument()

    // Fast-forward 45 seconds and flush all timers
    act(() => {
      vi.advanceTimersByTime(45000)
      vi.runAllTimers()
    })

    // Should now show error state
    expect(screen.getByTestId('error-state')).toBeInTheDocument()
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Loading took longer than expected. Please try again.'
    )
  })

  it('should allow retry after timeout', async () => {
    const mockRefetch = vi.fn()

    render(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation
        recommendation={null}
        refetchRecommendation={mockRefetch}
      />
    )

    // Fast-forward to timeout and flush timers
    act(() => {
      vi.advanceTimersByTime(45000)
      vi.runAllTimers()
    })

    // Should show error state
    expect(screen.getByTestId('error-state')).toBeInTheDocument()

    // Click retry button
    const retryButton = screen.getByTestId('retry-button')
    act(() => {
      retryButton.click()
    })

    // Should call refetch
    expect(mockRefetch).toHaveBeenCalledTimes(1)
  })

  it('should reset timeout when loading state changes', async () => {
    const { rerender } = render(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation
        recommendation={null}
      />
    )

    // Fast-forward 30 seconds (not enough to timeout)
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    // Change loading state (simulate recommendation loaded)
    rerender(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation={false}
        recommendation={{ id: 1, value: 'test' } as any}
      />
    )

    // Fast-forward another 30 seconds (total 60 seconds)
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    // Should not show timeout error since loading state changed
    expect(screen.queryByTestId('error-state')).not.toBeInTheDocument()
  })

  it('should not timeout if recommendation is already loaded', () => {
    render(
      <ExercisePageStates
        {...defaultProps}
        isLoadingRecommendation
        recommendation={{ id: 1, value: 'test' } as any}
      />
    )

    // Fast-forward past timeout
    act(() => {
      vi.advanceTimersByTime(50000)
    })

    // Should not show error state since recommendation exists
    expect(screen.queryByTestId('error-state')).not.toBeInTheDocument()
  })
})
