import { test, expect } from '@playwright/test'
import { WorkoutPage } from './pages/WorkoutPage'
import { ExercisePage } from './pages/ExercisePage'
import { setupAuthenticatedUser } from './helpers/auth-helper'

test.describe('V2 Rest Timer Duration Settings', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 390, height: 844 })
    await setupAuthenticatedUser(page)
  })

  test('should display and allow changing rest timer duration', async ({
    page,
  }) => {
    // Navigate to V2 exercise page
    await page.goto('/workout')
    const workoutPage = new WorkoutPage(page)
    await workoutPage.waitForPageLoad()
    await workoutPage.startWorkout()

    // Go to V2 UI
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Wait for rest timer to appear
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()

    // Check duration setting is visible
    const durationSetting = page.getByTestId('duration-setting')
    await expect(durationSetting).toBeVisible()
    await expect(durationSetting).toContainText('1:30') // Default 90 seconds

    // Click to open duration picker
    await page.getByTestId('duration-setting-button').click()
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Select 2 minutes
    await page.getByText('2:00').click()

    // Duration picker should close
    await expect(page.getByTestId('duration-picker')).not.toBeVisible()

    // Verify localStorage was updated
    const restDuration = await page.evaluate(() =>
      localStorage.getItem('restDuration')
    )
    expect(restDuration).toBe('120')

    // Skip current timer
    await page.click('button:has-text("Hide")')

    // Save another set to verify new duration is used
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // New timer should show updated duration
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('2:00')
  })

  test('should persist duration setting across page reloads', async ({
    page,
  }) => {
    // Set custom duration
    await page.evaluate(() => {
      localStorage.setItem('restDuration', '180')
    })

    // Navigate to V2 exercise page
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Timer should use custom duration
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('3:00')
  })

  test('should handle edge cases for duration', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Open duration picker
    await page.getByTestId('duration-setting-button').click()

    // Should show preset options
    await expect(page.getByText('0:30')).toBeVisible()
    await expect(page.getByText('1:00')).toBeVisible()
    await expect(page.getByText('1:30')).toBeVisible()
    await expect(page.getByText('2:00')).toBeVisible()
    await expect(page.getByText('3:00')).toBeVisible()
    await expect(page.getByText('5:00')).toBeVisible()

    // Click outside to close
    await page.click('body', { position: { x: 10, y: 10 } })
    await expect(page.getByTestId('duration-picker')).not.toBeVisible()
  })

  test('should use shorter duration for warmup sets', async ({ page }) => {
    // Set custom duration
    await page.evaluate(() => {
      localStorage.setItem('restDuration', '120')
    })

    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Assuming first set is warmup (need to verify with actual data)
    // This test might need adjustment based on actual workout data

    // Save a warmup set
    const firstSetRow = page.locator('[data-testid="set-row-0"]')
    await expect(firstSetRow).toBeVisible()

    // Fill and save
    await page.fill('input[placeholder="Reps"]', '8')
    await page.fill('input[placeholder*="Weight"]', '25')
    await page.click('button:has-text("Save set")')

    // Timer should show reduced duration for warmup (1/3 of 120 = 40s)
    await expect(page.getByTestId('rest-timer-container')).toBeVisible()
    await expect(page.getByTestId('countdown-text')).toContainText('0:40')
  })

  test('should have proper touch targets for mobile', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Check touch target size
    const durationButton = page.getByTestId('duration-setting-button')
    const box = await durationButton.boundingBox()
    expect(box?.height).toBeGreaterThanOrEqual(44) // Minimum touch target

    // Open picker and check option sizes
    await durationButton.click()
    const firstOption = page.getByTestId('duration-option-30')
    const optionBox = await firstOption.boundingBox()
    expect(optionBox?.height).toBeGreaterThanOrEqual(44)
  })

  test('should allow entering custom duration', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set to trigger rest timer
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Open duration picker
    await page.getByTestId('duration-setting-button').click()
    await expect(page.getByTestId('duration-picker')).toBeVisible()

    // Click custom button
    await page.getByTestId('duration-option-custom').click()

    // Custom duration modal should appear
    await expect(page.getByTestId('custom-duration-modal')).toBeVisible()

    // Enter custom duration
    const input = page.getByLabel('Enter duration in seconds')
    await input.fill('240') // 4 minutes

    // Confirm
    await page.click('button:has-text("Confirm")')

    // Modal should close
    await expect(page.getByTestId('custom-duration-modal')).not.toBeVisible()
    await expect(page.getByTestId('duration-picker')).not.toBeVisible()

    // Timer should update to custom duration
    await expect(page.getByTestId('duration-setting')).toContainText('4:00')

    // Verify localStorage was updated
    const restDuration = await page.evaluate(() =>
      localStorage.getItem('restDuration')
    )
    expect(restDuration).toBe('240')
  })

  test('should validate custom duration input', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Open duration picker and click custom
    await page.getByTestId('duration-setting-button').click()
    await page.getByTestId('duration-option-custom').click()

    const input = page.getByLabel('Enter duration in seconds')

    // Test too low value
    await input.fill('3')
    await page.click('button:has-text("Confirm")')
    await expect(
      page.getByText('Please enter a value between 5 and 600 seconds')
    ).toBeVisible()

    // Test too high value
    await input.fill('700')
    await page.click('button:has-text("Confirm")')
    await expect(
      page.getByText('Please enter a value between 5 and 600 seconds')
    ).toBeVisible()

    // Test non-numeric value
    await input.fill('abc')
    await page.click('button:has-text("Confirm")')
    await expect(page.getByText('Please enter a valid number')).toBeVisible()

    // Test valid value
    await input.fill('60')
    await page.click('button:has-text("Confirm")')
    await expect(page.getByTestId('custom-duration-modal')).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('1:00')
  })

  test('should cancel custom duration entry', async ({ page }) => {
    await page.goto('/workout/exercise-v2/1')
    const exercisePage = new ExercisePage(page)
    await exercisePage.waitForPageLoad()

    // Save a set
    await page.fill('input[placeholder="Reps"]', '10')
    await page.fill('input[placeholder*="Weight"]', '50')
    await page.click('button:has-text("Save set")')

    // Open duration picker and click custom
    await page.getByTestId('duration-setting-button').click()
    await page.getByTestId('duration-option-custom').click()

    // Enter value but cancel
    const input = page.getByLabel('Enter duration in seconds')
    await input.fill('300')
    await page.click('button:has-text("Cancel")')

    // Modal should close without updating
    await expect(page.getByTestId('custom-duration-modal')).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('1:30') // Still default

    // Test escape key
    await page.getByTestId('duration-option-custom').click()
    await input.fill('300')
    await page.keyboard.press('Escape')

    // Modal should close without updating
    await expect(page.getByTestId('custom-duration-modal')).not.toBeVisible()
    await expect(page.getByTestId('duration-setting')).toContainText('1:30') // Still default
  })
})
