import { test, expect } from '@playwright/test'
import { login, waitForLoadingToComplete } from './helpers'

test.describe('Workout Pull-to-Refresh Sensitivity', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/workout')
    await waitForLoadingToComplete(page)
  })

  test('should not trigger pull-to-refresh with small scroll movements', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    // Wait for workout content to load
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Get initial state
    const refreshIndicator = page.locator(
      '[role="progressbar"], svg.animate-spin'
    )

    // Test dead zone - movements within 100px should be completely ignored
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 180, { steps: 15 }) // 80px movement (within deadZone)

    // Should not show ANY pull indicator within dead zone
    const pullIndicator = page
      .locator('div')
      .filter({ has: page.locator('svg') })
      .first()
    await expect(pullIndicator).not.toBeVisible()

    await page.mouse.up()

    // Test small movement beyond dead zone but below threshold
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 210, { steps: 20 }) // 110px movement

    // With resistance 4.0, effective distance = (60-30)/4 = 7.5px - should not trigger
    await expect(refreshIndicator).not.toBeVisible()

    await page.mouse.up()
  })

  test('should require significant pull distance to trigger refresh', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Simulate touch gesture using mouse events
    await page.mouse.move(200, 100)
    await page.mouse.down()

    // Move down 110px (should show indicator but not trigger with new 120px threshold)
    await page.mouse.move(200, 210, { steps: 20 })

    // Check that pull indicator appears but doesn't trigger refresh
    const pullIndicator = page
      .locator('svg')
      .filter({ hasNot: page.locator('.animate-spin') })
    await expect(pullIndicator).toBeVisible()

    await page.mouse.up()

    // Verify no refresh happened (no loading spinner)
    const loadingSpinner = page.locator('svg.animate-spin')
    await expect(loadingSpinner).not.toBeVisible()
  })

  test('should show resistance when pulling', async ({ page, context }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Start pull gesture
    await page.mouse.move(200, 50)
    await page.mouse.down()

    // Pull down significantly (190px raw movement)
    await page.mouse.move(200, 240, { steps: 30 })

    // With dead zone 30px and resistance 4.0:
    // Effective movement = 190 - 30 = 160px
    // Actual pullDistance = 160 / 4.0 = 40px
    const indicator = page
      .locator('div')
      .filter({ has: page.locator('svg') })
      .first()
    const transform = await indicator.evaluate((el) => {
      const style = window.getComputedStyle(el)
      const match = style.transform.match(/translateY\((\d+)px\)/)
      return match ? parseInt(match[1]) : 0
    })

    // Should be around 40px due to dead zone and resistance
    expect(transform).toBeGreaterThan(35)
    expect(transform).toBeLessThan(45)

    await page.mouse.up()
  })

  test('should require pull past 120px threshold to trigger refresh', async ({
    page,
    context,
  }) => {
    // Enable touch events
    await context.addInitScript(() => {
      Object.defineProperty(navigator, 'maxTouchPoints', {
        value: 1,
        writable: false,
        configurable: true,
      })
    })

    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Pull to exactly threshold (with dead zone and resistance calculation)
    // To reach 120px actual distance with resistance 4.0 and deadZone 30px:
    // Required raw movement = (120 * 4.0) + 30 = 510px
    await page.mouse.move(200, 50)
    await page.mouse.down()
    await page.mouse.move(200, 560, { steps: 50 }) // 510px movement

    // Should show ready state at threshold
    const pullIndicator = page
      .locator('svg')
      .filter({ hasNot: page.locator('.animate-spin') })
    await expect(pullIndicator).toBeVisible()

    await page.mouse.up()

    // Should trigger refresh
    const loadingSpinner = page.locator('svg.animate-spin')
    await expect(loadingSpinner).toBeVisible()
  })

  test('should not interfere with normal scrolling', async ({ page }) => {
    await page.waitForSelector('[data-testid="workout-overview-container"]')

    // Add content to make page scrollable
    await page.evaluate(() => {
      const container = document.querySelector(
        '[data-testid="workout-overview-container"]'
      )
      if (container) {
        // Add enough content to scroll
        for (let i = 0; i < 20; i++) {
          const div = document.createElement('div')
          div.style.height = '100px'
          div.textContent = `Test content ${i}`
          container.appendChild(div)
        }
      }
    })

    // Scroll down the page
    await page.evaluate(() => window.scrollTo(0, 200))

    // Try to pull down while scrolled - should not activate
    await page.mouse.move(200, 100)
    await page.mouse.down()
    await page.mouse.move(200, 200, { steps: 10 })

    // Should not show any refresh indicator when not at top
    const refreshIndicator = page
      .locator('div')
      .filter({ has: page.locator('svg') })
      .first()
    await expect(refreshIndicator).not.toBeVisible()

    await page.mouse.up()
  })
})
