<!DOCTYPE html>
<html>
<head>
    <title>Kebab Menu Visual Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .icon-display {
            display: flex;
            align-items: center;
            gap: 30px;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .label {
            font-weight: bold;
            min-width: 100px;
        }
        .button-wrapper {
            display: inline-flex;
            align-items: center;
            padding: 8px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .button-wrapper:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kebab Menu Icon Visibility Test</h1>
        <p>This page shows the before and after comparison of the kebab menu dots.</p>
        
        <div class="icon-display">
            <div class="label">OLD (r=1):</div>
            <div class="button-wrapper">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="5" r="1" />
                    <circle cx="12" cy="12" r="1" />
                    <circle cx="12" cy="19" r="1" />
                </svg>
            </div>
            <span>← Very thin, hard to see</span>
        </div>
        
        <div class="icon-display">
            <div class="label">NEW (r=2):</div>
            <div class="button-wrapper">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="5" r="2" />
                    <circle cx="12" cy="12" r="2" />
                    <circle cx="12" cy="19" r="2" />
                </svg>
            </div>
            <span>← Much better visibility!</span>
        </div>
        
        <h2>Gradient Version</h2>
        
        <div class="icon-display">
            <div class="label">OLD (r=1):</div>
            <div class="button-wrapper">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <defs>
                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#d4af37" />
                            <stop offset="100%" stop-color="#f7e98e" />
                        </linearGradient>
                    </defs>
                    <circle cx="12" cy="5" r="1" fill="url(#grad1)" />
                    <circle cx="12" cy="12" r="1" fill="url(#grad1)" />
                    <circle cx="12" cy="19" r="1" fill="url(#grad1)" />
                </svg>
            </div>
            <span>← Barely visible</span>
        </div>
        
        <div class="icon-display">
            <div class="label">NEW (r=2):</div>
            <div class="button-wrapper">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <defs>
                        <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#d4af37" />
                            <stop offset="100%" stop-color="#f7e98e" />
                        </linearGradient>
                    </defs>
                    <circle cx="12" cy="5" r="2" fill="url(#grad2)" />
                    <circle cx="12" cy="12" r="2" fill="url(#grad2)" />
                    <circle cx="12" cy="19" r="2" fill="url(#grad2)" />
                </svg>
            </div>
            <span>← Clear and tappable!</span>
        </div>
        
        <p><strong>Summary:</strong> The dots have been increased from radius 1 (2px diameter) to radius 2 (4px diameter), making them 2x larger and much easier to see and tap on mobile devices.</p>
    </div>
</body>
</html>