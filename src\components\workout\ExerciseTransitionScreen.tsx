'use client'

import { useEffect, useState } from 'react'
import { SuccessIcon } from './SuccessIcon'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

interface ExerciseTransitionScreenProps {
  exerciseName?: string
  exerciseId?: number
  onComplete: () => void
  coordinatorEnabled?: boolean
}

export function ExerciseTransitionScreen({
  exerciseName,
  exerciseId,
  onComplete,
  coordinatorEnabled = false,
}: ExerciseTransitionScreenProps) {
  // Check coordinator state to optimize transition
  const [transitionConfig] = useState(() => {
    let showCheckmark = true
    let duration = 800
    let checkmarkTime = 400
    let skipTransition = false

    if (coordinatorEnabled && exerciseId) {
      try {
        const coordinator = RecommendationLoadingCoordinator.getInstance()
        const isLoading = coordinator.isLoading(exerciseId)
        const isCompleted = coordinator.isCompleted(exerciseId)

        // Skip transition entirely if already completed
        if (isCompleted) {
          skipTransition = true
        }

        // Abbreviated transition if loading is in progress
        if (isLoading && !isCompleted) {
          showCheckmark = false // Skip checkmark animation
          duration = 400 // Shorter duration
          checkmarkTime = 0
        }
      } catch (error) {
        // If coordinator fails, use default behavior
        console.error('Coordinator error in ExerciseTransitionScreen:', error)
      }
    }

    return { showCheckmark, duration, checkmarkTime, skipTransition }
  })

  const [showCheckmark, setShowCheckmark] = useState(
    transitionConfig.showCheckmark
  )
  const [showExerciseName, setShowExerciseName] = useState(
    !transitionConfig.showCheckmark
  )

  // Skip transition entirely if coordinator says recommendation is already loaded
  useEffect(() => {
    if (transitionConfig.skipTransition) {
      onComplete()
    }
  }, [transitionConfig.skipTransition, onComplete])

  useEffect(() => {
    if (transitionConfig.skipTransition) {
      return // Skip timer setup if we're skipping transition
    }

    const timers: NodeJS.Timeout[] = []

    if (transitionConfig.checkmarkTime > 0) {
      // Hide checkmark after specified duration
      timers.push(
        setTimeout(
          () => setShowCheckmark(false),
          transitionConfig.checkmarkTime
        )
      )
      // Show exercise name at checkmark duration
      timers.push(
        setTimeout(
          () => setShowExerciseName(true),
          transitionConfig.checkmarkTime
        )
      )
    }

    // Complete at specified duration using requestAnimationFrame
    timers.push(
      setTimeout(() => {
        // Use requestAnimationFrame to avoid XHR violation during DOM updates
        if (typeof requestAnimationFrame !== 'undefined') {
          requestAnimationFrame(() => {
            onComplete()
          })
        } else {
          // Fallback for environments without requestAnimationFrame
          onComplete()
        }
      }, transitionConfig.duration)
    )

    return () => {
      timers.forEach((timer) => clearTimeout(timer))
    }
  }, [onComplete, transitionConfig])

  if (transitionConfig.skipTransition) {
    return null
  }

  return (
    <div
      data-testid="exercise-transition-screen"
      className="min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary px-6"
    >
      {showCheckmark && (
        <div data-testid="success-icon-wrapper">
          <SuccessIcon size={120} className="animate-fade-in" />
        </div>
      )}
      {showExerciseName && (
        <p className="text-xl font-medium text-text-primary animate-fade-in">
          {exerciseName || 'Loading exercise...'}
        </p>
      )}
    </div>
  )
}
