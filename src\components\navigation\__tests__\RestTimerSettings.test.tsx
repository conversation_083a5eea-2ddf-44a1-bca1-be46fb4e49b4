import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { RestTimerSettings } from '../RestTimerSettings'

describe('RestTimerSettings', () => {
  beforeEach(() => {
    localStorage.clear()
    vi.clearAllMocks()
  })

  describe('Rest Duration Setting', () => {
    it('should display current rest duration from localStorage', async () => {
      localStorage.setItem('restDuration', '180')
      render(<RestTimerSettings />)

      // Click to open menu first
      fireEvent.click(screen.getByLabelText('Timer settings'))

      // Wait for Rest Duration to appear with correct value
      await waitFor(() => {
        expect(screen.getByText('Rest Duration')).toBeInTheDocument()
        expect(screen.getByText('3:00')).toBeInTheDocument()
      })
    })

    it('should display default rest duration when not set', () => {
      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))

      expect(screen.getByText('Rest Duration')).toBeInTheDocument()
      expect(screen.getByText('2:00')).toBeInTheDocument()
    })

    it('should save selected rest duration to localStorage', async () => {
      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))
      fireEvent.click(screen.getByText('Rest Duration'))

      // Select 90 seconds option
      fireEvent.click(screen.getByText('1:30'))

      await waitFor(() => {
        expect(localStorage.getItem('restDuration')).toBe('90')
      })
    })

    it('should dispatch storage event when duration changes', async () => {
      const storageEventSpy = vi.fn()
      window.addEventListener('storage', storageEventSpy)

      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))
      fireEvent.click(screen.getByText('Rest Duration'))
      fireEvent.click(screen.getByText('3:00'))

      await waitFor(() => {
        expect(storageEventSpy).toHaveBeenCalled()
      })

      window.removeEventListener('storage', storageEventSpy)
    })

    it('should show all available duration options', () => {
      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))
      fireEvent.click(screen.getByText('Rest Duration'))

      // Check all duration options are available
      expect(screen.getByText('0:05')).toBeInTheDocument()
      expect(screen.getByText('0:20')).toBeInTheDocument()
      expect(screen.getByText('0:30')).toBeInTheDocument()
      expect(screen.getByText('1:00')).toBeInTheDocument()
      expect(screen.getByText('1:30')).toBeInTheDocument()
      expect(screen.getByText('2:00')).toBeInTheDocument()
      expect(screen.getByText('3:00')).toBeInTheDocument()
      expect(screen.getByText('5:00')).toBeInTheDocument()
      expect(screen.getByText('Custom')).toBeInTheDocument()
    })

    it('should close duration picker when clicking outside', async () => {
      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))
      fireEvent.click(screen.getByText('Rest Duration'))

      expect(screen.getByText('0:30')).toBeInTheDocument()

      // Click outside
      fireEvent.mouseDown(document.body)

      await waitFor(() => {
        expect(screen.queryByText('0:30')).not.toBeInTheDocument()
      })
    })

    it('should format duration correctly', () => {
      const testCases = [
        { seconds: 5, expected: '0:05' },
        { seconds: 20, expected: '0:20' },
        { seconds: 30, expected: '0:30' },
        { seconds: 60, expected: '1:00' },
        { seconds: 90, expected: '1:30' },
        { seconds: 120, expected: '2:00' },
        { seconds: 180, expected: '3:00' },
        { seconds: 300, expected: '5:00' },
      ]

      testCases.forEach(({ seconds, expected }) => {
        localStorage.setItem('restDuration', seconds.toString())
        const { unmount } = render(<RestTimerSettings />)

        fireEvent.click(screen.getByLabelText('Timer settings'))
        expect(screen.getByText(expected)).toBeInTheDocument()

        unmount()
      })
    })
  })

  // Existing tests for sound and vibration should remain...
  describe('Sound Setting', () => {
    it('should toggle sound setting', async () => {
      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))

      const soundButton = screen.getByText('Sound').closest('button')
      fireEvent.click(soundButton!)

      await waitFor(() => {
        expect(localStorage.getItem('soundEnabled')).toBe('false')
      })
    })
  })

  describe('Vibration Setting', () => {
    it('should toggle vibration setting', async () => {
      // Start with vibration enabled (default is true)
      localStorage.setItem('vibrationEnabled', 'true')

      render(<RestTimerSettings />)

      fireEvent.click(screen.getByLabelText('Timer settings'))

      const vibrationButton = screen.getByText('Vibration').closest('button')
      fireEvent.click(vibrationButton!)

      await waitFor(() => {
        expect(localStorage.getItem('vibrationEnabled')).toBe('false')
      })
    })
  })
})
