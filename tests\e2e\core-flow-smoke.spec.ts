import { test, expect } from '@playwright/test'
import { mockLoginAPI } from './mocks/auth'
import { mockProgramAPI } from './mocks/program'
import { mockWorkoutAPI } from './mocks/workout'

// Check if dev server is running before tests
test.beforeAll(async ({ request }) => {
  try {
    const response = await request.get('http://localhost:3000', {
      timeout: 1000,
    })
    if (!response.ok()) {
      test.skip()
    }
  } catch (error) {
    // Skip silently if no server - keeps pre-commit fast
    test.skip()
  }
})

test.describe('Core Flow Smoke Test', () => {
  // Use mobile viewport for fast mobile-first testing
  test.use({
    viewport: { width: 390, height: 844 },
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  })

  test('should complete core workflow quickly', async ({ page }) => {
    // Set up all API mocks before navigation
    await mockLoginAPI(page)
    await mockProgramAPI(page)
    await mockWorkoutAPI(page)

    // Mock workout session creation
    await page.route('**/api/Workout/CreateWorkoutSession', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          WorkoutId: 123456,
          WorkoutTemplateId: 101,
          StartTime: new Date().toISOString(),
        }),
      })
    })

    // Mock exercise details
    await page.route('**/api/Exercise/GetExercise*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 217,
          Label: 'Barbell bench press',
          VideoUrl: 'https://example.com/video.mp4',
          LocalVideo: 'bb_bench_press.mp4',
          IsBodyweight: false,
          IsWeighted: true,
        }),
      })
    })

    // Mock user workout series (for sets data)
    await page.route('**/api/Workout/GetUserWorkoutSeries*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Series: [],
          LastLogDate: null,
        }),
      })
    })

    // Mock finish workout
    await page.route('**/api/Workout/FinishWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          workoutId: 123456,
          duration: 1200, // 20 minutes
          totalSets: 1,
          totalVolume: 1000,
        }),
      })
    })

    // 1. Navigate to login page
    await page.goto('/')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // 2. Quick login (no real API call due to mocks)
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('password123')
    await page.getByRole('button', { name: /Login/i }).click()

    // 3. Verify quick success screen and program page
    await expect(
      page.locator('[data-testid="quick-success-screen"]')
    ).toBeVisible()
    await expect(page).toHaveURL('/program', { timeout: 5000 })

    // 4. Quick check for program elements
    await expect(page.getByRole('heading', { level: 1 }).nth(1)).toBeVisible()
    await expect(page.getByTestId('animated-counter')).toBeVisible()

    // 5. Navigate to workout
    await page.getByRole('button', { name: /Continue to Workout/i }).click()
    await expect(page).toHaveURL('/workout', { timeout: 3000 })

    // 6. Start workout
    await page.getByRole('button', { name: /Start Workout/i }).click()
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/, { timeout: 3000 })

    // 7. Quick exercise completion (minimal interaction)
    const repsInput = page.locator('input[type="number"]').first()
    await repsInput.fill('10')

    const weightInput = page.locator('input[type="number"]').nth(1)
    await weightInput.fill('100')

    // 8. Save set
    await page.getByRole('button', { name: /Save Set|Save|Log Set/i }).click()

    // Handle RIR picker if it appears (quick selection)
    try {
      const rirPicker = page.locator('[data-testid="rir-picker"]')
      if (await rirPicker.isVisible({ timeout: 1000 })) {
        await page.getByText(/1-2 more|2-3 reps left/i).click()
      }
    } catch {
      // Continue if no RIR picker
    }

    // 9. Complete workout (navigate directly to save time)
    await page.goto('/workout/complete')
    await expect(page).toHaveURL('/workout/complete', { timeout: 2000 })

    // 10. Quick verification and finish
    await expect(
      page.getByRole('heading', { name: /Workout Complete|Great Job/i }).first()
    ).toBeVisible()
    await page.getByRole('button', { name: 'Finish Workout' }).click()

    // Final verification
    await expect(page).toHaveURL(/\/(program|workout)/, { timeout: 3000 })
  })
})
